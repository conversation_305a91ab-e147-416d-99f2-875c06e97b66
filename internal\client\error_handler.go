package client

import (
	"context"
	"fmt"
	"sync"
	"time"

	"server-monitor/internal/logger"
)

// ErrorLevel 错误级别
type ErrorLevel int

const (
	ErrorLevelInfo ErrorLevel = iota
	ErrorLevelWarning
	ErrorLevelError
	ErrorLevelCritical
	ErrorLevelFatal
)

// String 返回错误级别字符串
func (e ErrorLevel) String() string {
	switch e {
	case ErrorLevelInfo:
		return "信息"
	case ErrorLevelWarning:
		return "警告"
	case ErrorLevelError:
		return "错误"
	case ErrorLevelCritical:
		return "严重"
	case ErrorLevelFatal:
		return "致命"
	default:
		return "未知"
	}
}

// ErrorType 错误类型
type ErrorType string

const (
	ErrorTypeNetwork     ErrorType = "network"     // 网络错误
	ErrorTypeConfig      ErrorType = "config"      // 配置错误
	ErrorTypeMonitor     ErrorType = "monitor"     // 监控错误
	ErrorTypeService     ErrorType = "service"     // 服务错误
	ErrorTypeSystem      ErrorType = "system"      // 系统错误
	ErrorTypeValidation  ErrorType = "validation"  // 验证错误
	ErrorTypeTimeout     ErrorType = "timeout"     // 超时错误
	ErrorTypePermission  ErrorType = "permission"  // 权限错误
	ErrorTypeResource    ErrorType = "resource"    // 资源错误
	ErrorTypeUnknown     ErrorType = "unknown"     // 未知错误
)

// ClientError 客户端错误
type ClientError struct {
	ID          string      `json:"id"`           // 错误ID
	Type        ErrorType   `json:"type"`         // 错误类型
	Level       ErrorLevel  `json:"level"`        // 错误级别
	Message     string      `json:"message"`      // 错误消息
	Details     string      `json:"details"`      // 错误详情
	Timestamp   time.Time   `json:"timestamp"`    // 发生时间
	Component   string      `json:"component"`    // 组件名称
	Context     interface{} `json:"context"`      // 上下文信息
	Cause       error       `json:"cause"`        // 原始错误
	Recoverable bool        `json:"recoverable"`  // 是否可恢复
	RetryCount  int         `json:"retry_count"`  // 重试次数
}

// Error 实现error接口
func (e *ClientError) Error() string {
	return fmt.Sprintf("[%s] %s: %s", e.Type, e.Level.String(), e.Message)
}

// ErrorHandler 错误处理器接口
type ErrorHandler interface {
	HandleError(err *ClientError) error
}

// ErrorRecovery 错误恢复策略接口
type ErrorRecovery interface {
	CanRecover(err *ClientError) bool
	Recover(ctx context.Context, err *ClientError) error
}

// ErrorReporter 错误报告器接口
type ErrorReporter interface {
	ReportError(err *ClientError) error
}

// ErrorManager 错误管理器
type ErrorManager struct {
	logger    *logger.Logger
	
	// 错误处理
	handlers  []ErrorHandler
	recovers  []ErrorRecovery
	reporters []ErrorReporter
	
	// 错误存储
	errors    []*ClientError
	errorsMu  sync.RWMutex
	
	// 统计信息
	stats     *ErrorStats
	statsMu   sync.RWMutex
	
	// 配置
	maxErrors     int
	retryAttempts int
	retryDelay    time.Duration
}

// ErrorStats 错误统计
type ErrorStats struct {
	TotalErrors     int64                    `json:"total_errors"`
	ErrorsByType    map[ErrorType]int64      `json:"errors_by_type"`
	ErrorsByLevel   map[ErrorLevel]int64     `json:"errors_by_level"`
	RecoveredErrors int64                    `json:"recovered_errors"`
	ReportedErrors  int64                    `json:"reported_errors"`
	LastErrorTime   time.Time                `json:"last_error_time"`
	LastErrorType   ErrorType                `json:"last_error_type"`
}

// NewErrorManager 创建错误管理器
func NewErrorManager(logger *logger.Logger) *ErrorManager {
	return &ErrorManager{
		logger:        logger,
		handlers:      make([]ErrorHandler, 0),
		recovers:      make([]ErrorRecovery, 0),
		reporters:     make([]ErrorReporter, 0),
		errors:        make([]*ClientError, 0),
		maxErrors:     1000,
		retryAttempts: 3,
		retryDelay:    5 * time.Second,
		stats: &ErrorStats{
			ErrorsByType:  make(map[ErrorType]int64),
			ErrorsByLevel: make(map[ErrorLevel]int64),
		},
	}
}

// AddHandler 添加错误处理器
func (em *ErrorManager) AddHandler(handler ErrorHandler) {
	em.handlers = append(em.handlers, handler)
}

// AddRecovery 添加错误恢复策略
func (em *ErrorManager) AddRecovery(recovery ErrorRecovery) {
	em.recovers = append(em.recovers, recovery)
}

// AddReporter 添加错误报告器
func (em *ErrorManager) AddReporter(reporter ErrorReporter) {
	em.reporters = append(em.reporters, reporter)
}

// HandleError 处理错误
func (em *ErrorManager) HandleError(ctx context.Context, err error, errorType ErrorType, level ErrorLevel, component string) error {
	clientError := &ClientError{
		ID:          em.generateErrorID(),
		Type:        errorType,
		Level:       level,
		Message:     err.Error(),
		Timestamp:   time.Now(),
		Component:   component,
		Cause:       err,
		Recoverable: em.isRecoverable(errorType, level),
		RetryCount:  0,
	}
	
	return em.processError(ctx, clientError)
}

// HandleClientError 处理客户端错误
func (em *ErrorManager) HandleClientError(ctx context.Context, err *ClientError) error {
	return em.processError(ctx, err)
}

// processError 处理错误
func (em *ErrorManager) processError(ctx context.Context, err *ClientError) error {
	// 记录错误
	em.recordError(err)
	
	// 更新统计
	em.updateStats(err)
	
	// 记录日志
	em.logError(err)
	
	// 尝试恢复
	if err.Recoverable && em.attemptRecovery(ctx, err) {
		em.logger.Info("错误恢复成功: %s", err.Message)
		em.statsMu.Lock()
		em.stats.RecoveredErrors++
		em.statsMu.Unlock()
		return nil
	}
	
	// 执行处理器
	for _, handler := range em.handlers {
		if handlerErr := handler.HandleError(err); handlerErr != nil {
			em.logger.Error("错误处理器执行失败: %v", handlerErr)
		}
	}
	
	// 报告错误
	em.reportError(err)
	
	// 根据错误级别决定是否返回错误
	if err.Level >= ErrorLevelError {
		return fmt.Errorf("客户端错误: %w", err.Cause)
	}
	
	return nil
}

// attemptRecovery 尝试错误恢复
func (em *ErrorManager) attemptRecovery(ctx context.Context, err *ClientError) bool {
	for _, recovery := range em.recovers {
		if recovery.CanRecover(err) {
			for attempt := 0; attempt < em.retryAttempts; attempt++ {
				err.RetryCount = attempt + 1
				
				if attempt > 0 {
					select {
					case <-ctx.Done():
						return false
					case <-time.After(em.retryDelay):
					}
				}
				
				if recoveryErr := recovery.Recover(ctx, err); recoveryErr == nil {
					return true
				} else {
					em.logger.Warn("错误恢复尝试 %d 失败: %v", attempt+1, recoveryErr)
				}
			}
		}
	}
	
	return false
}

// reportError 报告错误
func (em *ErrorManager) reportError(err *ClientError) {
	for _, reporter := range em.reporters {
		go func(r ErrorReporter) {
			if reportErr := r.ReportError(err); reportErr != nil {
				em.logger.Error("错误报告失败: %v", reportErr)
			} else {
				em.statsMu.Lock()
				em.stats.ReportedErrors++
				em.statsMu.Unlock()
			}
		}(reporter)
	}
}

// recordError 记录错误
func (em *ErrorManager) recordError(err *ClientError) {
	em.errorsMu.Lock()
	defer em.errorsMu.Unlock()
	
	em.errors = append(em.errors, err)
	
	// 限制错误数量
	if len(em.errors) > em.maxErrors {
		em.errors = em.errors[len(em.errors)-em.maxErrors:]
	}
}

// updateStats 更新统计信息
func (em *ErrorManager) updateStats(err *ClientError) {
	em.statsMu.Lock()
	defer em.statsMu.Unlock()
	
	em.stats.TotalErrors++
	em.stats.ErrorsByType[err.Type]++
	em.stats.ErrorsByLevel[err.Level]++
	em.stats.LastErrorTime = err.Timestamp
	em.stats.LastErrorType = err.Type
}

// logError 记录错误日志
func (em *ErrorManager) logError(err *ClientError) {
	switch err.Level {
	case ErrorLevelInfo:
		em.logger.Info("[%s] %s: %s", err.Component, err.Type, err.Message)
	case ErrorLevelWarning:
		em.logger.Warn("[%s] %s: %s", err.Component, err.Type, err.Message)
	case ErrorLevelError:
		em.logger.Error("[%s] %s: %s", err.Component, err.Type, err.Message)
	case ErrorLevelCritical:
		em.logger.Error("[%s] 严重错误 %s: %s", err.Component, err.Type, err.Message)
	case ErrorLevelFatal:
		em.logger.Fatal("[%s] 致命错误 %s: %s", err.Component, err.Type, err.Message)
	}
}

// GetErrors 获取错误列表
func (em *ErrorManager) GetErrors(limit int) []*ClientError {
	em.errorsMu.RLock()
	defer em.errorsMu.RUnlock()
	
	if limit <= 0 || limit > len(em.errors) {
		limit = len(em.errors)
	}
	
	start := len(em.errors) - limit
	if start < 0 {
		start = 0
	}
	
	errors := make([]*ClientError, limit)
	copy(errors, em.errors[start:])
	
	return errors
}

// GetStats 获取统计信息
func (em *ErrorManager) GetStats() *ErrorStats {
	em.statsMu.RLock()
	defer em.statsMu.RUnlock()
	
	stats := *em.stats
	return &stats
}

// ClearErrors 清空错误记录
func (em *ErrorManager) ClearErrors() {
	em.errorsMu.Lock()
	defer em.errorsMu.Unlock()
	
	em.errors = make([]*ClientError, 0)
}

// generateErrorID 生成错误ID
func (em *ErrorManager) generateErrorID() string {
	return fmt.Sprintf("err_%d", time.Now().UnixNano())
}

// isRecoverable 判断错误是否可恢复
func (em *ErrorManager) isRecoverable(errorType ErrorType, level ErrorLevel) bool {
	// 致命错误不可恢复
	if level == ErrorLevelFatal {
		return false
	}
	
	// 根据错误类型判断
	switch errorType {
	case ErrorTypeNetwork, ErrorTypeTimeout, ErrorTypeResource:
		return true
	case ErrorTypeConfig, ErrorTypeValidation, ErrorTypePermission:
		return false
	default:
		return level <= ErrorLevelWarning
	}
}

// DefaultErrorHandler 默认错误处理器
type DefaultErrorHandler struct {
	logger *logger.Logger
}

// NewDefaultErrorHandler 创建默认错误处理器
func NewDefaultErrorHandler(logger *logger.Logger) *DefaultErrorHandler {
	return &DefaultErrorHandler{logger: logger}
}

// HandleError 处理错误
func (h *DefaultErrorHandler) HandleError(err *ClientError) error {
	h.logger.Debug("默认错误处理器处理错误: %s", err.Message)
	return nil
}

// NetworkErrorRecovery 网络错误恢复策略
type NetworkErrorRecovery struct {
	logger *logger.Logger
}

// NewNetworkErrorRecovery 创建网络错误恢复策略
func NewNetworkErrorRecovery(logger *logger.Logger) *NetworkErrorRecovery {
	return &NetworkErrorRecovery{logger: logger}
}

// CanRecover 判断是否可以恢复
func (r *NetworkErrorRecovery) CanRecover(err *ClientError) bool {
	return err.Type == ErrorTypeNetwork || err.Type == ErrorTypeTimeout
}

// Recover 执行恢复
func (r *NetworkErrorRecovery) Recover(ctx context.Context, err *ClientError) error {
	r.logger.Info("尝试恢复网络错误: %s", err.Message)
	
	// 这里可以实现具体的网络恢复逻辑
	// 例如：重新连接、重置连接池等
	
	return nil
}
