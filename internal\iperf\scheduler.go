package iperf

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/models"
)

// Scheduler iPerf3测试调度器
type Scheduler struct {
	config     *config.Config
	repository database.Repository
	running    bool
	mu         sync.RWMutex
	stopCh     chan struct{}
	wg         sync.WaitGroup
}

// NewScheduler 创建新的测试调度器
func NewScheduler(cfg *config.Config, repo database.Repository) *Scheduler {
	return &Scheduler{
		config:     cfg,
		repository: repo,
		running:    false,
		stopCh:     make(chan struct{}),
	}
}

// Start 启动调度器
func (s *Scheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("调度器已经在运行")
	}

	s.running = true
	s.stopCh = make(chan struct{})

	// 启动主调度循环
	s.wg.Add(1)
	go s.scheduleLoop()

	log.Println("iPerf3测试调度器已启动")
	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return
	}

	s.running = false
	close(s.stopCh)
	s.wg.Wait()

	log.Println("iPerf3测试调度器已停止")
}

// IsRunning 检查调度器是否运行中
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// scheduleLoop 主调度循环
func (s *Scheduler) scheduleLoop() {
	defer s.wg.Done()

	// 创建定时器
	ticker := time.NewTicker(time.Duration(s.config.IPerf.TestInterval) * time.Minute)
	defer ticker.Stop()

	log.Printf("调度器循环启动，测试间隔: %d分钟", s.config.IPerf.TestInterval)

	for {
		select {
		case <-s.stopCh:
			log.Println("调度器循环收到停止信号")
			return
		case <-ticker.C:
			s.runScheduledTests()
		}
	}
}

// runScheduledTests 运行计划的测试
func (s *Scheduler) runScheduledTests() {
	log.Println("开始执行计划测试")

	// 获取活跃的服务器列表
	servers, err := s.repository.ListServers(true)
	if err != nil {
		log.Printf("获取服务器列表失败: %v", err)
		return
	}

	if len(servers) == 0 {
		log.Println("没有活跃的服务器，跳过测试")
		return
	}

	log.Printf("找到 %d 个活跃服务器，开始测试", len(servers))

	// 并发执行测试
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, s.config.IPerf.MaxConcurrentTests)

	for _, server := range servers {
		wg.Add(1)
		go func(srv *models.Server) { // Change to models.Server
			defer wg.Done()

			// 限制并发数
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			s.testServer(srv)
		}(server)
	}

	wg.Wait()
	log.Println("所有计划测试完成")
}

// testServer 测试单个服务器
func (s *Scheduler) testServer(server *models.Server) { // Change to models.Server
	log.Printf("开始测试服务器: %s (%s)", server.Name, server.IPAddress) // Change IP to IPAddress and remove Port

	// 创建测试上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(),
		time.Duration(s.config.IPerf.TestTimeout)*time.Second)
	defer cancel()

	// 执行上传测试
	uploadResult, err := s.runIPerf3Test(ctx, server, "upload")
	if err != nil {
		log.Printf("服务器 %s 上传测试失败: %v", server.Name, err)
		s.saveFailedResult(server, "upload", err)
		return
	}

	// 执行下载测试
	downloadResult, err := s.runIPerf3Test(ctx, server, "download")
	if err != nil {
		log.Printf("服务器 %s 下载测试失败: %v", server.Name, err)
		s.saveFailedResult(server, "download", err)
		return
	}

	// 保存测试结果
	s.saveTestResults(server, uploadResult, downloadResult)
	log.Printf("服务器 %s 测试完成", server.Name)
}

// runIPerf3Test 执行iPerf3测试
func (s *Scheduler) runIPerf3Test(ctx context.Context, server *models.Server, testType string) (*TestResult, error) { // Change to models.Server
	// 创建iPerf3客户端
	client := NewClient(&ClientConfig{
		ServerHost: server.IPAddress, // Change IP to IPAddress
		Duration:   s.config.IPerf.TestDuration,
		Parallel:   s.config.IPerf.ParallelStreams,
		Reverse:    testType == "download",
	})

	// 执行测试
	result, err := client.Run(ctx)
	if err != nil {
		return nil, fmt.Errorf("iPerf3测试执行失败: %w", err)
	}

	return result, nil
}

// saveTestResults 保存测试结果
func (s *Scheduler) saveTestResults(server *models.Server, uploadResult, downloadResult *TestResult) { // Change to models.Server
	// 计算当前小时
	now := time.Now()
	testHour := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())

	// 创建小时结果记录
	hourlyResult := &database.HourlyResult{
		ServerID:      server.ID,
		TestHour:      testHour,
		UploadSpeed:   uploadResult.Bandwidth,
		DownloadSpeed: downloadResult.Bandwidth,
		Latency:       (uploadResult.Latency + downloadResult.Latency) / 2,
		Jitter:        (uploadResult.Jitter + downloadResult.Jitter) / 2,
		PacketLoss:    (uploadResult.PacketLoss + downloadResult.PacketLoss) / 2,
		TestDuration:  s.config.IPerf.TestDuration,
		TestType:      "both",
		Status:        "success",
	}

	// 尝试获取现有记录
	existing, err := s.repository.GetHourlyResult(server.ID, testHour)
	if err == nil && existing != nil {
		// 更新现有记录
		existing.UploadSpeed = hourlyResult.UploadSpeed
		existing.DownloadSpeed = hourlyResult.DownloadSpeed
		existing.Latency = hourlyResult.Latency
		existing.Jitter = hourlyResult.Jitter
		existing.PacketLoss = hourlyResult.PacketLoss
		existing.Status = "success"
		existing.ErrorMessage = ""

		err = s.repository.UpdateHourlyResult(existing)
		if err != nil {
			log.Printf("更新测试结果失败: %v", err)
		}
	} else {
		// 创建新记录
		err = s.repository.CreateHourlyResult(hourlyResult)
		if err != nil {
			log.Printf("保存测试结果失败: %v", err)
		}
	}

	log.Printf("服务器 %s 测试结果已保存: 上传=%.2fMbps, 下载=%.2fMbps", 
		server.Name, uploadResult.Bandwidth, downloadResult.Bandwidth)
}

// saveFailedResult 保存失败的测试结果
func (s *Scheduler) saveFailedResult(server *models.Server, testType string, testErr error) { // Change to models.Server
	now := time.Now()
	testHour := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())

	hourlyResult := &database.HourlyResult{
		ServerID:     server.ID,
		TestHour:     testHour,
		TestDuration: s.config.IPerf.TestDuration,
		TestType:     testType,
		Status:       "failed",
		ErrorMessage: testErr.Error(),
	}

	// 尝试获取现有记录
	existing, err := s.repository.GetHourlyResult(server.ID, testHour)
	if err == nil && existing != nil {
		// 更新现有记录的错误信息
		existing.Status = "failed"
		existing.ErrorMessage = testErr.Error()
		s.repository.UpdateHourlyResult(existing)
	} else {
		// 创建新的失败记录
		s.repository.CreateHourlyResult(hourlyResult)
	}

	log.Printf("服务器 %s %s测试失败结果已保存", server.Name, testType)
}

// GetTestStats 获取测试统计信息
func (s *Scheduler) GetTestStats() (*TestStats, error) {
	// 获取最近24小时的测试结果
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)

	var totalTests, successTests, failedTests int
	var totalBandwidth float64

	// 获取所有服务器
	servers, err := s.repository.ListServers(false)
	if err != nil {
		return nil, err
	}

	for _, server := range servers {
		results, err := s.repository.ListHourlyResults(uint(server.ID), startTime, endTime) // Cast server.ID to uint
		if err != nil {
			continue
		}

		for _, result := range results {
			totalTests++
			if result.Status == "success" {
				successTests++
				totalBandwidth += (result.UploadSpeed + result.DownloadSpeed) / 2
			} else {
				failedTests++
			}
		}
	}

	var avgBandwidth float64
	if successTests > 0 {
		avgBandwidth = totalBandwidth / float64(successTests)
	}

	var successRate float64
	if totalTests > 0 {
		successRate = float64(successTests) / float64(totalTests) * 100
	}

	return &TestStats{
		TotalTests:     totalTests,
		SuccessTests:   successTests,
		FailedTests:    failedTests,
		SuccessRate:    successRate,
		AvgBandwidth:   avgBandwidth,
		LastUpdateTime: time.Now(),
	}, nil
}

// TestStats 测试统计信息
type TestStats struct {
	TotalTests     int       `json:"total_tests"`     // 总测试次数
	SuccessTests   int       `json:"success_tests"`   // 成功测试次数
	FailedTests    int       `json:"failed_tests"`    // 失败测试次数
	SuccessRate    float64   `json:"success_rate"`    // 成功率(%)
	AvgBandwidth   float64   `json:"avg_bandwidth"`   // 平均带宽(Mbps)
	LastUpdateTime time.Time `json:"last_update"`     // 最后更新时间
}
