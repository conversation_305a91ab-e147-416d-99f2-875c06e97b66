package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"server-monitor/internal/client"
	"server-monitor/internal/logger"
)

func main() {
	var (
		configFile = flag.String("config", "./configs/client.yaml", "客户端配置文件路径")
		mode       = flag.String("mode", "run", "运行模式: run(运行), test(测试), status(状态)")
		daemon     = flag.Bool("daemon", false, "是否以守护进程模式运行")
		verbose    = flag.Bool("verbose", false, "是否显示详细日志")
	)
	flag.Parse()

	fmt.Printf("服务器监控客户端主程序\n")
	fmt.Printf("配置文件: %s\n", *configFile)
	fmt.Printf("运行模式: %s\n", *mode)
	fmt.Printf("守护进程: %v\n", *daemon)
	fmt.Println("---")

	switch *mode {
	case "run":
		runClient(*configFile, *daemon, *verbose)
	case "test":
		testClient(*configFile)
	case "status":
		showStatus(*configFile)
	default:
		log.Fatalf("未知运行模式: %s", *mode)
	}
}

// runClient 运行客户端
func runClient(configFile string, daemon, verbose bool) {
	// 创建主客户端
	mainClient, err := client.NewMainClient(configFile)
	if err != nil {
		log.Fatalf("创建主客户端失败: %v", err)
	}

	// 设置日志级别
	if verbose {
		// 这里可以设置更详细的日志级别
		fmt.Println("启用详细日志模式")
	}

	// 启动客户端
	if err := mainClient.Start(); err != nil {
		log.Fatalf("启动客户端失败: %v", err)
	}

	fmt.Println("客户端启动成功")

	// 如果不是守护进程模式，显示状态信息
	if !daemon {
		go showRuntimeStatus(mainClient)
	}

	// 等待信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	if daemon {
		fmt.Println("客户端以守护进程模式运行...")
	} else {
		fmt.Println("客户端运行中，按 Ctrl+C 停止...")
	}

	// 等待停止信号
	<-sigCh
	fmt.Println("\n收到停止信号，正在停止客户端...")

	// 停止客户端
	if err := mainClient.Stop(); err != nil {
		log.Printf("停止客户端失败: %v", err)
	}

	fmt.Println("客户端已停止")
}

// testClient 测试客户端
func testClient(configFile string) {
	fmt.Println("=== 客户端功能测试 ===")

	// 测试配置管理
	fmt.Println("1. 测试配置管理...")
	testConfigManager(configFile)

	// 测试生命周期管理
	fmt.Println("\n2. 测试生命周期管理...")
	testLifecycleManager()

	// 测试错误处理
	fmt.Println("\n3. 测试错误处理...")
	testErrorHandler()

	// 测试主客户端
	fmt.Println("\n4. 测试主客户端...")
	testMainClient(configFile)

	fmt.Println("\n✅ 所有测试完成")
}

// testConfigManager 测试配置管理器
func testConfigManager(configFile string) {
	configManager := client.NewConfigManager(configFile)

	// 加载配置
	if err := configManager.Load(); err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	config := configManager.Get()
	if config == nil {
		fmt.Println("❌ 获取配置失败")
		return
	}

	fmt.Printf("✅ 配置加载成功: %s v%s\n", config.Name, config.Version)
	fmt.Printf("   服务器: %s\n", configManager.GetServerURL())
	fmt.Printf("   监控间隔: %v\n", configManager.GetMonitorInterval())
	fmt.Printf("   上报间隔: %v\n", configManager.GetReportInterval())
}

// testLifecycleManager 测试生命周期管理器
func testLifecycleManager() {
	// 创建日志器
	logConfig := &logger.Config{
		Level:  logger.LevelDebug,
		Format: "text",
		Output: "stdout",
	}
	testLogger, err := logger.NewLogger(logConfig)
	if err != nil {
		fmt.Printf("❌ 创建日志器失败: %v\n", err)
		return
	}
	defer testLogger.Close()

	// 创建生命周期管理器
	lifecycle := client.NewLifecycleManager("test-client", testLogger)

	// 添加测试组件
	testComponent := &TestComponent{name: "test-component"}
	lifecycle.AddComponent(testComponent)

	// 启动
	if err := lifecycle.Start(); err != nil {
		fmt.Printf("❌ 启动生命周期管理器失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 生命周期管理器启动成功，状态: %s\n", lifecycle.GetPhase().String())

	// 等待一下
	time.Sleep(100 * time.Millisecond)

	// 停止
	if err := lifecycle.Stop(); err != nil {
		fmt.Printf("❌ 停止生命周期管理器失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 生命周期管理器停止成功，运行时间: %v\n", lifecycle.GetUptime())
}

// testErrorHandler 测试错误处理器
func testErrorHandler() {
	// 创建日志器
	logConfig := &logger.Config{
		Level:  logger.LevelDebug,
		Format: "text",
		Output: "stdout",
	}
	testLogger, err := logger.NewLogger(logConfig)
	if err != nil {
		fmt.Printf("❌ 创建日志器失败: %v\n", err)
		return
	}
	defer testLogger.Close()

	// 创建错误管理器
	errorManager := client.NewErrorManager(testLogger)

	// 添加默认处理器
	errorManager.AddHandler(client.NewDefaultErrorHandler(testLogger))
	errorManager.AddRecovery(client.NewNetworkErrorRecovery(testLogger))

	ctx := context.Background()

	// 测试不同类型的错误
	testErrors := []struct {
		err       error
		errorType client.ErrorType
		level     client.ErrorLevel
		component string
	}{
		{fmt.Errorf("网络连接失败"), client.ErrorTypeNetwork, client.ErrorLevelWarning, "network"},
		{fmt.Errorf("配置文件格式错误"), client.ErrorTypeConfig, client.ErrorLevelError, "config"},
		{fmt.Errorf("监控数据获取超时"), client.ErrorTypeTimeout, client.ErrorLevelWarning, "monitor"},
	}

	for i, testErr := range testErrors {
		if err := errorManager.HandleError(ctx, testErr.err, testErr.errorType, testErr.level, testErr.component); err != nil {
			fmt.Printf("❌ 错误处理 %d 失败: %v\n", i+1, err)
		} else {
			fmt.Printf("✅ 错误处理 %d 成功: %s\n", i+1, testErr.err.Error())
		}
	}

	// 显示统计信息
	stats := errorManager.GetStats()
	fmt.Printf("   错误统计: 总计 %d, 恢复 %d, 报告 %d\n", 
		stats.TotalErrors, stats.RecoveredErrors, stats.ReportedErrors)
}

// testMainClient 测试主客户端
func testMainClient(configFile string) {
	// 创建主客户端
	mainClient, err := client.NewMainClient(configFile)
	if err != nil {
		fmt.Printf("❌ 创建主客户端失败: %v\n", err)
		return
	}

	// 启动客户端
	if err := mainClient.Start(); err != nil {
		fmt.Printf("❌ 启动主客户端失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 主客户端启动成功，状态: %s\n", mainClient.GetState().String())

	// 等待一下让客户端运行
	time.Sleep(2 * time.Second)

	// 获取统计信息
	stats := mainClient.GetStats()
	fmt.Printf("   运行时间: %v\n", stats.Uptime)
	fmt.Printf("   监控周期: %d\n", stats.MonitorCycles)

	// 停止客户端
	if err := mainClient.Stop(); err != nil {
		fmt.Printf("❌ 停止主客户端失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 主客户端停止成功\n")
}

// showStatus 显示状态
func showStatus(configFile string) {
	fmt.Println("=== 客户端状态信息 ===")

	// 这里可以实现从运行中的客户端获取状态信息
	// 例如通过文件、管道或网络接口

	fmt.Println("状态查询功能待实现")
}

// showRuntimeStatus 显示运行时状态
func showRuntimeStatus(mainClient *client.MainClient) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			stats := mainClient.GetStats()
			healthCheck := mainClient.HealthCheck()

			fmt.Printf("\n=== 运行状态 (%s) ===\n", time.Now().Format("15:04:05"))
			fmt.Printf("状态: %s\n", mainClient.GetState().String())
			fmt.Printf("运行时间: %v\n", stats.Uptime)
			fmt.Printf("监控周期: %d\n", stats.MonitorCycles)
			fmt.Printf("上报成功: %d\n", stats.ReportsSent)
			fmt.Printf("上报失败: %d\n", stats.ReportsFailed)
			fmt.Printf("错误数量: %d\n", stats.ErrorCount)

			if lastError := mainClient.GetLastError(); lastError != nil {
				fmt.Printf("最后错误: %v\n", lastError)
			}

			// 显示健康检查信息
			if healthData, err := json.MarshalIndent(healthCheck, "", "  "); err == nil {
				fmt.Printf("健康检查: %s\n", string(healthData))
			}
		}
	}
}

// TestComponent 测试组件
type TestComponent struct {
	name    string
	running bool
}

func (c *TestComponent) Name() string {
	return c.name
}

func (c *TestComponent) Start(ctx context.Context) error {
	c.running = true
	return nil
}

func (c *TestComponent) Stop(ctx context.Context) error {
	c.running = false
	return nil
}

func (c *TestComponent) IsHealthy() bool {
	return c.running
}
