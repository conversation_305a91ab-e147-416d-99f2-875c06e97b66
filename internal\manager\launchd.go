// +build darwin

package manager

import (
	"context"
	"fmt"
	"strings"
)

// LaunchdManager macOS launchd服务管理器
type LaunchdManager struct{}

// NewLaunchdManager 创建launchd管理器
func NewLaunchdManager() *LaunchdManager {
	return &LaunchdManager{}
}

// ListServices 列出所有launchd服务
func (l *LaunchdManager) ListServices(ctx context.Context) ([]*Service, error) {
	// 获取用户服务
	userOutput, _ := executeCommand(ctx, "launchctl", "list")
	
	var services []*Service
	
	// 解析用户服务
	services = append(services, l.parseServices(userOutput, "user")...)
	
	// 尝试获取系统服务（需要sudo权限）
	systemOutput, err := executeCommand(ctx, "sudo", "launchctl", "list")
	if err == nil {
		services = append(services, l.parseServices(systemOutput, "system")...)
	}

	return services, nil
}

// parseServices 解析launchctl list输出
func (l *LaunchdManager) parseServices(output, serviceType string) []*Service {
	var services []*Service
	lines := strings.Split(output, "\n")
	
	for i, line := range lines {
		if i == 0 || strings.TrimSpace(line) == "" {
			continue // 跳过标题行和空行
		}
		
		fields := strings.Fields(line)
		if len(fields) < 3 {
			continue
		}
		
		pid := fields[0]
		status := fields[1]
		name := fields[2]
		
		service := &Service{
			Name:        name,
			DisplayName: name,
			Status:      parseLaunchdStatus(status, pid),
			Type:        "launchd",
			Description: fmt.Sprintf("launchd服务 (%s)", serviceType),
		}
		
		services = append(services, service)
	}
	
	return services
}

// GetService 获取指定launchd服务的详细信息
func (l *LaunchdManager) GetService(ctx context.Context, name string) (*Service, error) {
	if !l.ServiceExists(ctx, name) {
		return nil, fmt.Errorf("服务不存在: %s", name)
	}

	service := &Service{
		Name:        name,
		DisplayName: name,
		Type:        "launchd",
	}

	// 获取服务状态
	output, err := executeCommand(ctx, "launchctl", "list", name)
	if err != nil {
		return nil, err
	}

	// 解析输出获取详细信息
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.Contains(line, "PID") {
			fields := strings.Fields(line)
			if len(fields) >= 3 {
				service.Status = parseLaunchdStatus(fields[2], fields[2])
			}
		}
	}

	return service, nil
}

// StartService 启动launchd服务
func (l *LaunchdManager) StartService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "launchctl", "start", name)
	if err != nil {
		return fmt.Errorf("启动launchd服务失败: %w", err)
	}
	return nil
}

// StopService 停止launchd服务
func (l *LaunchdManager) StopService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "launchctl", "stop", name)
	if err != nil {
		return fmt.Errorf("停止launchd服务失败: %w", err)
	}
	return nil
}

// RestartService 重启launchd服务
func (l *LaunchdManager) RestartService(ctx context.Context, name string) error {
	// launchctl没有直接的restart命令，需要先stop再start
	if err := l.StopService(ctx, name); err != nil {
		return err
	}
	return l.StartService(ctx, name)
}

// EnableService 启用launchd服务
func (l *LaunchdManager) EnableService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "launchctl", "enable", name)
	if err != nil {
		return fmt.Errorf("启用launchd服务失败: %w", err)
	}
	return nil
}

// DisableService 禁用launchd服务
func (l *LaunchdManager) DisableService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "launchctl", "disable", name)
	if err != nil {
		return fmt.Errorf("禁用launchd服务失败: %w", err)
	}
	return nil
}

// ServiceExists 检查launchd服务是否存在
func (l *LaunchdManager) ServiceExists(ctx context.Context, name string) bool {
	_, err := executeCommand(ctx, "launchctl", "list", name)
	return err == nil
}

// GetServiceLogs 获取launchd服务日志
func (l *LaunchdManager) GetServiceLogs(ctx context.Context, name string, lines int) ([]string, error) {
	// macOS使用log命令查看日志
	output, err := executeCommand(ctx, "log", "show", "--predicate", fmt.Sprintf("process == '%s'", name), "--last", "1h", "--style", "syslog")
	if err != nil {
		return nil, fmt.Errorf("获取launchd服务日志失败: %w", err)
	}

	logLines := strings.Split(output, "\n")
	
	// 限制行数
	if len(logLines) > lines {
		logLines = logLines[len(logLines)-lines:]
	}
	
	// 过滤空行
	var result []string
	for _, line := range logLines {
		if strings.TrimSpace(line) != "" {
			result = append(result, line)
		}
	}

	return result, nil
}

// parseLaunchdStatus 解析launchd服务状态
func parseLaunchdStatus(status, pid string) ServiceStatus {
	status = strings.ToLower(strings.TrimSpace(status))
	pid = strings.TrimSpace(pid)
	
	// 如果有PID且不是"-"，说明服务在运行
	if pid != "-" && pid != "" {
		return StatusRunning
	}
	
	// 根据状态码判断
	switch status {
	case "0":
		return StatusStopped
	case "-":
		return StatusStopped
	default:
		if status != "" && status != "-" {
			return StatusRunning
		}
		return StatusUnknown
	}
}

// LoadService 加载launchd服务
func (l *LaunchdManager) LoadService(ctx context.Context, plistPath string) error {
	_, err := executeCommand(ctx, "launchctl", "load", plistPath)
	if err != nil {
		return fmt.Errorf("加载launchd服务失败: %w", err)
	}
	return nil
}

// UnloadService 卸载launchd服务
func (l *LaunchdManager) UnloadService(ctx context.Context, plistPath string) error {
	_, err := executeCommand(ctx, "launchctl", "unload", plistPath)
	if err != nil {
		return fmt.Errorf("卸载launchd服务失败: %w", err)
	}
	return nil
}

// GetServiceInfo 获取launchd服务详细信息
func (l *LaunchdManager) GetServiceInfo(ctx context.Context, name string) (map[string]interface{}, error) {
	output, err := executeCommand(ctx, "launchctl", "list", name)
	if err != nil {
		return nil, fmt.Errorf("获取launchd服务信息失败: %w", err)
	}

	info := make(map[string]interface{})
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		if strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])
				info[key] = value
			}
		}
	}

	return info, nil
}
