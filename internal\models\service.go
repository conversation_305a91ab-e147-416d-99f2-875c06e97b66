package models

import (
	"time"

	"gorm.io/gorm"
)

// Service 表示服务表 (Service represents the service table)
type Service struct {
	gorm.Model
	ServerID    uint            `gorm:"not null"`
	Server      Server          `gorm:"foreignKey:ServerID"` // 属于服务器 (Belongs to server)
	Name        string          `gorm:"not null"`
	Type        string          `gorm:"size:20;not null"` // 服务类型：supervisord/systemd/docker (Service type: supervisord/systemd/docker)
	DisplayName string          `gorm:"size:200"`
	Description string          `gorm:"type:text"`
	AutoStart   bool            `gorm:"default:false"`
	IsMonitored bool            `gorm:"default:true"`
	Statuses    []ServiceStatus `gorm:"foreignKey:ServiceID"` // 拥有多个 ServiceStatus (Has multiple ServiceStatus)
}

// ServiceStatus 表示服务状态表 (ServiceStatus represents the service status table)
type ServiceStatus struct {
	gorm.Model
	ServiceID    uint   `gorm:"not null"`
	Status       string `gorm:"size:20;not null"` // 服务状态：running/stopped/failed (Service status: running/stopped/failed)
	PID          int
	Uptime       string `gorm:"size:50"`
	MemoryUsage  int
	CPUUsage     float64
	RestartCount int       `gorm:"default:0"`
	ErrorMessage string    `gorm:"type:text"`
	Timestamp    time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}
