{{template "layouts/base.html" .}}

{{define "content"}}
<div class="px-4 py-6 sm:px-0">
    <!-- 页面标题 -->
    <div class="mb-8">
        <h2 class="text-3xl font-bold text-gray-900">服务器监控仪表板</h2>
        <p class="mt-2 text-gray-600">实时监控12台服务器的CPU和内存使用情况</p>
    </div>

    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">在线服务器</p>
                    <p class="text-2xl font-bold text-gray-900" id="online-count">{{.activeServers}}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">总服务器</p>
                    <p class="text-2xl font-bold text-gray-900">{{.totalServers}}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">警告</p>
                    <p class="text-2xl font-bold text-gray-900" id="warning-count">0</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">平均CPU</p>
                    <p class="text-2xl font-bold text-gray-900" id="avg-cpu">0%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务器网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="servers-grid">
        <!-- 服务器卡片将通过JavaScript动态生成 -->
    </div>

    <!-- 加载状态 -->
    <div id="loading-indicator" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-gray-600">正在加载服务器数据...</span>
    </div>

    <!-- 错误状态 -->
    <div id="error-indicator" class="hidden bg-red-50 border border-red-200 rounded-md p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">连接错误</h3>
                <div class="mt-2 text-sm text-red-700">
                    <p id="error-message">无法连接到服务器，请检查网络连接。</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 服务器卡片模板 -->
<template id="server-card-template">
    <div class="server-card bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-all duration-300" 
         data-server-id="">
        
        <!-- 服务器标题和状态 -->
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <span class="status-indicator status-online"></span>
                <h3 class="text-lg font-semibold text-gray-900 server-name">Server Name</h3>
            </div>
            <div class="text-sm text-gray-500 server-ip">
                ***********
            </div>
        </div>

        <!-- 服务器信息 -->
        <div class="mb-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
                <span class="server-hostname">hostname</span>
                <span class="server-status-text text-green-600">在线</span>
            </div>
            <div class="text-xs text-gray-500 server-os">
                Linux Ubuntu 20.04
            </div>
        </div>

        <!-- CPU 使用率 -->
        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700">CPU 使用率</span>
                <span class="text-sm font-bold text-gray-900 cpu-percentage">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="progress-bar bg-gradient-to-r from-green-400 to-green-600 h-2.5 rounded-full" 
                     data-type="cpu" 
                     style="width: 0%"></div>
            </div>
        </div>

        <!-- 内存使用率 -->
        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700">内存使用率</span>
                <span class="text-sm font-bold text-gray-900 memory-percentage">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="progress-bar bg-gradient-to-r from-blue-400 to-blue-600 h-2.5 rounded-full" 
                     data-type="memory" 
                     style="width: 0%"></div>
            </div>
        </div>

        <!-- 最后更新时间 -->
        <div class="flex justify-between items-center text-xs text-gray-500">
            <span>最后更新: <span class="last-update">--:--:--</span></span>
            <span class="uptime">运行时间: --</span>
        </div>

        <!-- 悬停效果提示 -->
        <div class="mt-3 text-center">
            <span class="text-xs text-gray-400 opacity-0 hover:opacity-100 transition-opacity">
                点击查看详情
            </span>
        </div>
    </div>
</template>

<script>
// 仪表板初始化函数
function initDashboard() {
    console.log('Initializing dashboard...');
    
    // 隐藏加载指示器，显示内容
    setTimeout(() => {
        document.getElementById('loading-indicator').style.display = 'none';
        // 这里将由dashboard.js接管后续的初始化
    }, 1000);
}

// 导航到服务器详情页面
function navigateToDetail(serverId) {
    window.location.href = `/servers/${serverId}`;
}
</script>
{{end}}
