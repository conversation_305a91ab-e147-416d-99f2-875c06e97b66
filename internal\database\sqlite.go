package database

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	_ "modernc.org/sqlite"
)

// Config 数据库配置结构体 (Database configuration struct)
type Config struct {
	DatabasePath     string        `json:"database_path"`      // 数据库文件路径 (Database file path)
	MaxOpenConns     int           `json:"max_open_conns"`     // 数据库最大打开连接数 (Maximum open connections to the database)
	MaxIdleConns     int           `json:"max_idle_conns"`     // 数据库最大空闲连接数 (Maximum idle connections to the database)
	ConnMaxLifetime  time.Duration `json:"conn_max_lifetime"`  // 连接可重用的最长时间 (Maximum time a connection can be reused)
	ConnMaxIdleTime  time.Duration `json:"conn_max_idle_time"` // 连接在关闭前可保持空闲的最长时间 (Maximum time a connection can remain idle before being closed)
	EnableWAL        bool          `json:"enable_wal"`         // 启用 WAL（Write-Ahead Logging）模式 (Enable WAL (Write-Ahead Logging) mode)
	EnableForeignKey bool          `json:"enable_foreign_key"` // 启用外键约束 (Enable foreign key constraints)
	BusyTimeout      time.Duration `json:"busy_timeout"`       // 数据库忙碌时的超时时间 (Timeout for database busy state)
}

// DefaultConfig 返回默认的数据库配置 (Returns default database configuration)
func DefaultConfig() *Config {
	return &Config{
		DatabasePath:     "data/monitor.db",
		MaxOpenConns:     25,
		MaxIdleConns:     5,
		ConnMaxLifetime:  time.Hour,
		ConnMaxIdleTime:  time.Minute * 10,
		EnableWAL:        true,
		EnableForeignKey: true,
		BusyTimeout:      time.Second * 30,
	}
}

// Database 是 SQLite 数据库管理器 (SQLite database manager)
type Database struct {
	db     *sql.DB
	config *Config
	mu     sync.RWMutex
	closed bool
}

// NewDatabase 创建并返回一个新的数据库实例 (Creates and returns a new database instance)
func NewDatabase(config *Config) (*Database, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// 确保数据库文件所在的目录存在，如果不存在则创建 (Ensures the directory for the database file exists, creating it if it doesn't)
	dbDir := filepath.Dir(config.DatabasePath)
	if err := ensureDir(dbDir); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// buildDSN 根据配置构建数据库连接字符串 (Builds the database connection string based on the configuration)
	dsn := buildDSN(config)

	// 打开 SQLite 数据库连接 (Opens a SQLite database connection)
	db, err := sql.Open("sqlite", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 配置数据库连接池参数 (Configures database connection pool parameters)
	db.SetMaxOpenConns(config.MaxOpenConns)
	db.SetMaxIdleConns(config.MaxIdleConns)
	db.SetConnMaxLifetime(config.ConnMaxLifetime)
	db.SetConnMaxIdleTime(config.ConnMaxIdleTime)

	// 测试数据库连接是否正常 (Tests if the database connection is working)
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	database := &Database{
		db:     db,
		config: config,
		closed: false,
	}

	// 初始化数据库的 PRAGMA 设置 (Initializes PRAGMA settings for the database)
	if err := database.initializeSettings(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to initialize database settings: %w", err)
	}

	return database, nil
}

// buildDSN 根据配置构建数据源名称字符串 (Builds the Data Source Name string based on the configuration)
func buildDSN(config *Config) string {
	dsn := config.DatabasePath + "?"

	// 启用 WAL 日志模式 (Enable WAL (Write-Ahead Logging) mode)
	if config.EnableWAL {
		dsn += "journal_mode=WAL&"
	}

	// 启用外键约束 (Enable foreign key constraints)
	if config.EnableForeignKey {
		dsn += "foreign_keys=ON&"
	}

	// 设置数据库忙碌超时时间 (Sets the database busy timeout)
	if config.BusyTimeout > 0 {
		dsn += fmt.Sprintf("busy_timeout=%d&", int(config.BusyTimeout.Milliseconds()))
	}

	// 其他 SQLite 性能优化设置 (Other SQLite performance optimization settings)
	dsn += "synchronous=NORMAL&"
	dsn += "cache_size=-64000&" // 64MB缓存
	dsn += "temp_store=MEMORY&"
	dsn += "mmap_size=268435456" // 256MB内存映射

	return dsn
}

// initializeSettings 初始化数据库的 PRAGMA 设置 (Initializes PRAGMA settings for the database)
func (d *Database) initializeSettings() error {
	settings := []string{
		"PRAGMA journal_mode = WAL",
		"PRAGMA synchronous = NORMAL",
		"PRAGMA cache_size = -64000",
		"PRAGMA temp_store = MEMORY",
		"PRAGMA mmap_size = 268435456",
	}

	if d.config.EnableForeignKey {
		settings = append(settings, "PRAGMA foreign_keys = ON")
	}

	for _, setting := range settings {
		if _, err := d.db.Exec(setting); err != nil {
			return fmt.Errorf("failed to execute setting '%s': %w", setting, err)
		}
	}

	return nil
}

// GetDB 获取底层的 *sql.DB 连接对象 (Gets the underlying *sql.DB connection object)
func (d *Database) GetDB() *sql.DB {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.db
}

// Close 关闭数据库连接 (Closes the database connection)
func (d *Database) Close() error {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.closed {
		return nil
	}

	d.closed = true
	return d.db.Close()
}

// IsClosed 检查数据库连接是否已关闭 (Checks if the database connection is closed)
func (d *Database) IsClosed() bool {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.closed
}

// Ping 测试数据库连接的可用性 (Tests the availability of the database connection)
func (d *Database) Ping() error {
	d.mu.RLock()
	defer d.mu.RUnlock()

	if d.closed {
		return ErrDatabaseClosed
	}

	return d.db.Ping()
}

// BeginTx 开始一个新的数据库事务 (Starts a new database transaction)
func (d *Database) BeginTx() (*sql.Tx, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	if d.closed {
		return nil, ErrDatabaseClosed
	}

	return d.db.Begin()
}

// Stats 获取数据库连接池的统计信息 (Gets statistics for the database connection pool)
func (d *Database) Stats() sql.DBStats {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.db.Stats()
}

// GetConfig 获取当前数据库的配置 (Gets the current database configuration)
func (d *Database) GetConfig() *Config {
	return d.config
}

// ensureDir 确保目录存在，如果不存在则创建 (Ensures the directory exists, creating it if it doesn't)
func ensureDir(dir string) error {
	return os.MkdirAll(dir, 0755)
}
