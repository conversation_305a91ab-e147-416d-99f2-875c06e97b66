package database

import (
	"os"
	"path/filepath"
	"testing"
	"time"
	"server-monitor/internal/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 创建测试数据库
func setupTestDB(t *testing.T) (*gorm.DB, Repository, func()) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "monitor_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}

	// 配置测试数据库
	config := &Config{
		DatabasePath:    filepath.Join(tempDir, "test.db"),
		MaxOpenConns:    5,
		MaxIdleConns:    2,
		ConnMaxLifetime: time.Minute,
		ConnMaxIdleTime: time.Second * 30,
		EnableWAL:       true,
		EnableForeignKey: true,
		BusyTimeout:     time.Second * 10,
	}

	// 创建数据库实例 (使用GORM)
	db, err := gorm.Open(sqlite.Open(config.DatabasePath), &gorm.Config{})
	if err != nil {
		os.RemoveAll(tempDir)
		t.Fatalf("Failed to open GORM database: %v", err)
	}

	// 运行迁移 (使用GORM AutoMigrate)
	err = db.AutoMigrate(&models.Server{}, &HourlyResult{}, &SyncStatus{}, &SystemConfig{})
	if err != nil {
		sqlDB, _ := db.DB()
		sqlDB.Close()
		os.RemoveAll(tempDir)
		t.Fatalf("Failed to auto migrate database: %v", err)
	}

	// 创建repository
	repo := NewRepository(db)

	// 清理函数
	cleanup := func() {
		sqlDB, _ := db.DB()
		sqlDB.Close()
		os.RemoveAll(tempDir)
	}

	return db, repo, cleanup
}

func TestDatabaseConnection(t *testing.T) {
	gormDB, _, cleanup := setupTestDB(t) // Change db to gormDB
	defer cleanup()

	// GORM doesn't have Ping() or Stats() directly on *gorm.DB
	// You can check if the underlying sql.DB is alive
	sqlDB, err := gormDB.DB()
	if err != nil {
		t.Fatalf("Failed to get underlying sql.DB: %v", err)
	}
	if err := sqlDB.Ping(); err != nil {
		t.Errorf("Failed to ping database: %v", err)
	}

	// GORM doesn't expose these directly, so these tests are removed
	// stats := gormDB.Stats()
	// if stats.MaxOpenConnections != 5 {
	// 	t.Errorf("Expected MaxOpenConnections=5, got %d", stats.MaxOpenConnections)
	// }

	// config := gormDB.GetConfig()
	// if config.MaxOpenConns != 5 {
	// 	t.Errorf("Expected MaxOpenConns=5, got %d", config.MaxOpenConns)
	// }
}


func TestServerOperations(t *testing.T) {
	_, repo, cleanup := setupTestDB(t)
	defer cleanup()

	// 测试创建服务器
	server := &models.Server{ // Change to models.Server
		Name:      "test-server",
		IPAddress: "*************", // Change IP to IPAddress
		IsActive:  true,
		LastSeen:  time.Now(),
	}

	err := repo.CreateServer(server)
	if err != nil {
		t.Errorf("Failed to create server: %v", err)
	}

	if server.ID == 0 {
		t.Error("Expected server ID to be set")
	}

	// 测试获取服务器
	retrieved, err := repo.GetServer(int(server.ID)) // Cast to int for GetServer
	if err != nil {
		t.Errorf("Failed to get server: %v", err)
	}

	if retrieved.Name != server.Name {
		t.Errorf("Expected name=%s, got %s", server.Name, retrieved.Name)
	}

	// 测试按名称获取服务器
	byName, err := repo.GetServerByName(server.Name)
	if err != nil {
		t.Errorf("Failed to get server by name: %v", err)
	}

	if byName.ID != server.ID {
		t.Errorf("Expected ID=%d, got %d", server.ID, byName.ID)
	}

	// 测试更新服务器
	// server.Location = "Updated Location" // Remove Location
	err = repo.UpdateServer(server)
	if err != nil {
		t.Errorf("Failed to update server: %v", err)
	}


	// 测试列出服务器
	servers, err := repo.ListServers(false)
	if err != nil {
		t.Errorf("Failed to list servers: %v", err)
	}

	if len(servers) != 1 {
		t.Errorf("Expected 1 server, got %d", len(servers))
	}

	// 测试删除服务器
	err = repo.DeleteServer(int(server.ID)) // Cast to int for DeleteServer
	if err != nil {
		t.Errorf("Failed to delete server: %v", err)
	}

	// 验证删除
	_, err = repo.GetServer(int(server.ID)) // Cast to int for GetServer
	if err != ErrRecordNotFound {
		t.Errorf("Expected ErrRecordNotFound, got %v", err)
	}
}

func TestHourlyResultOperations(t *testing.T) {
	_, repo, cleanup := setupTestDB(t)
	defer cleanup()

	// 先创建一个服务器
	server := &models.Server{ // Change to models.Server
		Name:      "test-server",
		IPAddress: "*************", // Change IP to IPAddress
		IsActive:  true,
		LastSeen:  time.Now(),
	}
	err := repo.CreateServer(server)
	if err != nil {
		t.Fatalf("Failed to create server: %v", err)
	}

	// 测试创建测试结果
	testHour := time.Now().Truncate(time.Hour)
	result := &HourlyResult{
		ServerID:      server.ID, // ServerID is now uint
		TestHour:      testHour,
		UploadSpeed:   100.5,
		DownloadSpeed: 200.8,
		Latency:       15.2,
		Jitter:        2.1,
		PacketLoss:    0.1,
		TestDuration:  30,
		TestType:      TestTypeBoth,
		Status:        TestStatusSuccess,
	}

	err = repo.CreateHourlyResult(result)
	if err != nil {
		t.Errorf("Failed to create hourly result: %v", err)
	}

	if result.ID == 0 {
		t.Error("Expected result ID to be set")
	}

	// 测试获取测试结果
	retrieved, err := repo.GetHourlyResult(server.ID, testHour) // server.ID is uint
	if err != nil {
		t.Errorf("Failed to get hourly result: %v", err)
	}

	if retrieved.UploadSpeed != result.UploadSpeed {
		t.Errorf("Expected upload speed=%.1f, got %.1f", result.UploadSpeed, retrieved.UploadSpeed)
	}

	// 测试更新测试结果
	result.Status = TestStatusFailed
	result.ErrorMessage = "Test error"
	err = repo.UpdateHourlyResult(result)
	if err != nil {
		t.Errorf("Failed to update hourly result: %v", err)
	}

	updated, err := repo.GetHourlyResult(server.ID, testHour) // server.ID is uint
	if err != nil {
		t.Errorf("Failed to get updated result: %v", err)
	}

	if updated.Status != TestStatusFailed {
		t.Errorf("Expected status=%s, got %s", TestStatusFailed, updated.Status)
	}

	// 测试列出测试结果
	startTime := testHour.Add(-time.Hour)
	endTime := testHour.Add(time.Hour)
	results, err := repo.ListHourlyResults(server.ID, startTime, endTime) // server.ID is uint
	if err != nil {
		t.Errorf("Failed to list hourly results: %v", err)
	}

	if len(results) != 1 {
		t.Errorf("Expected 1 result, got %d", len(results))
	}

	// 测试删除测试结果
	err = repo.DeleteHourlyResult(result.ID)
	if err != nil {
		t.Errorf("Failed to delete hourly result: %v", err)
	}

	// 验证删除
	_, err = repo.GetHourlyResult(server.ID, testHour) // server.ID is uint
	if err != ErrRecordNotFound {
		t.Errorf("Expected ErrRecordNotFound, got %v", err)
	}
}

func TestSystemConfigOperations(t *testing.T) {
	_, repo, cleanup := setupTestDB(t)
	defer cleanup()

	// 测试设置配置
	err := repo.SetConfig("test_key", "test_value", "Test configuration", ConfigTypeString, false)
	if err != nil {
		t.Errorf("Failed to set config: %v", err)
	}

	// 测试获取配置
	config, err := repo.GetConfig("test_key")
	if err != nil {
		t.Errorf("Failed to get config: %v", err)
	}

	if config.ConfigValue != "test_value" {
		t.Errorf("Expected value='test_value', got %s", config.ConfigValue)
	}

	// 测试更新配置
	err = repo.SetConfig("test_key", "updated_value", "Updated configuration", ConfigTypeString, true)
	if err != nil {
		t.Errorf("Failed to update config: %v", err)
	}

	updated, err := repo.GetConfig("test_key")
	if err != nil {
		t.Errorf("Failed to get updated config: %v", err)
	}

	if updated.ConfigValue != "updated_value" {
		t.Errorf("Expected value='updated_value', got %s", updated.ConfigValue)
	}

	if !updated.IsSystem {
		t.Error("Expected IsSystem=true")
	}

	// 测试列出配置
	configs, err := repo.ListConfigs(false)
	if err != nil {
		t.Errorf("Failed to list configs: %v", err)
	}

	if len(configs) != 1 {
		t.Errorf("Expected 1 config, got %d", len(configs))
	}

	// 测试删除配置
	err = repo.DeleteConfig("test_key")
	if err != nil {
		t.Errorf("Failed to delete config: %v", err)
	}

	// 验证删除
	_, err = repo.GetConfig("test_key")
	if err != ErrRecordNotFound {
		t.Errorf("Expected ErrRecordNotFound, got %v", err)
	}
}
