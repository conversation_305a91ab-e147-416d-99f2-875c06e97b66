// websocket.js
console.log('WebSocket script loaded.');

let ws;
const messageHandlers = {}; // To store handlers for different message types

function connectWebSocket(path, onOpenCallback, onCloseCallback, onErrorCallback) {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const url = `${protocol}//${host}${path}`;

    ws = new WebSocket(url);

    ws.onopen = function(event) {
        console.log('WebSocket connected:', event);
        if (onOpenCallback) onOpenCallback(event);
    };

    ws.onmessage = function(event) {
        try {
            const message = JSON.parse(event.data);

            // 处理不同的消息格式
            let messageType, messageData;

            if (message.type && message.data !== undefined) {
                // 格式1: {type: "system_info", data: {...}, timestamp: 123}
                messageType = message.type;
                messageData = message.data;
            } else if (message.type && message.payload !== undefined) {
                // 格式2: {type: "system_info", payload: {...}}
                messageType = message.type;
                messageData = message.payload;
            } else {
                console.log('Received unknown WebSocket message format:', message);
                return;
            }

            // 调用注册的处理器
            if (messageHandlers[messageType]) {
                messageHandlers[messageType](messageData);
            } else {
                console.log('No handler for message type:', messageType, messageData);
            }
        } catch (e) {
            console.error('Failed to parse WebSocket message:', e, event.data);
        }
    };

    ws.onclose = function(event) {
        console.log('WebSocket disconnected:', event);
        if (onCloseCallback) onCloseCallback(event);
        // Attempt to reconnect after a delay
        setTimeout(() => connectWebSocket(path, onOpenCallback, onCloseCallback, onErrorCallback), 3000);
    };

    ws.onerror = function(error) {
        console.error('WebSocket error:', error);
        if (onErrorCallback) onErrorCallback(error);
        ws.close(); // Close to trigger reconnect logic
    };
}

function sendMessage(type, payload) {
    if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type, payload }));
    } else {
        console.warn('WebSocket not open. Message not sent:', { type, payload });
    }
}

function registerMessageHandler(type, handler) {
    messageHandlers[type] = handler;
}

function unregisterMessageHandler(type) {
    delete messageHandlers[type];
}

/**
 * 初始化图表WebSocket连接
 * @param {string} path - WebSocket路径
 * @param {Object} chartConfigs - 图表配置 {canvasId: type}
 */
function initChartWebSocket(path, chartConfigs) {
    // 注册系统信息处理器
    registerMessageHandler('system_info', function(data) {
        if (window.ChartManager) {
            window.ChartManager.handleSystemInfoUpdate(data);
        }
    });

    // 注册欢迎消息处理器
    registerMessageHandler('welcome', function(data) {
        console.log('WebSocket连接成功:', data);
        // 请求初始系统信息
        sendMessage('get_system_info', {});
    });

    // 注册错误处理器
    registerMessageHandler('error', function(data) {
        console.error('WebSocket错误:', data);
        // 设置所有图表为错误状态
        if (window.ChartManager && chartConfigs) {
            Object.keys(chartConfigs).forEach(canvasId => {
                window.ChartManager.setChartError(canvasId, '连接错误');
            });
        }
    });

    // 连接WebSocket
    connectWebSocket(path,
        function onOpen() {
            console.log('图表WebSocket连接已建立');
            // 连接成功后创建图表
            if (window.ChartManager && chartConfigs) {
                window.ChartManager.createMonitoringCharts(chartConfigs);
            }
        },
        function onClose() {
            console.log('图表WebSocket连接已关闭');
            // 连接关闭时设置错误状态
            if (window.ChartManager && chartConfigs) {
                Object.keys(chartConfigs).forEach(canvasId => {
                    window.ChartManager.setChartError(canvasId, '连接已断开');
                });
            }
        },
        function onError(error) {
            console.error('图表WebSocket连接错误:', error);
            // 连接错误时设置错误状态
            if (window.ChartManager && chartConfigs) {
                Object.keys(chartConfigs).forEach(canvasId => {
                    window.ChartManager.setChartError(canvasId, '连接失败');
                });
            }
        }
    );
}

/**
 * 请求系统信息更新
 */
function requestSystemInfo() {
    sendMessage('get_system_info', {});
}

/**
 * 清理图表WebSocket连接
 */
function cleanupChartWebSocket() {
    unregisterMessageHandler('system_info');
    unregisterMessageHandler('welcome');
    unregisterMessageHandler('error');

    if (window.ChartManager) {
        window.ChartManager.destroyAllCharts();
    }
}

export {
    connectWebSocket,
    sendMessage,
    registerMessageHandler,
    unregisterMessageHandler,
    initChartWebSocket,
    requestSystemInfo,
    cleanupChartWebSocket
};