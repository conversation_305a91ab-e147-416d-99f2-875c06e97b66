package protocol

import (
	"context"
	"errors"
	"sync"
	"testing"
	"time"
)

// MockMessageSender 模拟消息发送器 (Mock message sender)
type MockMessageSender struct {
	sentMessages []string
	shouldFail   bool
	failCount    int
	mutex        sync.Mutex
}

// SendMessage 发送消息 (Sends message)
func (m *MockMessageSender) SendMessage(ctx context.Context, msg *ApplicationMessage) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if m.shouldFail && m.failCount > 0 {
		m.failCount--
		return errors.New("mock send failure")
	}
	
	m.sentMessages = append(m.sentMessages, msg.ID)
	return nil
}

// GetSentMessages 获取已发送消息 (Gets sent messages)
func (m *MockMessageSender) GetSentMessages() []string {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	result := make([]string, len(m.sentMessages))
	copy(result, m.sentMessages)
	return result
}

// Reset 重置发送器 (Resets sender)
func (m *MockMessageSender) Reset() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.sentMessages = nil
	m.shouldFail = false
	m.failCount = 0
}

func TestNewMessageReliabilityManager(t *testing.T) {
	sender := &MockMessageSender{}
	mrm := NewMessageReliabilityManager(nil, sender)
	defer mrm.Stop()
	
	if mrm == nil {
		t.Fatal("MessageReliabilityManager should not be nil")
	}
	
	if mrm.config.AckTimeout != 30*time.Second {
		t.Errorf("Expected ack timeout 30s, got %v", mrm.config.AckTimeout)
	}
}

func TestSendReliableMessage(t *testing.T) {
	sender := &MockMessageSender{}
	config := DefaultMessageReliabilityConfig()
	config.AckTimeout = 100 * time.Millisecond // 短超时用于测试 (Short timeout for testing)
	
	mrm := NewMessageReliabilityManager(config, sender)
	defer mrm.Stop()
	
	// 创建测试消息 (Create test message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg.SetSequenceNumber(1)
	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetChecksum()
	
	// 发送消息 (Send message)
	ctx := context.Background()
	err := mrm.SendReliableMessage(ctx, msg)
	if err != nil {
		t.Fatalf("Failed to send reliable message: %v", err)
	}
	
	// 等待消息发送 (Wait for message to be sent)
	time.Sleep(50 * time.Millisecond)
	
	// 检查消息是否被发送 (Check if message was sent)
	sentMessages := sender.GetSentMessages()
	if len(sentMessages) != 1 {
		t.Errorf("Expected 1 sent message, got %d", len(sentMessages))
	}
	if sentMessages[0] != msg.ID {
		t.Errorf("Expected message ID %s, got %s", msg.ID, sentMessages[0])
	}
	
	// 检查待处理消息 (Check pending message)
	pendingMsg, exists := mrm.GetPendingMessage(msg.ID)
	if !exists {
		t.Error("Message should be in pending list")
	}
	if pendingMsg.Status != MessageStatusSent {
		t.Errorf("Expected status sent, got %s", pendingMsg.Status)
	}
}

func TestHandleAck(t *testing.T) {
	sender := &MockMessageSender{}
	mrm := NewMessageReliabilityManager(nil, sender)
	defer mrm.Stop()
	
	// 创建并发送消息 (Create and send message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg.SetSequenceNumber(1)
	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetChecksum()
	
	ctx := context.Background()
	err := mrm.SendReliableMessage(ctx, msg)
	if err != nil {
		t.Fatalf("Failed to send reliable message: %v", err)
	}
	
	// 等待消息发送 (Wait for message to be sent)
	time.Sleep(50 * time.Millisecond)
	
	// 处理确认 (Handle acknowledgment)
	err = mrm.HandleAck(msg.ID)
	if err != nil {
		t.Errorf("Failed to handle ack: %v", err)
	}
	
	// 检查消息状态 (Check message status)
	pendingMsg, exists := mrm.GetPendingMessage(msg.ID)
	if !exists {
		t.Error("Message should still be in pending list")
	}
	if pendingMsg.Status != MessageStatusAcked {
		t.Errorf("Expected status acked, got %s", pendingMsg.Status)
	}
}

func TestDuplicateDetection(t *testing.T) {
	sender := &MockMessageSender{}
	config := DefaultMessageReliabilityConfig()
	config.EnableDedup = true
	
	mrm := NewMessageReliabilityManager(config, sender)
	defer mrm.Stop()
	
	// 创建消息 (Create message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg.SetSequenceNumber(1)
	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetChecksum()
	
	ctx := context.Background()
	
	// 第一次发送应该成功 (First send should succeed)
	err := mrm.SendReliableMessage(ctx, msg)
	if err != nil {
		t.Fatalf("First send should succeed: %v", err)
	}
	
	// 第二次发送应该检测到重复 (Second send should detect duplicate)
	err = mrm.SendReliableMessage(ctx, msg)
	if err == nil {
		t.Error("Second send should fail due to duplicate detection")
	}
	if !contains(err.Error(), "duplicate") {
		t.Errorf("Error should mention duplicate, got: %v", err)
	}
}

func TestMessageRetry(t *testing.T) {
	sender := &MockMessageSender{
		shouldFail: true,
		failCount:  2, // 前两次失败，第三次成功 (Fail first two times, succeed on third)
	}
	
	config := DefaultMessageReliabilityConfig()
	config.RetryConfig.MaxAttempts = 3
	config.RetryConfig.InitialDelay = 10 * time.Millisecond
	
	mrm := NewMessageReliabilityManager(config, sender)
	defer mrm.Stop()
	
	// 创建消息 (Create message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg.SetSequenceNumber(1)
	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetChecksum()
	
	ctx := context.Background()
	err := mrm.SendReliableMessage(ctx, msg)
	if err != nil {
		t.Fatalf("Failed to send reliable message: %v", err)
	}
	
	// 等待重试完成 (Wait for retries to complete)
	time.Sleep(200 * time.Millisecond)
	
	// 检查消息最终发送成功 (Check message was eventually sent successfully)
	sentMessages := sender.GetSentMessages()
	if len(sentMessages) != 1 {
		t.Errorf("Expected 1 sent message after retries, got %d", len(sentMessages))
	}
	
	// 检查重试结果 (Check retry result)
	pendingMsg, exists := mrm.GetPendingMessage(msg.ID)
	if !exists {
		t.Error("Message should be in pending list")
	}
	if pendingMsg.RetryResult == nil {
		t.Error("Retry result should not be nil")
	}
	if !pendingMsg.RetryResult.Success {
		t.Error("Retry should have succeeded")
	}
	if pendingMsg.RetryResult.Attempts != 3 {
		t.Errorf("Expected 3 attempts, got %d", pendingMsg.RetryResult.Attempts)
	}
}

func TestMessageExpiration(t *testing.T) {
	sender := &MockMessageSender{}
	config := DefaultMessageReliabilityConfig()
	config.MessageTTL = 50 * time.Millisecond // 很短的TTL用于测试 (Very short TTL for testing)
	config.CleanupInterval = 25 * time.Millisecond
	
	mrm := NewMessageReliabilityManager(config, sender)
	defer mrm.Stop()
	
	// 创建消息 (Create message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg.SetSequenceNumber(1)
	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetChecksum()
	
	ctx := context.Background()
	err := mrm.SendReliableMessage(ctx, msg)
	if err != nil {
		t.Fatalf("Failed to send reliable message: %v", err)
	}
	
	// 等待消息过期和清理 (Wait for message to expire and be cleaned up)
	time.Sleep(100 * time.Millisecond)
	
	// 检查消息是否被清理 (Check if message was cleaned up)
	_, exists := mrm.GetPendingMessage(msg.ID)
	if exists {
		t.Error("Expired message should have been cleaned up")
	}
}

func TestGetStats(t *testing.T) {
	sender := &MockMessageSender{}
	mrm := NewMessageReliabilityManager(nil, sender)
	defer mrm.Stop()
	
	// 发送几条消息 (Send several messages)
	for i := 0; i < 3; i++ {
		msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
			Username: "testuser",
			Password: "testpass",
			ClientID: "client_001",
			Version:  "1.0.0",
		})
		msg.SetSequenceNumber(uint64(i + 1))
		msg.Timestamp = time.Now().UnixNano()
		msg.GenerateAndSetChecksum()
		
		ctx := context.Background()
		err := mrm.SendReliableMessage(ctx, msg)
		if err != nil {
			t.Fatalf("Failed to send message %d: %v", i, err)
		}
	}
	
	// 等待消息发送 (Wait for messages to be sent)
	time.Sleep(50 * time.Millisecond)
	
	// 获取统计信息 (Get statistics)
	stats := mrm.GetStats()
	
	pendingCount, ok := stats["pending_count"].(int)
	if !ok || pendingCount != 3 {
		t.Errorf("Expected 3 pending messages, got %v", stats["pending_count"])
	}
	
	statusCounts, ok := stats["status_counts"].(map[string]int)
	if !ok {
		t.Error("Status counts should be a map")
	}
	
	if statusCounts["sent"] != 3 {
		t.Errorf("Expected 3 sent messages, got %d", statusCounts["sent"])
	}
}

func TestMaxPendingMessages(t *testing.T) {
	sender := &MockMessageSender{}
	config := DefaultMessageReliabilityConfig()
	config.MaxPendingMsgs = 2 // 限制为2条消息 (Limit to 2 messages)
	
	mrm := NewMessageReliabilityManager(config, sender)
	defer mrm.Stop()
	
	ctx := context.Background()
	
	// 发送前两条消息应该成功 (First two messages should succeed)
	for i := 0; i < 2; i++ {
		msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
			Username: "testuser",
			Password: "testpass",
			ClientID: "client_001",
			Version:  "1.0.0",
		})
		msg.SetSequenceNumber(uint64(i + 1))
		msg.Timestamp = time.Now().UnixNano()
		msg.GenerateAndSetChecksum()
		
		err := mrm.SendReliableMessage(ctx, msg)
		if err != nil {
			t.Fatalf("Message %d should succeed: %v", i, err)
		}
	}
	
	// 第三条消息应该失败 (Third message should fail)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg.SetSequenceNumber(3)
	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetChecksum()
	
	err := mrm.SendReliableMessage(ctx, msg)
	if err == nil {
		t.Error("Third message should fail due to max pending limit")
	}
	if !contains(err.Error(), "too many pending") {
		t.Errorf("Error should mention too many pending, got: %v", err)
	}
}

// 辅助函数 (Helper function)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
				s[len(s)-len(substr):] == substr ||
				containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
