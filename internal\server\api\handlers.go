package api

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time" // Add time package

	"server-monitor/internal/database"
	"server-monitor/internal/models"

	"github.com/gin-gonic/gin"
)

// ServerRepository 接口定义了与服务器数据交互的方法
type ServerRepository interface {
	CreateServer(server *models.Server) error
	GetServer(id int) (*models.Server, error)
	UpdateServer(server *models.Server) error
	DeleteServer(id int) error
	ListServers(active bool) ([]*models.Server, error)
}

// CreateServerRequest 定义了创建服务器的请求体
type CreateServerRequest struct {
	Name        string `json:"name" binding:"required"`
	IPAddress   string `json:"ip_address" binding:"required"`
	Hostname    string `json:"hostname"`
	OSType      string `json:"os_type"`
	OSVersion   string `json:"os_version"`
	Description string `json:"description"`
	Tags        string `json:"tags"`
}

// Validate validates the CreateServerRequest struct fields
func (r *CreateServerRequest) Validate() error {
	if r.Name == "" {
		return fmt.Errorf("server name cannot be empty")
	}
	if r.IPAddress == "" {
		return fmt.Errorf("IP address cannot be empty")
	}
	// Basic IP address validation (can be enhanced with regex)
	if !strings.Contains(r.IPAddress, ".") { // Simple check for IPv4
		return fmt.Errorf("invalid IP address format")
	}
	return nil
}

// UpdateServerRequest 定义了更新服务器的请求体
type UpdateServerRequest struct {
	ID          uint   `json:"id" binding:"required"`
	Name        string `json:"name"`
	IPAddress   string `json:"ip_address"`
	Hostname    string `json:"hostname"`
	OSType      string `json:"os_type"`
	OSVersion   string `json:"os_version"`
	IsActive    *bool  `json:"is_active"` // 使用指针以区分未提供和明确设置为false
	Description string `json:"description"`
	Tags        string `json:"tags"`
}

// Validate validates the UpdateServerRequest struct fields
func (r *UpdateServerRequest) Validate() error {
	if r.ID == 0 {
		return fmt.Errorf("server ID cannot be empty")
	}
	// At least one field should be provided for update
	if r.Name == "" && r.IPAddress == "" && r.Hostname == "" && r.OSType == "" &&
		r.OSVersion == "" && r.IsActive == nil && r.Description == "" && r.Tags == "" {
		return fmt.Errorf("at least one field must be provided for update")
	}
	if r.IPAddress != "" && !strings.Contains(r.IPAddress, ".") { // Simple check for IPv4
		return fmt.Errorf("invalid IP address format")
	}
	return nil
}

// CreateServerHandler 处理创建服务器的请求
func CreateServerHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req CreateServerRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Invalid request payload", err.Error())
			return
		}

		if err := req.Validate(); err != nil {
			SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Validation failed", err.Error())
			return
		}

		server := &models.Server{
			Name:        req.Name,
			IPAddress:   req.IPAddress,
			Hostname:    req.Hostname,
			OSType:      req.OSType,
			OSVersion:   req.OSVersion,
			IsActive:    true, // 默认激活
			Description: req.Description,
			Tags:        req.Tags,
		}

		if err := repo.CreateServer(server); err != nil {
			SendErrorResponse(c, http.StatusInternalServerError, http.StatusInternalServerError, "Failed to create server", err.Error())
			return
		}

		c.JSON(http.StatusCreated, server)
	}
}

// GetServerHandler 处理获取单个服务器的请求
func GetServerHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := strconv.Atoi(idParam)
		if err != nil {
			SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Invalid server ID", err.Error())
			return
		}

		server, err := repo.GetServer(id)
		if err != nil {
			if err == database.ErrRecordNotFound {
				SendErrorResponse(c, http.StatusNotFound, http.StatusNotFound, "Server not found")
				return
			}
			SendErrorResponse(c, http.StatusInternalServerError, http.StatusInternalServerError, "Failed to get server", err.Error())
			return
		}

		c.JSON(http.StatusOK, server)
	}
}

// UpdateServerHandler 处理更新服务器的请求
func UpdateServerHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req UpdateServerRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Invalid request payload", err.Error())
			return
		}

		if err := req.Validate(); err != nil {
			SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Validation failed", err.Error())
			return
		}

		server, err := repo.GetServer(int(req.ID))
		if err != nil {
			if err == database.ErrRecordNotFound {
				SendErrorResponse(c, http.StatusNotFound, http.StatusNotFound, "Server not found")
				return
			}
			SendErrorResponse(c, http.StatusInternalServerError, http.StatusInternalServerError, "Failed to get server for update", err.Error())
			return
		}

		// 更新字段
		if req.Name != "" {
			server.Name = req.Name
		}
		if req.IPAddress != "" {
			server.IPAddress = req.IPAddress
		}
		if req.Hostname != "" {
			server.Hostname = req.Hostname
		}
		if req.OSType != "" {
			server.OSType = req.OSType
		}
		if req.OSVersion != "" {
			server.OSVersion = req.OSVersion
		}
		if req.IsActive != nil { // 检查是否提供了 IsActive 字段
			server.IsActive = *req.IsActive
		}
		if req.Description != "" {
			server.Description = req.Description
		}
		if req.Tags != "" {
			server.Tags = req.Tags
		}

		if err := repo.UpdateServer(server); err != nil {
			SendErrorResponse(c, http.StatusInternalServerError, http.StatusInternalServerError, "Failed to update server", err.Error())
			return
		}

		c.JSON(http.StatusOK, server)
	}
}

// DeleteServerHandler 处理删除服务器的请求
func DeleteServerHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := strconv.Atoi(idParam)
		if err != nil {
			SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Invalid server ID", err.Error())
			return
		}

		if err := repo.DeleteServer(id); err != nil {
			if err == database.ErrRecordNotFound {
				SendErrorResponse(c, http.StatusNotFound, http.StatusNotFound, "Server not found")
				return
			}
			SendErrorResponse(c, http.StatusInternalServerError, http.StatusInternalServerError, "Failed to delete server", err.Error())
			return
		}

		c.JSON(http.StatusNoContent, nil) // 204 No Content
	}
}

// GetSystemInfoHandler 处理获取系统信息的请求
func GetSystemInfoHandler(repo ServerRepository) gin.HandlerFunc { // Note: Using ServerRepository for now, will need to adjust if a more generic repo is introduced
	return func(c *gin.Context) {
		serverIDParam := c.Param("serverID")
		serverID, err := strconv.ParseUint(serverIDParam, 10, 64)
		if err != nil {
			SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Invalid server ID", err.Error())
			return
		}

		startTimeStr := c.Query("start_time")
		endTimeStr := c.Query("end_time")

		var startTime, endTime time.Time

		if startTimeStr != "" {
			startTime, err = time.Parse(time.RFC3339, startTimeStr)
			if err != nil {
				SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Invalid start_time format. Use RFC3339.", err.Error())
				return
			}
		}

		if endTimeStr != "" {
			endTime, err = time.Parse(time.RFC3339, endTimeStr)
			if err != nil {
				SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Invalid end_time format. Use RFC3339.", err.Error())
				return
			}
		}

		// Need to cast repo to database.Repository to access GetSystemInfo
		dbRepo, ok := repo.(database.Repository)
		if !ok {
			SendErrorResponse(c, http.StatusInternalServerError, http.StatusInternalServerError, "Repository does not support SystemInfo operations")
			return
		}

		systemInfos, err := dbRepo.GetSystemInfo(uint(serverID), startTime, endTime)
		if err != nil {
			SendErrorResponse(c, http.StatusInternalServerError, http.StatusInternalServerError, "Failed to get system info", err.Error())
			return
		}

		c.JSON(http.StatusOK, systemInfos)
	}
}

// ListServersHandler 处理列出所有服务器的请求
func ListServersHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 可以添加查询参数来过滤，例如 active=true/false
		activeParam := c.DefaultQuery("active", "true")
		active := true
		if activeParam == "false" {
			active = false
		}

		servers, err := repo.ListServers(active)
		if err != nil {
			SendErrorResponse(c, http.StatusInternalServerError, http.StatusInternalServerError, "Failed to list servers", err.Error())
			return
		}

		c.JSON(http.StatusOK, servers)
	}
}

// ErrorResponse 定义了统一的错误响应结构
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// NewErrorResponse 创建并返回一个 ErrorResponse 实例
func NewErrorResponse(code int, message string, details ...string) ErrorResponse {
	errResp := ErrorResponse{
		Code:    code,
		Message: message,
	}
	if len(details) > 0 {
		errResp.Details = details[0]
	}
	return errResp
}

// SendErrorResponse 发送统一的错误响应
func SendErrorResponse(c *gin.Context, httpStatus int, code int, message string, details ...string) {
	c.JSON(httpStatus, NewErrorResponse(code, message, details...))
}

// HealthCheckHandler 处理健康检查端点。(HealthCheckHandler handles the health check endpoint.)
func HealthCheckHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// ExampleRequest 表示示例端点的请求体。(ExampleRequest represents the request body for the example endpoint.)
type ExampleRequest struct {
	Name  string `json:"name" binding:"required"`
	Value int    `json:"value" binding:"required,gte=0"`
}

// ExampleHandler 处理带有数据验证的示例端点。(ExampleHandler handles the example endpoint with data validation.)
func ExampleHandler(c *gin.Context) {
	var req ExampleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		SendErrorResponse(c, http.StatusBadRequest, http.StatusBadRequest, "Invalid request payload", err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Data received and validated successfully", "data": req})
}
