# 🖥️ 服务器监控系统设计文档

## 📋 项目概述

### 系统简介
基于Go语言开发的分布式服务器监控系统，支持监控12台服务器的CPU、内存、磁盘、网络等资源使用情况，并提供supervisorctl、systemd、docker服务管理功能。

### 核心特性
- 🔄 **统一程序**：单个可执行文件，支持服务端/客户端模式切换
- 🔐 **AES加密**：端到端AES-256-GCM加密传输
- 📊 **实时监控**：系统资源和服务状态实时监控
- 🎛️ **服务管理**：支持supervisorctl、systemd、docker管理
- 🌐 **Web界面**：基于Go模板 + Tailwind CSS的现代化界面
- 🗄️ **SQLite存储**：轻量级数据库，无需额外服务

## 🏗️ 技术架构

### 技术栈选择

| 组件 | 技术选择 | 说明 |
|------|----------|------|
| **后端框架** | Gin | 轻量高性能HTTP框架 |
| **数据库** | SQLite + GORM | 轻量级数据库配合ORM |
| **前端** | Go模板 + Tailwind CSS | 服务端渲染 + 现代CSS框架 |
| **实时通信** | WebSocket | 实时数据推送 |
| **系统监控** | gopsutil | 跨平台系统信息获取 |
| **加密传输** | AES-256-GCM | 对称加密 + 认证 |
| **配置管理** | 命令行参数 | 简化部署和配置 |

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端服务器   │    │   服务端        │    │   Web浏览器     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 系统监控    │ │    │ │ 数据接收    │ │    │ │ 监控界面    │ │
│ │ gopsutil    │ │    │ │ AES解密     │ │    │ │ Tailwind    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 服务管理    │ │    │ │ Web服务     │ │    │ │ 实时图表    │ │
│ │ supervisord │◄────┤ │ Gin框架     │◄────┤ │ Chart.js    │ │
│ │ systemd     │ │    │ │ WebSocket   │ │    │ │ WebSocket   │ │
│ │ docker      │ │    │ └─────────────┘ │    │ └─────────────┘ │
│ └─────────────┘ │    │ ┌─────────────┐ │    └─────────────────┘
│ ┌─────────────┐ │    │ │ 数据存储    │ │
│ │ AES加密传输 │ │    │ │ SQLite      │ │
│ └─────────────┘ │    │ │ GORM        │ │
└─────────────────┘    │ └─────────────┘ │
                       └─────────────────┘
```

## 💻 命令行设计

### 服务端启动
```bash
# 基本启动
./monitor -s -p 8443 -pw "your-password"

# 完整参数
./monitor -s \
  -p 8443 \                      # API监听端口（必需）
  -pw "your-shared-password" \   # 加密密码（必需）
  -web-port 8080 \               # Web界面端口（可选，默认8080）
  -db "./data/monitor.db" \      # 数据库路径（可选）
  -log-level info \              # 日志级别（可选）
  -retention 30 \                # 数据保留天数（可选）
  -data-dir "./data"             # 数据目录（可选）
```

### 客户端启动
```bash
# 基本启动
./monitor -c -n "web01" -ip ************* -p 8443 -pw "your-password"

# 完整参数
./monitor -c \
  -n "web01" \                   # 服务器名称（必需）
  -ip ************* \            # 服务端IP（必需）
  -p 8443 \                      # 服务端端口（必需）
  -pw "your-shared-password" \   # 加密密码（必需，与服务端相同）
  -desc "Web服务器" \            # 服务器描述（可选）
  -tags "web,nginx,prod" \       # 标签，逗号分隔（可选）
  -interval 30 \                 # 监控间隔秒数（可选，默认30）
  -log-level info                # 日志级别（可选，默认info）
```

### 参数说明表

| 参数 | 服务端 | 客户端 | 必需 | 默认值 | 说明 |
|------|--------|--------|------|--------|------|
| `-s` | ✅ | ❌ | ✅ | - | 服务端模式 |
| `-c` | ❌ | ✅ | ✅ | - | 客户端模式 |
| `-p` | ✅ | ✅ | ✅ | - | 端口号 |
| `-pw` | ✅ | ✅ | ✅ | - | 加密密码 |
| `-n` | ❌ | ✅ | ✅ | - | 服务器名称 |
| `-ip` | ❌ | ✅ | ✅ | - | 服务端IP地址 |
| `-web-port` | ✅ | ❌ | ❌ | 8080 | Web界面端口 |
| `-db` | ✅ | ❌ | ❌ | ./data/monitor.db | 数据库路径 |
| `-desc` | ❌ | ✅ | ❌ | - | 服务器描述 |
| `-tags` | ❌ | ✅ | ❌ | - | 服务器标签 |
| `-interval` | ❌ | ✅ | ❌ | 30 | 监控间隔（秒） |
| `-log-level` | ✅ | ✅ | ❌ | info | 日志级别 |
| `-retention` | ✅ | ❌ | ❌ | 30 | 数据保留天数 |
| `-data-dir` | ✅ | ❌ | ❌ | ./data | 数据目录 |

## 🔐 AES加密方案

### 加密算法
- **算法**：AES-256-GCM
- **密钥派生**：PBKDF2 (10000次迭代)
- **认证加密**：提供数据完整性和认证

### 消息格式
```
[4字节长度][12字节Nonce][加密数据][16字节认证标签]
```

### 性能影响
| 数据大小 | 加密时间 | 解密时间 | 网络传输时间 | 总影响 |
|----------|----------|----------|--------------|--------|
| 1KB | 0.003ms | 0.003ms | 1-10ms | < 1% |
| 5KB | 0.008ms | 0.008ms | 2-15ms | < 1% |
| 10KB | 0.015ms | 0.015ms | 3-20ms | < 1% |

**结论**：加密开销极小，用户无感知，安全性大幅提升。

## 🗄️ 数据库设计

### 核心表结构

#### 1. 服务器信息表 (servers)
```sql
CREATE TABLE servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,        -- 服务器名称
    ip_address VARCHAR(45) NOT NULL,          -- IP地址
    hostname VARCHAR(255),                    -- 主机名
    os_type VARCHAR(50),                      -- 操作系统类型
    os_version VARCHAR(100),                  -- 操作系统版本
    last_seen DATETIME,                       -- 最后在线时间
    is_active BOOLEAN DEFAULT 1,              -- 是否活跃
    description TEXT,                         -- 描述
    tags TEXT,                                -- 标签(JSON格式)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 监控指标表 (server_metrics)
```sql
CREATE TABLE server_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER NOT NULL,
    cpu_usage REAL NOT NULL,                  -- CPU使用率
    memory_usage REAL NOT NULL,               -- 内存使用率
    memory_total INTEGER NOT NULL,            -- 总内存(KB)
    memory_used INTEGER NOT NULL,             -- 已用内存(KB)
    disk_usage REAL NOT NULL,                 -- 磁盘使用率
    disk_total INTEGER NOT NULL,              -- 总磁盘(KB)
    disk_used INTEGER NOT NULL,               -- 已用磁盘(KB)
    network_in_bytes INTEGER DEFAULT 0,       -- 网络入流量
    network_out_bytes INTEGER DEFAULT 0,      -- 网络出流量
    load_average_1 REAL,                      -- 1分钟负载
    load_average_5 REAL,                      -- 5分钟负载
    load_average_15 REAL,                     -- 15分钟负载
    process_count INTEGER,                    -- 进程数
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES servers(id)
);
```

#### 3. 服务信息表 (services)
```sql
CREATE TABLE services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,               -- 服务名称
    type VARCHAR(20) NOT NULL,                -- 服务类型(supervisord/systemd/docker)
    display_name VARCHAR(200),                -- 显示名称
    description TEXT,                         -- 描述
    auto_start BOOLEAN DEFAULT 0,             -- 是否自动启动
    is_monitored BOOLEAN DEFAULT 1,           -- 是否监控
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES servers(id)
);
```

#### 4. 服务状态表 (service_status)
```sql
CREATE TABLE service_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    service_id INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL,              -- 状态(running/stopped/failed)
    pid INTEGER,                              -- 进程ID
    uptime VARCHAR(50),                       -- 运行时间
    memory_usage INTEGER,                     -- 内存使用(KB)
    cpu_usage REAL,                          -- CPU使用率
    restart_count INTEGER DEFAULT 0,         -- 重启次数
    error_message TEXT,                       -- 错误信息
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES services(id)
);
```

## 🎨 前端界面设计

### 技术选择
- **模板引擎**：Go `html/template`
- **CSS框架**：Tailwind CSS
- **交互增强**：htmx + Alpine.js
- **图表库**：Chart.js
- **图标**：Heroicons

### 页面结构
```
web/
├── templates/
│   ├── layouts/
│   │   └── base.html              # 基础布局
│   ├── pages/
│   │   ├── dashboard.html         # 仪表板
│   │   ├── server-detail.html     # 服务器详情
│   │   ├── services.html          # 服务管理
│   │   └── settings.html          # 系统设置
│   └── components/
│       ├── navbar.html            # 导航栏
│       ├── server-card.html       # 服务器卡片
│       └── service-list.html      # 服务列表
├── static/
│   ├── css/
│   │   └── output.css             # Tailwind编译后的CSS
│   ├── js/
│   │   ├── dashboard.js           # 仪表板脚本
│   │   └── charts.js              # 图表脚本
│   └── images/
└── src/
    └── input.css                  # Tailwind源文件
```

## 📊 监控功能

### 系统指标监控
- **CPU使用率**：总体和分核心使用率
- **内存使用**：物理内存、交换分区使用情况
- **磁盘使用**：各挂载点的使用率和I/O统计
- **网络流量**：各网卡的入出流量统计
- **系统负载**：1分钟、5分钟、15分钟负载平均值
- **进程信息**：CPU和内存占用前N的进程

### 服务管理功能

#### Supervisord管理
```bash
# 支持的操作
supervisorctl status    # 查看状态
supervisorctl start     # 启动服务
supervisorctl stop      # 停止服务
supervisorctl restart   # 重启服务
```

#### Systemd管理
```bash
# 支持的操作
systemctl status        # 查看状态
systemctl start         # 启动服务
systemctl stop          # 停止服务
systemctl restart       # 重启服务
systemctl enable        # 启用服务
systemctl disable       # 禁用服务
```

#### Docker管理
```bash
# 支持的操作
docker ps              # 查看容器
docker start           # 启动容器
docker stop            # 停止容器
docker restart         # 重启容器
docker logs            # 查看日志
```

### 数据采集间隔
- **系统指标**：30秒（可配置）
- **服务状态**：60秒（可配置）
- **心跳检测**：30秒
- **历史数据保留**：30天（可配置）

## 🚀 部署方案

### 生产环境部署

#### 服务端部署
```bash
#!/bin/bash
# deploy-server.sh

# 创建目录
sudo mkdir -p /opt/monitor/{bin,data,logs}

# 复制程序
sudo cp monitor /opt/monitor/bin/

# 创建systemd服务
sudo tee /etc/systemd/system/monitor-server.service > /dev/null << EOF
[Unit]
Description=Server Monitor Server
After=network.target

[Service]
Type=simple
User=monitor
WorkingDirectory=/opt/monitor
ExecStart=/opt/monitor/bin/monitor -s -p 8443 -pw "ProductionPassword123" -data-dir /opt/monitor/data
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable monitor-server
sudo systemctl start monitor-server
```

#### 客户端批量部署
```bash
#!/bin/bash
# deploy-clients.sh

PASSWORD="ProductionPassword123"
SERVER_IP="*************"
SERVER_PORT="8443"

# 服务器列表
declare -A SERVERS=(
    ["web01"]="Web服务器1,web,nginx,prod"
    ["web02"]="Web服务器2,web,nginx,prod"
    ["db01"]="数据库服务器,db,mysql,prod"
    ["cache01"]="缓存服务器,cache,redis,prod"
)

for server in "${!SERVERS[@]}"; do
    IFS=',' read -r desc tags <<< "${SERVERS[$server]}"
    
    echo "部署到 $server..."
    
    # 复制程序
    scp monitor root@$server:/usr/local/bin/
    
    # 创建systemd服务
    ssh root@$server "cat > /etc/systemd/system/monitor-client.service << EOF
[Unit]
Description=Server Monitor Client
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/monitor -c -n \"$server\" -ip $SERVER_IP -p $SERVER_PORT -pw \"$PASSWORD\" -desc \"$desc\" -tags \"$tags\"
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF"
    
    # 启动服务
    ssh root@$server "systemctl daemon-reload && systemctl enable monitor-client && systemctl start monitor-client"
    
    echo "$server 部署完成"
done

echo "所有客户端部署完成！"
```

## 🔧 开发环境

### 项目结构
```
server-monitor/
├── cmd/
│   └── main.go                    # 程序入口
├── internal/
│   ├── client/                    # 客户端模块
│   ├── server/                    # 服务端模块
│   ├── crypto/                    # 加密模块
│   ├── protocol/                  # 通信协议
│   ├── models/                    # 数据模型
│   └── common/                    # 公共组件
├── web/                           # Web资源
├── configs/                       # 配置示例
├── scripts/                       # 部署脚本
├── docs/                          # 文档
├── go.mod
├── go.sum
├── Makefile
└── README.md
```

## 📈 性能指标

### 系统要求
- **CPU**：1核心以上
- **内存**：512MB以上
- **磁盘**：100MB以上
- **网络**：100Mbps以上

### 性能基准
- **并发客户端**：支持100+客户端同时连接
- **数据处理**：每秒处理1000+监控数据点
- **响应时间**：Web界面响应时间 < 100ms
- **资源占用**：服务端内存占用 < 100MB

## 🔒 安全考虑

### 数据传输安全
- **AES-256-GCM加密**：端到端加密传输
- **密钥管理**：PBKDF2密钥派生
- **消息认证**：防止数据篡改

### 访问控制
- **密码认证**：客户端和服务端共享密码
- **IP白名单**：可限制客户端IP范围
- **会话管理**：Web界面会话超时控制

### 安全建议
1. **使用强密码**：至少16位，包含大小写字母、数字、特殊字符
2. **定期更换密码**：建议每季度更换一次
3. **网络隔离**：部署在内网环境
4. **日志审计**：记录所有操作日志
5. **定期备份**：定期备份数据库文件

---

**文档版本**：v1.0  
**最后更新**：2024-01-20  
**维护者**：开发团队
