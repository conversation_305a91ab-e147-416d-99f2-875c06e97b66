package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"server-monitor/internal/alert"
	"server-monitor/internal/config"
	"server-monitor/internal/monitor"
)

func main() {
	var (
		configFile = flag.String("config", "./configs/config.yaml", "配置文件路径")
		mode       = flag.String("mode", "monitor", "运行模式: monitor(监控), rules(查看规则), history(查看历史)")
		testAlert  = flag.Bool("test", false, "触发测试告警")
	)
	flag.Parse()

	fmt.Printf("监控告警系统测试程序\n")
	fmt.Printf("配置文件: %s\n", *configFile)
	fmt.Printf("运行模式: %s\n", *mode)
	fmt.Println("---")

	// 加载配置
	configManager := config.NewManager(*configFile)
	if err := configManager.Load(); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	cfg := configManager.Get()
	fmt.Printf("配置加载成功\n")

	// 创建系统监控器
	systemMonitor := monitor.NewSystemMonitor(5 * time.Second)

	// 创建告警管理器
	alertManager := alert.NewManager(cfg, systemMonitor)

	switch *mode {
	case "rules":
		showRules(alertManager)
	case "history":
		showHistory(alertManager)
	case "monitor":
		runMonitor(alertManager, *testAlert)
	default:
		log.Fatalf("未知模式: %s", *mode)
	}
}

// showRules 显示告警规则
func showRules(manager *alert.Manager) {
	fmt.Println("=== 告警规则配置 ===")

	rules := manager.GetRules()
	if len(rules) == 0 {
		fmt.Println("暂无告警规则")
		return
	}

	for i, rule := range rules {
		fmt.Printf("\n规则 %d:\n", i+1)
		fmt.Printf("  名称: %s\n", rule.Name)
		fmt.Printf("  类型: %s\n", rule.Type)
		fmt.Printf("  阈值: %.2f %s\n", rule.Threshold, rule.Operator)
		fmt.Printf("  级别: %s\n", rule.Level.String())
		fmt.Printf("  持续时间: %d秒\n", rule.Duration)
		fmt.Printf("  状态: %s\n", getEnabledText(rule.Enabled))
		fmt.Printf("  描述: %s\n", rule.Description)
	}
}

// showHistory 显示告警历史
func showHistory(manager *alert.Manager) {
	fmt.Println("=== 告警历史记录 ===")

	history := manager.GetAlertHistory(20) // 显示最近20条
	if len(history) == 0 {
		fmt.Println("暂无告警历史")
		return
	}

	for i, alert := range history {
		fmt.Printf("\n告警 %d:\n", i+1)
		fmt.Printf("  ID: %s\n", alert.ID)
		fmt.Printf("  标题: %s\n", alert.Title)
		fmt.Printf("  级别: %s\n", alert.Level.String())
		fmt.Printf("  类型: %s\n", alert.Type)
		fmt.Printf("  主机: %s\n", alert.Hostname)
		fmt.Printf("  消息: %s\n", alert.Message)
		fmt.Printf("  时间: %s\n", alert.Timestamp.Format("2006-01-02 15:04:05"))
		fmt.Printf("  状态: %s\n", getResolvedText(alert.Resolved))
		if alert.ResolvedAt != nil {
			fmt.Printf("  解决时间: %s\n", alert.ResolvedAt.Format("2006-01-02 15:04:05"))
		}
	}
}

// runMonitor 运行监控模式
func runMonitor(manager *alert.Manager, testAlert bool) {
	fmt.Println("=== 监控告警模式 ===")

	// 启动告警管理器
	if err := manager.Start(); err != nil {
		log.Fatalf("启动告警管理器失败: %v", err)
	}

	fmt.Println("告警管理器已启动")

	if testAlert {
		// 触发测试告警
		go triggerTestAlert(manager)
	}

	// 定期显示状态
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				showStatus(manager)
			}
		}
	}()

	// 设置信号处理
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	fmt.Println("监控运行中，按 Ctrl+C 停止...")

	// 等待信号
	<-sigCh
	fmt.Println("\n收到停止信号，正在停止监控...")

	// 停止告警管理器
	manager.Stop()
	fmt.Println("监控告警系统已停止")
}

// showStatus 显示当前状态
func showStatus(manager *alert.Manager) {
	fmt.Println("\n=== 当前状态 ===")
	fmt.Printf("时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	// 显示活跃告警
	activeAlerts := manager.GetActiveAlerts()
	fmt.Printf("活跃告警数量: %d\n", len(activeAlerts))

	if len(activeAlerts) > 0 {
		fmt.Println("\n🚨 活跃告警:")
		for i, alert := range activeAlerts {
			fmt.Printf("  %d. [%s] %s - %s\n",
				i+1, alert.Level.String(), alert.Title, alert.Message)
		}
	}

	// 显示告警历史统计
	history := manager.GetAlertHistory(100)
	fmt.Printf("历史告警总数: %d\n", len(history))

	// 统计各级别告警数量
	var infoCount, warningCount, criticalCount int
	for _, alertItem := range history {
		switch alertItem.Level {
		case alert.LevelInfo:
			infoCount++
		case alert.LevelWarning:
			warningCount++
		case alert.LevelCritical:
			criticalCount++
		}
	}

	fmt.Printf("告警级别统计: 信息(%d) 警告(%d) 严重(%d)\n",
		infoCount, warningCount, criticalCount)
	fmt.Println("---")
}

// triggerTestAlert 触发测试告警
func triggerTestAlert(manager *alert.Manager) {
	fmt.Println("⚠️  准备触发测试告警...")

	// 等待5秒
	time.Sleep(5 * time.Second)

	// 添加一个测试规则（阈值很低，容易触发）
	testRule := alert.Rule{
		Name:        "测试告警规则",
		Type:        "cpu",
		Threshold:   0.1, // 很低的阈值，容易触发
		Operator:    ">",
		Level:       alert.LevelWarning,
		Duration:    5, // 5秒
		Enabled:     true,
		Description: "这是一个测试告警规则",
	}

	manager.AddRule(testRule)
	fmt.Println("✅ 测试告警规则已添加")
	fmt.Println("💡 提示: 该规则将在CPU使用率超过0.1%持续5秒后触发告警")
}

// getEnabledText 获取启用状态文本
func getEnabledText(enabled bool) string {
	if enabled {
		return "启用"
	}
	return "禁用"
}

// getResolvedText 获取解决状态文本
func getResolvedText(resolved bool) string {
	if resolved {
		return "已解决"
	}
	return "活跃"
}

// 添加一个JSON输出模式
func outputJSON(data interface{}) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		log.Printf("JSON序列化失败: %v", err)
		return
	}
	fmt.Println(string(jsonData))
}
