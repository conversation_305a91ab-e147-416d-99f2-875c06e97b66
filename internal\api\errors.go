package api

import (
	"fmt"
	"net/http"
	"time"

	"server-monitor/internal/database"
	"server-monitor/internal/logger"

	"github.com/gin-gonic/gin"
)

// ErrorCode 错误码类型
type ErrorCode string

// 预定义错误码
const (
	// 通用错误码
	ErrCodeInternalServer  ErrorCode = "INTERNAL_SERVER_ERROR"
	ErrCodeBadRequest      ErrorCode = "BAD_REQUEST"
	ErrCodeUnauthorized    ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden       ErrorCode = "FORBIDDEN"
	ErrCodeNotFound        ErrorCode = "NOT_FOUND"
	ErrCodeConflict        ErrorCode = "CONFLICT"
	ErrCodeTooManyRequests ErrorCode = "TOO_MANY_REQUESTS"
	ErrCodeRequestTooLarge ErrorCode = "REQUEST_TOO_LARGE"

	// 验证错误码
	ErrCodeValidationFailed ErrorCode = "VALIDATION_FAILED"
	ErrCodeInvalidFormat    ErrorCode = "INVALID_FORMAT"
	ErrCodeMissingParameter ErrorCode = "MISSING_PARAMETER"
	ErrCodeInvalidParameter ErrorCode = "INVALID_PARAMETER"

	// 业务错误码
	ErrCodeServerNotFound   ErrorCode = "SERVER_NOT_FOUND"
	ErrCodeServerExists     ErrorCode = "SERVER_EXISTS"
	ErrCodeAlertNotFound    ErrorCode = "ALERT_NOT_FOUND"
	ErrCodeInvalidTimeRange ErrorCode = "INVALID_TIME_RANGE"
	ErrCodeDatabaseError    ErrorCode = "DATABASE_ERROR"

	// 系统错误码
	ErrCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE"
	ErrCodeTimeout            ErrorCode = "TIMEOUT"
	ErrCodeRateLimited        ErrorCode = "RATE_LIMITED"
)

// APIError API错误结构
type APIError struct {
	Code       ErrorCode   `json:"code"`
	Message    string      `json:"message"`
	Details    interface{} `json:"details,omitempty"`
	RequestID  string      `json:"request_id,omitempty"`
	Timestamp  int64       `json:"timestamp"`
	HTTPStatus int         `json:"-"`
}

// Error 实现error接口
func (e *APIError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// ErrorResponse 标准错误响应结构
type ErrorResponse struct {
	Success bool     `json:"success"`
	Error   APIError `json:"error"`
}

// NewAPIError 创建新的API错误
func NewAPIError(code ErrorCode, message string, httpStatus int) *APIError {
	return &APIError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
		Timestamp:  getCurrentTimestamp(),
	}
}

// WithDetails 添加错误详情
func (e *APIError) WithDetails(details interface{}) *APIError {
	e.Details = details
	return e
}

// WithRequestID 添加请求ID
func (e *APIError) WithRequestID(requestID string) *APIError {
	e.RequestID = requestID
	return e
}

// 预定义的常用错误

// NewBadRequestError 创建400错误
func NewBadRequestError(message string) *APIError {
	return NewAPIError(ErrCodeBadRequest, message, http.StatusBadRequest)
}

// NewValidationError 创建验证错误
func NewValidationError(message string, details interface{}) *APIError {
	return NewAPIError(ErrCodeValidationFailed, message, http.StatusBadRequest).WithDetails(details)
}

// NewNotFoundError 创建404错误
func NewNotFoundError(resource string) *APIError {
	message := fmt.Sprintf("%s not found", resource)
	return NewAPIError(ErrCodeNotFound, message, http.StatusNotFound)
}

// NewUnauthorizedError 创建401错误
func NewUnauthorizedError(message string) *APIError {
	if message == "" {
		message = "Authentication required"
	}
	return NewAPIError(ErrCodeUnauthorized, message, http.StatusUnauthorized)
}

// NewForbiddenError 创建403错误
func NewForbiddenError(message string) *APIError {
	if message == "" {
		message = "Access forbidden"
	}
	return NewAPIError(ErrCodeForbidden, message, http.StatusForbidden)
}

// NewConflictError 创建409错误
func NewConflictError(message string) *APIError {
	return NewAPIError(ErrCodeConflict, message, http.StatusConflict)
}

// NewInternalServerError 创建500错误
func NewInternalServerError(message string) *APIError {
	if message == "" {
		message = "Internal server error"
	}
	return NewAPIError(ErrCodeInternalServer, message, http.StatusInternalServerError)
}

// NewRateLimitError 创建429错误
func NewRateLimitError() *APIError {
	return NewAPIError(ErrCodeRateLimited, "Too many requests", http.StatusTooManyRequests)
}

// NewRequestTooLargeError 创建413错误
func NewRequestTooLargeError(maxSize int64) *APIError {
	message := fmt.Sprintf("Request body too large, maximum size is %d bytes", maxSize)
	return NewAPIError(ErrCodeRequestTooLarge, message, http.StatusRequestEntityTooLarge)
}

// 业务特定错误

// NewServerNotFoundError 服务器未找到错误
func NewServerNotFoundError(serverID interface{}) *APIError {
	message := fmt.Sprintf("Server with ID %v not found", serverID)
	return NewAPIError(ErrCodeServerNotFound, message, http.StatusNotFound)
}

// NewAlertNotFoundError 告警未找到错误
func NewAlertNotFoundError(alertID interface{}) *APIError {
	message := fmt.Sprintf("Alert with ID %v not found", alertID)
	return NewAPIError(ErrCodeAlertNotFound, message, http.StatusNotFound)
}

// NewDatabaseError 数据库错误
func NewDatabaseError(err error) *APIError {
	// 根据数据库错误类型返回不同的API错误
	if err == database.ErrRecordNotFound {
		return NewAPIError(ErrCodeNotFound, "Record not found", http.StatusNotFound)
	}

	// 记录数据库错误日志
	logger.Error("Database error: %v", err)

	return NewAPIError(ErrCodeDatabaseError, "Database operation failed", http.StatusInternalServerError)
}

// 错误处理中间件和辅助函数

// ErrorHandlerMiddleware 全局错误处理中间件
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 处理panic恢复
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			// 获取请求ID
			requestID := getRequestID(c)

			// 根据错误类型处理
			var apiErr *APIError
			if apiError, ok := err.Err.(*APIError); ok {
				apiErr = apiError.WithRequestID(requestID)
			} else {
				// 未知错误，返回内部服务器错误
				logger.Error("Unhandled error: %v", err.Err)
				apiErr = NewInternalServerError("An unexpected error occurred").WithRequestID(requestID)
			}

			SendErrorResponse(c, apiErr)
		}
	}
}

// SendErrorResponse 发送错误响应
func SendErrorResponse(c *gin.Context, apiErr *APIError) {
	// 添加请求ID
	if apiErr.RequestID == "" {
		apiErr.RequestID = getRequestID(c)
	}

	// 记录错误日志
	logError(c, apiErr)

	// 发送响应
	c.JSON(apiErr.HTTPStatus, ErrorResponse{
		Success: false,
		Error:   *apiErr,
	})
}

// HandleError 处理错误的辅助函数
func HandleError(c *gin.Context, err error) {
	var apiErr *APIError

	switch e := err.(type) {
	case *APIError:
		apiErr = e
	default:
		// 根据错误类型进行转换
		if err == database.ErrRecordNotFound {
			apiErr = NewNotFoundError("Resource")
		} else {
			logger.Error("Unhandled error: %v", err)
			apiErr = NewInternalServerError("")
		}
	}

	SendErrorResponse(c, apiErr)
}

// 辅助函数

// getRequestID 获取请求ID
func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// getCurrentTimestamp 获取当前时间戳
func getCurrentTimestamp() int64 {
	return getCurrentTime().Unix()
}

// getCurrentTime 获取当前时间（便于测试时mock）
var getCurrentTime = func() time.Time {
	return time.Now()
}

// logError 记录错误日志
func logError(c *gin.Context, apiErr *APIError) {
	logData := map[string]interface{}{
		"request_id": apiErr.RequestID,
		"error_code": apiErr.Code,
		"message":    apiErr.Message,
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"client_ip":  c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
	}

	if apiErr.Details != nil {
		logData["details"] = apiErr.Details
	}

	// 根据HTTP状态码选择日志级别
	if apiErr.HTTPStatus >= 500 {
		logger.Error("API Error - %v", logData)
	} else if apiErr.HTTPStatus >= 400 {
		logger.Warn("API Error - %v", logData)
	} else {
		logger.Info("API Error - %v", logData)
	}
}

// 错误码映射（用于国际化）
var errorMessages = map[ErrorCode]map[string]string{
	ErrCodeBadRequest: {
		"en": "Bad request",
		"zh": "请求参数错误",
	},
	ErrCodeUnauthorized: {
		"en": "Authentication required",
		"zh": "需要身份验证",
	},
	ErrCodeForbidden: {
		"en": "Access forbidden",
		"zh": "访问被禁止",
	},
	ErrCodeNotFound: {
		"en": "Resource not found",
		"zh": "资源未找到",
	},
	ErrCodeInternalServer: {
		"en": "Internal server error",
		"zh": "内部服务器错误",
	},
	ErrCodeValidationFailed: {
		"en": "Validation failed",
		"zh": "数据验证失败",
	},
	ErrCodeServerNotFound: {
		"en": "Server not found",
		"zh": "服务器未找到",
	},
	ErrCodeAlertNotFound: {
		"en": "Alert not found",
		"zh": "告警未找到",
	},
}

// GetLocalizedMessage 获取本地化错误消息
func GetLocalizedMessage(code ErrorCode, lang string) string {
	if messages, exists := errorMessages[code]; exists {
		if message, exists := messages[lang]; exists {
			return message
		}
		// 默认返回英文
		if message, exists := messages["en"]; exists {
			return message
		}
	}
	return string(code)
}
