package pool

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"
)

// Connection 连接接口
type Connection interface {
	Close() error
	IsAlive() bool
	LastUsed() time.Time
	SetLastUsed(time.Time)
}

// TCPConnection TCP连接实现
type TCPConnection struct {
	conn     net.Conn
	lastUsed time.Time
	mu       sync.RWMutex
}

// NewTCPConnection 创建TCP连接
func NewTCPConnection(conn net.Conn) *TCPConnection {
	return &TCPConnection{
		conn:     conn,
		lastUsed: time.Now(),
	}
}

// Close 关闭连接
func (c *TCPConnection) Close() error {
	return c.conn.Close()
}

// IsAlive 检查连接是否存活
func (c *TCPConnection) IsAlive() bool {
	if c.conn == nil {
		return false
	}

	// 设置很短的超时时间来检查连接
	c.conn.SetReadDeadline(time.Now().Add(time.Millisecond))
	defer c.conn.SetReadDeadline(time.Time{})

	// 尝试读取一个字节
	one := make([]byte, 1)
	_, err := c.conn.Read(one)
	
	// 如果是超时错误，说明连接正常但没有数据
	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		return true
	}
	
	// 其他错误说明连接有问题
	return err == nil
}

// LastUsed 获取最后使用时间
func (c *TCPConnection) LastUsed() time.Time {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.lastUsed
}

// SetLastUsed 设置最后使用时间
func (c *TCPConnection) SetLastUsed(t time.Time) {
	c.mu.Lock()
	c.lastUsed = t
	c.mu.Unlock()
}

// GetConn 获取底层连接
func (c *TCPConnection) GetConn() net.Conn {
	return c.conn
}

// Pool 连接池
type Pool struct {
	// 连接工厂函数
	factory func() (Connection, error)
	
	// 连接池配置
	config *Config
	
	// 连接队列
	connections chan Connection
	
	// 活跃连接数
	activeCount int
	
	// 互斥锁
	mu sync.RWMutex
	
	// 是否已关闭
	closed bool
	
	// 清理器
	cleaner *time.Ticker
}

// Config 连接池配置
type Config struct {
	MaxIdle     int           // 最大空闲连接数
	MaxActive   int           // 最大活跃连接数
	IdleTimeout time.Duration // 空闲超时时间
	MaxLifetime time.Duration // 连接最大生存时间
	TestOnGet   bool          // 获取时是否测试连接
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		MaxIdle:     10,
		MaxActive:   100,
		IdleTimeout: 5 * time.Minute,
		MaxLifetime: 30 * time.Minute,
		TestOnGet:   true,
	}
}

// NewPool 创建连接池
func NewPool(factory func() (Connection, error), config *Config) *Pool {
	if config == nil {
		config = DefaultConfig()
	}

	pool := &Pool{
		factory:     factory,
		config:      config,
		connections: make(chan Connection, config.MaxIdle),
		closed:      false,
	}

	// 启动清理器
	pool.cleaner = time.NewTicker(time.Minute)
	go pool.cleanup()

	return pool
}

// Get 获取连接
func (p *Pool) Get(ctx context.Context) (Connection, error) {
	p.mu.RLock()
	if p.closed {
		p.mu.RUnlock()
		return nil, fmt.Errorf("连接池已关闭")
	}
	p.mu.RUnlock()

	// 尝试从池中获取连接
	select {
	case conn := <-p.connections:
		// 测试连接是否可用
		if p.config.TestOnGet && !conn.IsAlive() {
			conn.Close()
			return p.createConnection()
		}
		
		conn.SetLastUsed(time.Now())
		p.incrementActive()
		return conn, nil
	default:
		// 池中没有连接，创建新连接
		return p.createConnection()
	}
}

// Put 归还连接
func (p *Pool) Put(conn Connection) error {
	if conn == nil {
		return fmt.Errorf("连接为空")
	}

	p.mu.RLock()
	if p.closed {
		p.mu.RUnlock()
		conn.Close()
		return fmt.Errorf("连接池已关闭")
	}
	p.mu.RUnlock()

	// 检查连接是否还活着
	if !conn.IsAlive() {
		conn.Close()
		p.decrementActive()
		return nil
	}

	// 检查连接是否超过最大生存时间
	if p.config.MaxLifetime > 0 {
		if time.Since(conn.LastUsed()) > p.config.MaxLifetime {
			conn.Close()
			p.decrementActive()
			return nil
		}
	}

	// 尝试放回池中
	select {
	case p.connections <- conn:
		p.decrementActive()
		return nil
	default:
		// 池已满，关闭连接
		conn.Close()
		p.decrementActive()
		return nil
	}
}

// createConnection 创建新连接
func (p *Pool) createConnection() (Connection, error) {
	p.mu.RLock()
	if p.config.MaxActive > 0 && p.activeCount >= p.config.MaxActive {
		p.mu.RUnlock()
		return nil, fmt.Errorf("连接池已达到最大活跃连接数")
	}
	p.mu.RUnlock()

	conn, err := p.factory()
	if err != nil {
		return nil, err
	}

	p.incrementActive()
	return conn, nil
}

// incrementActive 增加活跃连接数
func (p *Pool) incrementActive() {
	p.mu.Lock()
	p.activeCount++
	p.mu.Unlock()
}

// decrementActive 减少活跃连接数
func (p *Pool) decrementActive() {
	p.mu.Lock()
	if p.activeCount > 0 {
		p.activeCount--
	}
	p.mu.Unlock()
}

// cleanup 清理过期连接
func (p *Pool) cleanup() {
	for range p.cleaner.C {
		p.mu.RLock()
		if p.closed {
			p.mu.RUnlock()
			return
		}
		p.mu.RUnlock()

		// 清理空闲超时的连接
		if p.config.IdleTimeout > 0 {
			p.cleanupIdleConnections()
		}
	}
}

// cleanupIdleConnections 清理空闲连接
func (p *Pool) cleanupIdleConnections() {
	now := time.Now()
	var toClose []Connection

	// 从池中取出所有连接检查
	for {
		select {
		case conn := <-p.connections:
			if now.Sub(conn.LastUsed()) > p.config.IdleTimeout || !conn.IsAlive() {
				toClose = append(toClose, conn)
			} else {
				// 放回池中
				select {
				case p.connections <- conn:
				default:
					// 池满了，关闭连接
					toClose = append(toClose, conn)
				}
			}
		default:
			// 池中没有更多连接
			goto cleanup
		}
	}

cleanup:
	// 关闭需要清理的连接
	for _, conn := range toClose {
		conn.Close()
	}
}

// Stats 获取连接池统计信息
func (p *Pool) Stats() map[string]interface{} {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return map[string]interface{}{
		"active_count": p.activeCount,
		"idle_count":   len(p.connections),
		"max_active":   p.config.MaxActive,
		"max_idle":     p.config.MaxIdle,
		"closed":       p.closed,
	}
}

// Close 关闭连接池
func (p *Pool) Close() error {
	p.mu.Lock()
	if p.closed {
		p.mu.Unlock()
		return nil
	}
	p.closed = true
	p.mu.Unlock()

	// 停止清理器
	p.cleaner.Stop()

	// 关闭所有连接
	close(p.connections)
	for conn := range p.connections {
		conn.Close()
	}

	return nil
}

// TCPPoolFactory TCP连接池工厂
func TCPPoolFactory(network, address string) func() (Connection, error) {
	return func() (Connection, error) {
		conn, err := net.Dial(network, address)
		if err != nil {
			return nil, err
		}
		return NewTCPConnection(conn), nil
	}
}
