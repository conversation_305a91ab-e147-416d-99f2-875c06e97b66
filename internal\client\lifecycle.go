package client

import (
	"context"
	"fmt"
	"sync"
	"time"

	"server-monitor/internal/logger"
)

// LifecyclePhase 生命周期阶段
type LifecyclePhase int

const (
	PhaseInitializing LifecyclePhase = iota
	PhaseStarting
	PhaseRunning
	PhaseStopping
	PhaseStopped
	PhaseError
)

// String 返回阶段字符串
func (p LifecyclePhase) String() string {
	switch p {
	case PhaseInitializing:
		return "初始化中"
	case PhaseStarting:
		return "启动中"
	case PhaseRunning:
		return "运行中"
	case PhaseStopping:
		return "停止中"
	case PhaseStopped:
		return "已停止"
	case PhaseError:
		return "错误"
	default:
		return "未知"
	}
}

// LifecycleEvent 生命周期事件
type LifecycleEvent struct {
	Phase     LifecyclePhase `json:"phase"`
	Timestamp time.Time      `json:"timestamp"`
	Message   string         `json:"message"`
	Error     error          `json:"error,omitempty"`
}

// LifecycleListener 生命周期监听器
type LifecycleListener interface {
	OnPhaseChanged(event *LifecycleEvent)
}

// Component 组件接口
type Component interface {
	Name() string
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	IsHealthy() bool
}

// LifecycleManager 生命周期管理器
type LifecycleManager struct {
	// 基本信息
	name    string
	logger  *logger.Logger
	
	// 状态管理
	phase   LifecyclePhase
	phaseMu sync.RWMutex
	
	// 组件管理
	components []Component
	compMu     sync.RWMutex
	
	// 事件管理
	listeners []LifecycleListener
	events    []*LifecycleEvent
	eventMu   sync.RWMutex
	
	// 上下文管理
	ctx    context.Context
	cancel context.CancelFunc
	
	// 错误处理
	lastError error
	errorMu   sync.RWMutex
	
	// 统计信息
	startTime time.Time
	stopTime  time.Time
}

// NewLifecycleManager 创建生命周期管理器
func NewLifecycleManager(name string, logger *logger.Logger) *LifecycleManager {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &LifecycleManager{
		name:       name,
		logger:     logger,
		phase:      PhaseInitializing,
		components: make([]Component, 0),
		listeners:  make([]LifecycleListener, 0),
		events:     make([]*LifecycleEvent, 0),
		ctx:        ctx,
		cancel:     cancel,
	}
}

// AddComponent 添加组件
func (lm *LifecycleManager) AddComponent(component Component) {
	lm.compMu.Lock()
	defer lm.compMu.Unlock()
	
	lm.components = append(lm.components, component)
	lm.logger.Debug("添加组件: %s", component.Name())
}

// AddListener 添加监听器
func (lm *LifecycleManager) AddListener(listener LifecycleListener) {
	lm.eventMu.Lock()
	defer lm.eventMu.Unlock()
	
	lm.listeners = append(lm.listeners, listener)
}

// Start 启动生命周期管理器
func (lm *LifecycleManager) Start() error {
	lm.phaseMu.Lock()
	if lm.phase != PhaseInitializing {
		lm.phaseMu.Unlock()
		return fmt.Errorf("生命周期管理器已启动或处于错误状态")
	}
	lm.phase = PhaseStarting
	lm.phaseMu.Unlock()
	
	lm.startTime = time.Now()
	lm.emitEvent(PhaseStarting, "开始启动生命周期管理器", nil)
	
	// 启动所有组件
	lm.compMu.RLock()
	components := make([]Component, len(lm.components))
	copy(components, lm.components)
	lm.compMu.RUnlock()
	
	for _, component := range components {
		lm.logger.Info("启动组件: %s", component.Name())
		
		if err := component.Start(lm.ctx); err != nil {
			lm.setError(fmt.Errorf("启动组件 %s 失败: %w", component.Name(), err))
			lm.emitEvent(PhaseError, fmt.Sprintf("启动组件 %s 失败", component.Name()), err)
			return err
		}
		
		lm.logger.Info("组件 %s 启动成功", component.Name())
	}
	
	lm.phaseMu.Lock()
	lm.phase = PhaseRunning
	lm.phaseMu.Unlock()
	
	lm.emitEvent(PhaseRunning, "生命周期管理器启动完成", nil)
	lm.logger.Info("生命周期管理器启动完成")
	
	return nil
}

// Stop 停止生命周期管理器
func (lm *LifecycleManager) Stop() error {
	lm.phaseMu.Lock()
	if lm.phase != PhaseRunning {
		lm.phaseMu.Unlock()
		return fmt.Errorf("生命周期管理器未在运行")
	}
	lm.phase = PhaseStopping
	lm.phaseMu.Unlock()
	
	lm.emitEvent(PhaseStopping, "开始停止生命周期管理器", nil)
	
	// 取消上下文
	lm.cancel()
	
	// 停止所有组件（逆序）
	lm.compMu.RLock()
	components := make([]Component, len(lm.components))
	copy(components, lm.components)
	lm.compMu.RUnlock()
	
	// 创建停止上下文，设置超时
	stopCtx, stopCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer stopCancel()
	
	for i := len(components) - 1; i >= 0; i-- {
		component := components[i]
		lm.logger.Info("停止组件: %s", component.Name())
		
		if err := component.Stop(stopCtx); err != nil {
			lm.logger.Error("停止组件 %s 失败: %v", component.Name(), err)
			// 继续停止其他组件，不返回错误
		} else {
			lm.logger.Info("组件 %s 停止成功", component.Name())
		}
	}
	
	lm.stopTime = time.Now()
	
	lm.phaseMu.Lock()
	lm.phase = PhaseStopped
	lm.phaseMu.Unlock()
	
	lm.emitEvent(PhaseStopped, "生命周期管理器停止完成", nil)
	lm.logger.Info("生命周期管理器停止完成")
	
	return nil
}

// GetPhase 获取当前阶段
func (lm *LifecycleManager) GetPhase() LifecyclePhase {
	lm.phaseMu.RLock()
	defer lm.phaseMu.RUnlock()
	return lm.phase
}

// IsRunning 检查是否在运行
func (lm *LifecycleManager) IsRunning() bool {
	return lm.GetPhase() == PhaseRunning
}

// GetUptime 获取运行时间
func (lm *LifecycleManager) GetUptime() time.Duration {
	if lm.startTime.IsZero() {
		return 0
	}
	
	if !lm.stopTime.IsZero() {
		return lm.stopTime.Sub(lm.startTime)
	}
	
	return time.Since(lm.startTime)
}

// GetLastError 获取最后一个错误
func (lm *LifecycleManager) GetLastError() error {
	lm.errorMu.RLock()
	defer lm.errorMu.RUnlock()
	return lm.lastError
}

// GetEvents 获取事件历史
func (lm *LifecycleManager) GetEvents() []*LifecycleEvent {
	lm.eventMu.RLock()
	defer lm.eventMu.RUnlock()
	
	events := make([]*LifecycleEvent, len(lm.events))
	copy(events, lm.events)
	return events
}

// GetComponentStatus 获取组件状态
func (lm *LifecycleManager) GetComponentStatus() map[string]bool {
	lm.compMu.RLock()
	defer lm.compMu.RUnlock()
	
	status := make(map[string]bool)
	for _, component := range lm.components {
		status[component.Name()] = component.IsHealthy()
	}
	
	return status
}

// HealthCheck 健康检查
func (lm *LifecycleManager) HealthCheck() map[string]interface{} {
	return map[string]interface{}{
		"name":             lm.name,
		"phase":            lm.GetPhase().String(),
		"uptime":           lm.GetUptime().String(),
		"component_count":  len(lm.components),
		"component_status": lm.GetComponentStatus(),
		"last_error":       lm.GetLastError(),
		"event_count":      len(lm.events),
	}
}

// setError 设置错误
func (lm *LifecycleManager) setError(err error) {
	lm.errorMu.Lock()
	lm.lastError = err
	lm.errorMu.Unlock()
	
	lm.phaseMu.Lock()
	lm.phase = PhaseError
	lm.phaseMu.Unlock()
}

// emitEvent 发出事件
func (lm *LifecycleManager) emitEvent(phase LifecyclePhase, message string, err error) {
	event := &LifecycleEvent{
		Phase:     phase,
		Timestamp: time.Now(),
		Message:   message,
		Error:     err,
	}
	
	lm.eventMu.Lock()
	lm.events = append(lm.events, event)
	
	// 限制事件数量，保留最近的100个
	if len(lm.events) > 100 {
		lm.events = lm.events[len(lm.events)-100:]
	}
	
	listeners := make([]LifecycleListener, len(lm.listeners))
	copy(listeners, lm.listeners)
	lm.eventMu.Unlock()
	
	// 通知监听器
	for _, listener := range listeners {
		go func(l LifecycleListener) {
			defer func() {
				if r := recover(); r != nil {
					lm.logger.Error("生命周期监听器异常: %v", r)
				}
			}()
			l.OnPhaseChanged(event)
		}(listener)
	}
	
	lm.logger.Debug("生命周期事件: %s - %s", phase.String(), message)
}

// Wait 等待生命周期结束
func (lm *LifecycleManager) Wait() {
	for {
		phase := lm.GetPhase()
		if phase == PhaseStopped || phase == PhaseError {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}
}

// Restart 重启生命周期管理器
func (lm *LifecycleManager) Restart() error {
	if lm.IsRunning() {
		if err := lm.Stop(); err != nil {
			return fmt.Errorf("停止失败: %w", err)
		}
	}
	
	// 等待完全停止
	lm.Wait()
	
	// 重新创建上下文
	lm.ctx, lm.cancel = context.WithCancel(context.Background())
	
	// 重置状态
	lm.phaseMu.Lock()
	lm.phase = PhaseInitializing
	lm.phaseMu.Unlock()
	
	lm.errorMu.Lock()
	lm.lastError = nil
	lm.errorMu.Unlock()
	
	// 重新启动
	return lm.Start()
}
