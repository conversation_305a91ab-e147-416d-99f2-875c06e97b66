package crypto

import (
	"fmt"
	"sync"
	"time"

	lru "github.com/hashicorp/golang-lru/v2"
)

var (
	// globalReplayProtector 全局重放攻击防护器实例 (Global replay protector instance)
	globalReplayProtector *ReplayProtector

	// globalReplayProtectorOnce 确保全局实例只初始化一次 (Ensures global instance is initialized only once)
	globalReplayProtectorOnce sync.Once
)

const (
	// DefaultTimeWindow 默认时间窗口 (Default time window)
	DefaultTimeWindow = 5 * time.Minute

	// DefaultCacheSize 默认缓存大小 (Default cache size)
	DefaultCacheSize = 10000

	// ClockSkewTolerance 时钟偏差容忍度 (Clock skew tolerance)
	ClockSkewTolerance = 30 * time.Second
)

var (
	// ErrMessageExpired 消息已过期错误 (Message expired error)
	ErrMessageExpired = fmt.Errorf("message has expired")

	// ErrReplayAttack 重放攻击错误 (Replay attack error)
	ErrReplayAttack = fmt.Errorf("replay attack detected")

	// ErrInvalidTimestamp 无效时间戳错误 (Invalid timestamp error)
	ErrInvalidTimestamp = fmt.Errorf("invalid timestamp")
)

// ReplayProtectorConfig 重放攻击防护配置 (Replay protector configuration)
type ReplayProtectorConfig struct {
	// TimeWindow 时间窗口大小 (Time window size)
	TimeWindow time.Duration

	// CacheSize nonce缓存大小 (Nonce cache size)
	CacheSize int

	// ClockSkewTolerance 时钟偏差容忍度 (Clock skew tolerance)
	ClockSkewTolerance time.Duration

	// EnableCleanup 是否启用定期清理 (Whether to enable periodic cleanup)
	EnableCleanup bool

	// CleanupInterval 清理间隔 (Cleanup interval)
	CleanupInterval time.Duration
}

// DefaultReplayProtectorConfig 返回默认配置 (Returns default configuration)
func DefaultReplayProtectorConfig() *ReplayProtectorConfig {
	return &ReplayProtectorConfig{
		TimeWindow:         DefaultTimeWindow,
		CacheSize:          DefaultCacheSize,
		ClockSkewTolerance: ClockSkewTolerance,
		EnableCleanup:      true,
		CleanupInterval:    time.Minute,
	}
}

// ReplayProtector 重放攻击防护器 (Replay attack protector)
type ReplayProtector struct {
	config     *ReplayProtectorConfig
	nonceCache *lru.Cache[string, time.Time]
	mutex      sync.RWMutex
	stopCh     chan struct{}
	stopped    bool
}

// NewReplayProtector 创建新的重放攻击防护器 (Creates a new replay attack protector)
func NewReplayProtector(config *ReplayProtectorConfig) (*ReplayProtector, error) {
	if config == nil {
		config = DefaultReplayProtectorConfig()
	}

	// 创建LRU缓存 (Create LRU cache)
	cache, err := lru.New[string, time.Time](config.CacheSize)
	if err != nil {
		return nil, fmt.Errorf("failed to create LRU cache: %w", err)
	}

	rp := &ReplayProtector{
		config:     config,
		nonceCache: cache,
		stopCh:     make(chan struct{}),
	}

	// 启动定期清理 (Start periodic cleanup)
	if config.EnableCleanup {
		go rp.cleanupLoop()
	}

	return rp, nil
}

// ValidateMessage 验证消息是否为重放攻击 (Validates if message is a replay attack)
func (rp *ReplayProtector) ValidateMessage(messageID string, timestamp int64) error {
	rp.mutex.Lock()
	defer rp.mutex.Unlock()

	if rp.stopped {
		return fmt.Errorf("replay protector has been stopped")
	}

	// 验证时间戳 (Validate timestamp)
	msgTime := time.Unix(0, timestamp)
	now := time.Now()

	// 检查消息是否过期 (Check if message is expired)
	if now.Sub(msgTime) > rp.config.TimeWindow {
		return ErrMessageExpired
	}

	// 检查消息是否来自未来（考虑时钟偏差）(Check if message is from future, considering clock skew)
	if msgTime.Sub(now) > rp.config.ClockSkewTolerance {
		return ErrInvalidTimestamp
	}

	// 检查nonce是否已存在 (Check if nonce already exists)
	if existingTime, exists := rp.nonceCache.Get(messageID); exists {
		// 记录重放攻击尝试 (Log replay attack attempt)
		return fmt.Errorf("%w: message ID %s already seen at %v", ErrReplayAttack, messageID, existingTime)
	}

	// 添加nonce到缓存 (Add nonce to cache)
	rp.nonceCache.Add(messageID, msgTime)

	return nil
}

// ValidateMessageWithID 使用安全消息ID验证消息 (Validates message using secure message ID)
func (rp *ReplayProtector) ValidateMessageWithID(messageID string) error {
	// 从消息ID中提取时间戳 (Extract timestamp from message ID)
	timestamp, err := GetIDTimestamp(messageID)
	if err != nil {
		return fmt.Errorf("failed to extract timestamp from message ID: %w", err)
	}

	return rp.ValidateMessage(messageID, timestamp.UnixNano())
}

// IsMessageSeen 检查消息是否已经见过 (Checks if message has been seen)
func (rp *ReplayProtector) IsMessageSeen(messageID string) bool {
	rp.mutex.RLock()
	defer rp.mutex.RUnlock()

	_, exists := rp.nonceCache.Get(messageID)
	return exists
}

// GetCacheStats 获取缓存统计信息 (Gets cache statistics)
func (rp *ReplayProtector) GetCacheStats() (size int, capacity int) {
	rp.mutex.RLock()
	defer rp.mutex.RUnlock()

	return rp.nonceCache.Len(), rp.config.CacheSize
}

// ClearCache 清空缓存 (Clears the cache)
func (rp *ReplayProtector) ClearCache() {
	rp.mutex.Lock()
	defer rp.mutex.Unlock()

	rp.nonceCache.Purge()
}

// Stop 停止重放攻击防护器 (Stops the replay protector)
func (rp *ReplayProtector) Stop() {
	rp.mutex.Lock()
	defer rp.mutex.Unlock()

	if !rp.stopped {
		rp.stopped = true
		close(rp.stopCh)
	}
}

// cleanupLoop 定期清理过期的nonce (Periodically cleans up expired nonces)
func (rp *ReplayProtector) cleanupLoop() {
	ticker := time.NewTicker(rp.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rp.cleanupExpiredNonces()
		case <-rp.stopCh:
			return
		}
	}
}

// cleanupExpiredNonces 清理过期的nonce (Cleans up expired nonces)
func (rp *ReplayProtector) cleanupExpiredNonces() {
	rp.mutex.Lock()
	defer rp.mutex.Unlock()

	if rp.stopped {
		return
	}

	now := time.Now()
	expiredKeys := make([]string, 0)

	// 遍历缓存找到过期的条目 (Iterate through cache to find expired entries)
	keys := rp.nonceCache.Keys()
	for _, key := range keys {
		if timestamp, exists := rp.nonceCache.Peek(key); exists {
			if now.Sub(timestamp) > rp.config.TimeWindow {
				expiredKeys = append(expiredKeys, key)
			}
		}
	}

	// 删除过期的条目 (Remove expired entries)
	for _, key := range expiredKeys {
		rp.nonceCache.Remove(key)
	}
}

// UpdateConfig 更新配置 (Updates configuration)
func (rp *ReplayProtector) UpdateConfig(config *ReplayProtectorConfig) error {
	rp.mutex.Lock()
	defer rp.mutex.Unlock()

	if rp.stopped {
		return fmt.Errorf("cannot update config: replay protector has been stopped")
	}

	// 如果缓存大小改变，需要重新创建缓存 (If cache size changes, need to recreate cache)
	if config.CacheSize != rp.config.CacheSize {
		newCache, err := lru.New[string, time.Time](config.CacheSize)
		if err != nil {
			return fmt.Errorf("failed to create new LRU cache: %w", err)
		}

		// 迁移现有数据 (Migrate existing data)
		keys := rp.nonceCache.Keys()
		for _, key := range keys {
			if value, exists := rp.nonceCache.Get(key); exists {
				newCache.Add(key, value)
			}
		}

		rp.nonceCache = newCache
	}

	rp.config = config
	return nil
}

// GetGlobalReplayProtector 获取全局重放攻击防护器实例 (Gets the global replay protector instance)
func GetGlobalReplayProtector() *ReplayProtector {
	globalReplayProtectorOnce.Do(func() {
		var err error
		globalReplayProtector, err = NewReplayProtector(DefaultReplayProtectorConfig())
		if err != nil {
			// 在生产环境中，这种情况不应该发生 (This should not happen in production)
			panic(fmt.Sprintf("failed to create global replay protector: %v", err))
		}
	})
	return globalReplayProtector
}

// InitGlobalReplayProtector 使用自定义配置初始化全局重放攻击防护器 (Initializes global replay protector with custom config)
func InitGlobalReplayProtector(config *ReplayProtectorConfig) error {
	var err error
	globalReplayProtectorOnce.Do(func() {
		globalReplayProtector, err = NewReplayProtector(config)
	})
	return err
}

// ValidateMessageGlobally 使用全局重放攻击防护器验证消息 (Validates message using global replay protector)
func ValidateMessageGlobally(messageID string, timestamp int64) error {
	return GetGlobalReplayProtector().ValidateMessage(messageID, timestamp)
}

// ValidateMessageWithIDGlobally 使用全局重放攻击防护器和安全消息ID验证消息 (Validates message using global replay protector and secure message ID)
func ValidateMessageWithIDGlobally(messageID string) error {
	return GetGlobalReplayProtector().ValidateMessageWithID(messageID)
}
