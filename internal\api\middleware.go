package api

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"server-monitor/internal/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequestIDMiddleware 添加请求ID追踪
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否已有请求ID
		requestID := c.<PERSON>eader("X-Request-ID")
		if requestID == "" {
			// 生成新的请求ID
			requestID = uuid.New().String()
		}

		// 设置请求ID到上下文和响应头
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)

		c.Next()
	}
}

// APIVersionMiddleware API版本控制中间件
func APIVersionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Header或Query参数获取API版本
		version := c.GetHeader("X-API-Version")
		if version == "" {
			version = c.Query("version")
		}
		if version == "" {
			version = "v1" // 默认版本
		}

		// 验证版本格式
		supportedVersions := map[string]bool{
			"v1": true,
			"v2": false, // 未来版本
		}

		if supported, exists := supportedVersions[version]; !exists || !supported {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":              fmt.Sprintf("Unsupported API version: %s", version),
				"supported_versions": []string{"v1"},
			})
			c.Abort()
			return
		}

		c.Set("api_version", version)
		c.Header("X-API-Version", version)
		c.Next()
	}
}

// RequestSizeLimitMiddleware 请求体大小限制中间件
func RequestSizeLimitMiddleware(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"error": fmt.Sprintf("Request body too large, maximum size is %d bytes", maxSize),
			})
			c.Abort()
			return
		}

		// 设置请求体大小限制
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxSize)
		c.Next()
	}
}

// CORSMiddleware CORS中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// 允许的源列表（生产环境应该配置具体的域名）
		allowedOrigins := []string{
			"http://localhost:3000",
			"http://localhost:8080",
			"http://127.0.0.1:3000",
			"http://127.0.0.1:8080",
		}

		// 检查是否为允许的源
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Request-ID, X-API-Version")
		c.Header("Access-Control-Expose-Headers", "Content-Length, X-Request-ID, X-API-Version")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(requestsPerMinute int) gin.HandlerFunc {
	// 简单的内存限流实现（生产环境建议使用Redis）
	clientRequests := make(map[string][]time.Time)

	return func(c *gin.Context) {
		if requestsPerMinute <= 0 {
			c.Next()
			return
		}

		clientIP := c.ClientIP()
		now := time.Now()

		// 清理过期的请求记录
		var validRequests []time.Time
		if requests, exists := clientRequests[clientIP]; exists {
			for _, requestTime := range requests {
				if now.Sub(requestTime) <= time.Minute {
					validRequests = append(validRequests, requestTime)
				}
			}
		}

		// 检查是否超过限制
		if len(validRequests) >= requestsPerMinute {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Too many requests",
				"retry_after": 60,
			})
			c.Abort()
			return
		}

		// 记录当前请求
		validRequests = append(validRequests, now)
		clientRequests[clientIP] = validRequests

		c.Next()
	}
}

// LoggerMiddleware 增强的日志中间件
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 计算延迟
		latency := time.Since(start)

		// 获取请求ID
		requestID, _ := c.Get("request_id")

		// 构建日志信息
		logData := map[string]interface{}{
			"request_id": requestID,
			"method":     c.Request.Method,
			"path":       path,
			"query":      raw,
			"status":     c.Writer.Status(),
			"latency":    latency.String(),
			"client_ip":  c.ClientIP(),
			"user_agent": c.Request.UserAgent(),
			"body_size":  c.Writer.Size(),
		}

		// 记录错误信息
		if len(c.Errors) > 0 {
			logData["errors"] = c.Errors.String()
		}

		// 根据状态码选择日志级别
		status := c.Writer.Status()
		if status >= 500 {
			logger.Error("HTTP Request - %v", logData)
		} else if status >= 400 {
			logger.Warn("HTTP Request - %v", logData)
		} else {
			logger.Info("HTTP Request - %v", logData)
		}
	}
}

// CompressionMiddleware 响应压缩中间件
func CompressionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查客户端是否支持压缩
		acceptEncoding := c.Request.Header.Get("Accept-Encoding")
		if !strings.Contains(acceptEncoding, "gzip") {
			c.Next()
			return
		}

		// 检查内容类型是否适合压缩
		contentType := c.Writer.Header().Get("Content-Type")
		compressibleTypes := []string{
			"application/json",
			"text/html",
			"text/plain",
			"text/css",
			"text/javascript",
			"application/javascript",
		}

		shouldCompress := false
		for _, cType := range compressibleTypes {
			if strings.Contains(contentType, cType) {
				shouldCompress = true
				break
			}
		}

		if shouldCompress {
			c.Header("Content-Encoding", "gzip")
			c.Header("Vary", "Accept-Encoding")
		}

		c.Next()
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置安全相关的HTTP头
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")

		c.Next()
	}
}

// AuthMiddleware 认证中间件（基础实现）
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header required",
			})
			c.Abort()
			return
		}

		// 检查Bearer token格式
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization format, use 'Bearer <token>'",
			})
			c.Abort()
			return
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token cannot be empty",
			})
			c.Abort()
			return
		}

		// TODO: 实现实际的token验证逻辑
		// 这里是一个简单的示例，生产环境应该验证JWT或其他token
		if token != "demo-token" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token",
			})
			c.Abort()
			return
		}

		// 设置用户信息到上下文
		c.Set("user_id", "demo-user")
		c.Set("authenticated", true)

		c.Next()
	}
}

// MetricsMiddleware 指标收集中间件
func MetricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		c.Next()

		// 收集指标
		duration := time.Since(start)
		status := c.Writer.Status()
		method := c.Request.Method
		path := c.FullPath()

		// TODO: 将指标发送到监控系统
		// 这里可以集成Prometheus、InfluxDB等监控系统
		logger.Debug("Metrics - Method: %s, Path: %s, Status: %d, Duration: %v",
			method, path, status, duration)
	}
}
