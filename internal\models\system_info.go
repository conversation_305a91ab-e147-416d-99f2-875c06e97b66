package models

import (
	"time"

	"gorm.io/gorm"
)

// SystemInfo represents the system information collected from a server.
type SystemInfo struct {
	gorm.Model
	ServerID    uint      `gorm:"not null"`
	Server      Server    `gorm:"foreignKey:ServerID"` // Belongs to server
	CPUUsage    float64   `gorm:"not null"`
	MemoryUsage float64   `gorm:"not null"` // Percentage
	DiskUsage   float64   `gorm:"not null"` // Percentage
	NetworkIn   uint64    `gorm:"not null"` // Bytes
	NetworkOut  uint64    `gorm:"not null"` // Bytes
	Timestamp   time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}
