package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"runtime"
	"sync"
	"time"

	"server-monitor/internal/cache"
	"server-monitor/internal/pool"
)

func main() {
	var (
		testType = flag.String("type", "cache", "测试类型: cache(缓存), pool(连接池), all(全部)")
		duration = flag.Int("duration", 30, "测试持续时间(秒)")
		workers  = flag.Int("workers", 10, "并发工作者数量")
	)
	flag.Parse()

	fmt.Printf("性能优化模块测试程序\n")
	fmt.Printf("测试类型: %s\n", *testType)
	fmt.Printf("测试时长: %d秒\n", *duration)
	fmt.Printf("并发数: %d\n", *workers)
	fmt.Println("---")

	switch *testType {
	case "cache":
		testCache(*duration, *workers)
	case "pool":
		testConnectionPool(*duration, *workers)
	case "all":
		testCache(*duration/2, *workers)
		fmt.Println()
		testConnectionPool(*duration/2, *workers)
	default:
		log.Fatalf("未知测试类型: %s", *testType)
	}
}

// testCache 测试缓存性能
func testCache(duration, workers int) {
	fmt.Println("=== 缓存性能测试 ===")

	// 创建缓存管理器
	cacheManager := cache.NewManager(time.Minute)
	defer cacheManager.Stop()

	// 预填充一些数据
	for i := 0; i < 1000; i++ {
		key := fmt.Sprintf("key_%d", i)
		value := fmt.Sprintf("value_%d", i)
		cacheManager.Set(key, value, 5*time.Minute)
	}

	fmt.Printf("预填充完成，当前缓存项数量: %d\n", cacheManager.Count())

	// 性能测试统计
	var (
		setOps    int64
		getOps    int64
		hitCount  int64
		missCount int64
		mu        sync.Mutex
	)

	// 启动工作者
	var wg sync.WaitGroup
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(duration)*time.Second)
	defer cancel()

	for i := 0; i < workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			localSetOps := int64(0)
			localGetOps := int64(0)
			localHits := int64(0)
			localMisses := int64(0)

			for {
				select {
				case <-ctx.Done():
					mu.Lock()
					setOps += localSetOps
					getOps += localGetOps
					hitCount += localHits
					missCount += localMisses
					mu.Unlock()
					return
				default:
					// 随机执行读写操作
					if time.Now().UnixNano()%2 == 0 {
						// 写操作
						key := fmt.Sprintf("worker_%d_key_%d", workerID, time.Now().UnixNano()%1000)
						value := fmt.Sprintf("worker_%d_value_%d", workerID, time.Now().UnixNano())
						cacheManager.Set(key, value, time.Minute)
						localSetOps++
					} else {
						// 读操作
						key := fmt.Sprintf("key_%d", time.Now().UnixNano()%1000)
						_, found := cacheManager.Get(key)
						if found {
							localHits++
						} else {
							localMisses++
						}
						localGetOps++
					}
				}
			}
		}(i)
	}

	// 定期输出统计信息
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				stats := cacheManager.GetStats()
				mu.Lock()
				fmt.Printf("进行中 - Set: %d, Get: %d, 命中: %d, 未命中: %d, 缓存项: %v\n",
					setOps, getOps, hitCount, missCount, stats["total_items"])
				mu.Unlock()
			}
		}
	}()

	wg.Wait()

	// 输出最终结果
	fmt.Printf("\n=== 缓存测试结果 ===\n")
	fmt.Printf("测试时长: %d秒\n", duration)
	fmt.Printf("并发工作者: %d\n", workers)
	fmt.Printf("Set操作: %d (%.2f ops/s)\n", setOps, float64(setOps)/float64(duration))
	fmt.Printf("Get操作: %d (%.2f ops/s)\n", getOps, float64(getOps)/float64(duration))
	fmt.Printf("缓存命中: %d\n", hitCount)
	fmt.Printf("缓存未命中: %d\n", missCount)
	if getOps > 0 {
		fmt.Printf("命中率: %.2f%%\n", float64(hitCount)/float64(getOps)*100)
	}

	stats := cacheManager.GetStats()
	fmt.Printf("最终缓存统计: %+v\n", stats)

	// 内存使用情况
	var m runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&m)
	fmt.Printf("内存使用: %.2f MB\n", float64(m.Alloc)/1024/1024)
}

// testConnectionPool 测试连接池性能
func testConnectionPool(duration, workers int) {
	fmt.Println("=== 连接池性能测试 ===")

	// 创建连接池配置
	config := &pool.Config{
		MaxIdle:     20,
		MaxActive:   100,
		IdleTimeout: 2 * time.Minute,
		MaxLifetime: 10 * time.Minute,
		TestOnGet:   true,
	}

	// 创建模拟连接工厂
	factory := func() (pool.Connection, error) {
		// 模拟连接创建延迟
		time.Sleep(time.Millisecond)
		return &MockConnection{
			id:       time.Now().UnixNano(),
			created:  time.Now(),
			lastUsed: time.Now(),
		}, nil
	}

	// 创建连接池
	connPool := pool.NewPool(factory, config)
	defer connPool.Close()

	fmt.Printf("连接池配置: MaxIdle=%d, MaxActive=%d\n", config.MaxIdle, config.MaxActive)

	// 性能测试统计
	var (
		getOps     int64
		putOps     int64
		createOps  int64
		errorCount int64
		mu         sync.Mutex
	)

	// 启动工作者
	var wg sync.WaitGroup
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(duration)*time.Second)
	defer cancel()

	for i := 0; i < workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			localGetOps := int64(0)
			localPutOps := int64(0)
			localCreateOps := int64(0)
			localErrors := int64(0)

			for {
				select {
				case <-ctx.Done():
					mu.Lock()
					getOps += localGetOps
					putOps += localPutOps
					createOps += localCreateOps
					errorCount += localErrors
					mu.Unlock()
					return
				default:
					// 获取连接
					conn, err := connPool.Get(ctx)
					if err != nil {
						localErrors++
						continue
					}
					localGetOps++

					// 模拟使用连接
					time.Sleep(time.Microsecond * 100)

					// 归还连接
					err = connPool.Put(conn)
					if err != nil {
						localErrors++
					} else {
						localPutOps++
					}
				}
			}
		}(i)
	}

	// 定期输出统计信息
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				stats := connPool.Stats()
				mu.Lock()
				fmt.Printf("进行中 - Get: %d, Put: %d, 错误: %d, 连接池: %+v\n",
					getOps, putOps, errorCount, stats)
				mu.Unlock()
			}
		}
	}()

	wg.Wait()

	// 输出最终结果
	fmt.Printf("\n=== 连接池测试结果 ===\n")
	fmt.Printf("测试时长: %d秒\n", duration)
	fmt.Printf("并发工作者: %d\n", workers)
	fmt.Printf("Get操作: %d (%.2f ops/s)\n", getOps, float64(getOps)/float64(duration))
	fmt.Printf("Put操作: %d (%.2f ops/s)\n", putOps, float64(putOps)/float64(duration))
	fmt.Printf("错误数量: %d\n", errorCount)

	stats := connPool.Stats()
	fmt.Printf("最终连接池统计: %+v\n", stats)

	// 内存使用情况
	var m runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&m)
	fmt.Printf("内存使用: %.2f MB\n", float64(m.Alloc)/1024/1024)
}

// MockConnection 模拟连接
type MockConnection struct {
	id       int64
	created  time.Time
	lastUsed time.Time
	closed   bool
	mu       sync.RWMutex
}

// Close 关闭连接
func (c *MockConnection) Close() error {
	c.mu.Lock()
	c.closed = true
	c.mu.Unlock()
	return nil
}

// IsAlive 检查连接是否存活
func (c *MockConnection) IsAlive() bool {
	c.mu.RLock()
	alive := !c.closed
	c.mu.RUnlock()
	return alive
}

// LastUsed 获取最后使用时间
func (c *MockConnection) LastUsed() time.Time {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.lastUsed
}

// SetLastUsed 设置最后使用时间
func (c *MockConnection) SetLastUsed(t time.Time) {
	c.mu.Lock()
	c.lastUsed = t
	c.mu.Unlock()
}
