package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// AlertHandlers 告警管理相关处理器

// listAlerts 列出告警
func (s *Server) listAlerts(c *gin.Context) {
	// 解析查询参数
	serverIDStr := c.Query("server_id")
	level := c.Query("level")
	status := c.Query("status")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	var serverID uint
	if serverIDStr != "" {
		id, err := strconv.ParseUint(serverIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, errorResponse("Invalid server_id parameter"))
			return
		}
		serverID = uint(id)
	}

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.<PERSON>(http.StatusBadRequest, errorResponse("Invalid start_time format, use RFC3339"))
			return
		}
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, errorResponse("Invalid end_time format, use RFC3339"))
			return
		}
	}

	alerts, err := s.repository.ListAlerts(serverID, level, status, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(gin.H{
		"alerts": alerts,
		"count":  len(alerts),
	}))
}

// updateAlertStatus 更新告警状态
func (s *Server) updateAlertStatus(c *gin.Context) {
	idStr := c.Param("id")
	alertID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("Invalid alert ID"))
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse(err.Error()))
		return
	}

	// 验证状态值
	validStatuses := map[string]bool{"active": true, "resolved": true}
	if _, ok := validStatuses[req.Status]; !ok {
		c.JSON(http.StatusBadRequest, errorResponse("Invalid status, must be 'active' or 'resolved'"))
		return
	}

	if err := s.repository.UpdateAlertStatus(uint(alertID), req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(gin.H{
		"message":  "Alert status updated successfully",
		"alert_id": alertID,
		"status":   req.Status,
	}))
}

// SystemInfoHandlers 系统信息相关处理器

// getSystemInfoHistory 获取系统信息历史
func (s *Server) getSystemInfoHistory(c *gin.Context) {
	serverIDStr := c.Query("server_id")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	var serverID uint
	if serverIDStr != "" {
		id, err := strconv.ParseUint(serverIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, errorResponse("Invalid server_id parameter"))
			return
		}
		serverID = uint(id)
	}

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, errorResponse("Invalid start_time format, use RFC3339"))
			return
		}
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, errorResponse("Invalid end_time format, use RFC3339"))
			return
		}
	}

	systemInfos, err := s.repository.GetSystemInfo(serverID, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(gin.H{
		"system_infos": systemInfos,
		"count":        len(systemInfos),
	}))
}

// StatisticsHandlers 统计相关处理器

// getAdvancedStats 获取高级统计信息
func (s *Server) getAdvancedStats(c *gin.Context) {
	// 获取服务器统计
	servers, err := s.repository.ListServers(false)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	activeServers := 0
	inactiveServers := 0
	for _, server := range servers {
		if server.IsActive {
			activeServers++
		} else {
			inactiveServers++
		}
	}

	// 获取告警统计
	now := time.Now()
	last24h := now.Add(-24 * time.Hour)

	allAlerts, err := s.repository.ListAlerts(0, "", "", last24h, now)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	alertStats := map[string]int{
		"total":    len(allAlerts),
		"active":   0,
		"resolved": 0,
		"critical": 0,
		"warning":  0,
		"info":     0,
	}

	for _, alert := range allAlerts {
		alertStats[alert.Status]++
		alertStats[alert.Level]++
	}

	// 获取系统信息统计
	systemInfos, err := s.repository.GetSystemInfo(0, last24h, now)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	stats := gin.H{
		"servers": gin.H{
			"total":    len(servers),
			"active":   activeServers,
			"inactive": inactiveServers,
		},
		"alerts": alertStats,
		"system_info": gin.H{
			"records_last_24h": len(systemInfos),
		},
		"timestamp": now.Unix(),
	}

	c.JSON(http.StatusOK, successResponse(stats))
}

// getServerMetrics 获取服务器指标
func (s *Server) getServerMetrics(c *gin.Context) {
	idStr := c.Param("id")
	serverID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("Invalid server ID"))
		return
	}

	// 获取时间范围参数
	hoursStr := c.DefaultQuery("hours", "24")
	hours, err := strconv.Atoi(hoursStr)
	if err != nil || hours <= 0 {
		c.JSON(http.StatusBadRequest, errorResponse("Invalid hours parameter"))
		return
	}

	now := time.Now()
	startTime := now.Add(-time.Duration(hours) * time.Hour)

	// 获取系统信息历史
	systemInfos, err := s.repository.GetSystemInfo(uint(serverID), startTime, now)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	// 获取告警历史
	alerts, err := s.repository.ListAlerts(uint(serverID), "", "", startTime, now)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	// 计算平均值
	var avgCPU, avgMemory, avgDisk float64
	if len(systemInfos) > 0 {
		var totalCPU, totalMemory, totalDisk float64
		for _, info := range systemInfos {
			totalCPU += info.CPUUsage
			totalMemory += info.MemoryUsage
			totalDisk += info.DiskUsage
		}
		avgCPU = totalCPU / float64(len(systemInfos))
		avgMemory = totalMemory / float64(len(systemInfos))
		avgDisk = totalDisk / float64(len(systemInfos))
	}

	metrics := gin.H{
		"server_id": serverID,
		"period": gin.H{
			"hours":      hours,
			"start_time": startTime.Format(time.RFC3339),
			"end_time":   now.Format(time.RFC3339),
		},
		"system_info": gin.H{
			"records": len(systemInfos),
			"averages": gin.H{
				"cpu_usage":    avgCPU,
				"memory_usage": avgMemory,
				"disk_usage":   avgDisk,
			},
		},
		"alerts": gin.H{
			"total": len(alerts),
		},
		"timestamp": now.Unix(),
	}

	c.JSON(http.StatusOK, successResponse(metrics))
}

// WebSocketHandlers WebSocket相关处理器

// upgradeWebSocket 升级到WebSocket连接
func (s *Server) upgradeWebSocket(c *gin.Context) {
	// 这里将在后续与WebSocket模块集成时实现
	// 目前返回一个占位符响应
	c.JSON(http.StatusNotImplemented, errorResponse("WebSocket upgrade not implemented yet"))
}
