package websocket

type Message struct {
	Type    string      `json:"type"`
	Payload interface{} `json:"payload"`
	Target  string      `json:"target,omitempty"` // Client ID or topic
}

type Hub struct {
	// 已注册的客户端。(Registered clients.)
	clients map[string]*Client // Changed to map[string]*Client

	// 来自客户端的入站消息。(Inbound messages from clients.)
	broadcast chan *Message // Changed to *Message

	// 来自客户端的注册请求。(Registration requests from clients.)
	register chan *Client

	// 来自客户端的取消注册请求。(Unregistration requests from clients.)
	unregister chan *Client

	// 用于向特定客户端发送消息的通道。(Channel for sending messages to specific clients.)
	sendToClient chan *Message
}

func NewHub() *Hub {
	return &Hub{
		broadcast:    make(chan *Message), // Changed to *Message
		register:     make(chan *Client),
		unregister:   make(chan *Client),
		clients:      make(map[string]*Client), // Changed to map[string]*Client
		sendToClient: make(chan *Message),
	}
}

func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client.ID] = client // Use client.ID as key
		case client := <-h.unregister:
			if _, ok := h.clients[client.ID]; ok { // Use client.ID as key
				delete(h.clients, client.ID) // Use client.ID as key
				close(client.send)
			}
		case message := <-h.broadcast:
			for _, client := range h.clients { // Iterate over values
				select {
				case client.send <- message.Payload.([]byte): // Assuming Payload is []byte for now
				default:
					close(client.send)
					delete(h.clients, client.ID) // Use client.ID as key
				}
			}
		case message := <-h.sendToClient:
			if client, ok := h.clients[message.Target]; ok {
				select {
				case client.send <- message.Payload.([]byte): // Assuming Payload is []byte for now
				default:
					close(client.send)
					delete(h.clients, client.ID)
				}
			}
		}
	}
}

// SendMessageToClient sends a message to a specific client.
func (h *Hub) SendMessageToClient(clientID string, msgType string, payload interface{}) {
	message := &Message{
		Type:    msgType,
		Payload: payload,
		Target:  clientID,
	}
	h.sendToClient <- message
}

// BroadcastMessage broadcasts a message to all connected clients.
func (h *Hub) BroadcastMessage(msgType string, payload interface{}) {
	message := &Message{
		Type:    msgType,
		Payload: payload,
	}
	h.broadcast <- message
}
