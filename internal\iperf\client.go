package iperf

import (
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"strconv"
	"time"
)

// Client iPerf3客户端
type Client struct {
	config *ClientConfig
}

// ClientConfig 客户端配置
type ClientConfig struct {
	ServerHost string `json:"server_host"` // 服务器地址
	ServerPort int    `json:"server_port"` // 服务器端口
	Duration   int    `json:"duration"`    // 测试持续时间(秒)
	Parallel   int    `json:"parallel"`    // 并行流数量
	Reverse    bool   `json:"reverse"`     // 反向测试(下载)
}

// TestResult 测试结果
type TestResult struct {
	Bandwidth  float64 `json:"bandwidth"`   // 带宽(Mbps)
	Latency    float64 `json:"latency"`     // 延迟(ms)
	Jitter     float64 `json:"jitter"`      // 抖动(ms)
	PacketLoss float64 `json:"packet_loss"` // 丢包率(%)
	Duration   float64 `json:"duration"`    // 实际测试时长(秒)
	Bytes      int64   `json:"bytes"`       // 传输字节数
	StartTime  string  `json:"start_time"`  // 开始时间
	EndTime    string  `json:"end_time"`    // 结束时间
}

// NewClient 创建新的iPerf3客户端
func NewClient(config *ClientConfig) *Client {
	return &Client{
		config: config,
	}
}

// Run 执行iPerf3测试
func (c *Client) Run(ctx context.Context) (*TestResult, error) {
	// 构建iPerf3命令参数
	args := c.buildArgs()

	// 执行命令
	cmd := exec.CommandContext(ctx, "iperf3", args...)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行iPerf3命令失败: %w", err)
	}

	// 解析输出结果
	result, err := c.parseOutput(output)
	if err != nil {
		return nil, fmt.Errorf("解析iPerf3输出失败: %w", err)
	}

	return result, nil
}

// buildArgs 构建命令行参数
func (c *Client) buildArgs() []string {
	args := []string{
		"-c", c.config.ServerHost,                    // 客户端模式，连接到服务器
		"-p", strconv.Itoa(c.config.ServerPort),     // 端口
		"-t", strconv.Itoa(c.config.Duration),       // 测试时长
		"-P", strconv.Itoa(c.config.Parallel),       // 并行流
		"-J",                                         // JSON输出格式
	}

	// 如果是反向测试(下载)
	if c.config.Reverse {
		args = append(args, "-R")
	}

	return args
}

// parseOutput 解析iPerf3 JSON输出
func (c *Client) parseOutput(output []byte) (*TestResult, error) {
	var iperfResult struct {
		Start struct {
			Timestamp struct {
				Time string `json:"time"`
			} `json:"timestamp"`
		} `json:"start"`
		End struct {
			SumSent struct {
				Seconds   float64 `json:"seconds"`
				Bytes     int64   `json:"bytes"`
				BitsPerSecond float64 `json:"bits_per_second"`
			} `json:"sum_sent"`
			SumReceived struct {
				Seconds   float64 `json:"seconds"`
				Bytes     int64   `json:"bytes"`
				BitsPerSecond float64 `json:"bits_per_second"`
			} `json:"sum_received"`
			CpuUtilizationPercent struct {
				HostTotal   float64 `json:"host_total"`
				HostUser    float64 `json:"host_user"`
				HostSystem  float64 `json:"host_system"`
				RemoteTotal float64 `json:"remote_total"`
				RemoteUser  float64 `json:"remote_user"`
				RemoteSystem float64 `json:"remote_system"`
			} `json:"cpu_utilization_percent"`
		} `json:"end"`
		Intervals []struct {
			Streams []struct {
				Socket int `json:"socket"`
				Start  float64 `json:"start"`
				End    float64 `json:"end"`
				Seconds float64 `json:"seconds"`
				Bytes   int64 `json:"bytes"`
				BitsPerSecond float64 `json:"bits_per_second"`
				Retransmits int `json:"retransmits"`
				Snd_cwnd int `json:"snd_cwnd"`
				Rtt     int `json:"rtt"`
				Rttvar  int `json:"rttvar"`
				Pmtu    int `json:"pmtu"`
				Omitted bool `json:"omitted"`
			} `json:"streams"`
			Sum struct {
				Start float64 `json:"start"`
				End   float64 `json:"end"`
				Seconds float64 `json:"seconds"`
				Bytes   int64 `json:"bytes"`
				BitsPerSecond float64 `json:"bits_per_second"`
				Retransmits int `json:"retransmits"`
				Omitted bool `json:"omitted"`
			} `json:"sum"`
		} `json:"intervals"`
	}

	err := json.Unmarshal(output, &iperfResult)
	if err != nil {
		return nil, fmt.Errorf("JSON解析失败: %w", err)
	}

	// 提取关键数据
	var bandwidth float64
	var bytes int64
	var duration float64

	if c.config.Reverse {
		// 下载测试，使用接收数据
		bandwidth = iperfResult.End.SumReceived.BitsPerSecond / 1000000 // 转换为Mbps
		bytes = iperfResult.End.SumReceived.Bytes
		duration = iperfResult.End.SumReceived.Seconds
	} else {
		// 上传测试，使用发送数据
		bandwidth = iperfResult.End.SumSent.BitsPerSecond / 1000000 // 转换为Mbps
		bytes = iperfResult.End.SumSent.Bytes
		duration = iperfResult.End.SumSent.Seconds
	}

	// 计算延迟和抖动(从间隔数据中提取)
	var totalRtt, totalRttvar float64
	var rttCount int

	for _, interval := range iperfResult.Intervals {
		if interval.Sum.Omitted {
			continue
		}
		for _, stream := range interval.Streams {
			if stream.Omitted {
				continue
			}
			if stream.Rtt > 0 {
				totalRtt += float64(stream.Rtt) / 1000 // 转换为ms
				totalRttvar += float64(stream.Rttvar) / 1000 // 转换为ms
				rttCount++
			}
		}
	}

	var latency, jitter float64
	if rttCount > 0 {
		latency = totalRtt / float64(rttCount)
		jitter = totalRttvar / float64(rttCount)
	}

	// 计算丢包率(基于重传次数估算)
	var totalRetransmits int
	for _, interval := range iperfResult.Intervals {
		if !interval.Sum.Omitted {
			totalRetransmits += interval.Sum.Retransmits
		}
	}

	// 简单的丢包率估算(重传次数/总包数的近似)
	var packetLoss float64
	if bytes > 0 {
		estimatedPackets := bytes / 1500 // 假设平均包大小1500字节
		if estimatedPackets > 0 {
			packetLoss = float64(totalRetransmits) / float64(estimatedPackets) * 100
			if packetLoss > 100 {
				packetLoss = 100 // 限制最大值
			}
		}
	}

	return &TestResult{
		Bandwidth:  bandwidth,
		Latency:    latency,
		Jitter:     jitter,
		PacketLoss: packetLoss,
		Duration:   duration,
		Bytes:      bytes,
		StartTime:  iperfResult.Start.Timestamp.Time,
		EndTime:    time.Now().Format(time.RFC3339),
	}, nil
}

// IsAvailable 检查iPerf3是否可用
func IsAvailable() bool {
	cmd := exec.Command("iperf3", "--version")
	err := cmd.Run()
	return err == nil
}

// GetVersion 获取iPerf3版本信息
func GetVersion() (string, error) {
	cmd := exec.Command("iperf3", "--version")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("获取iPerf3版本失败: %w", err)
	}
	return string(output), nil
}

// TestConnection 测试到服务器的连接
func (c *Client) TestConnection(ctx context.Context) error {
	// 执行一个很短的测试来验证连接
	args := []string{
		"-c", c.config.ServerHost,
		"-p", strconv.Itoa(c.config.ServerPort),
		"-t", "1", // 1秒测试
		"-J",      // JSON输出
	}

	cmd := exec.CommandContext(ctx, "iperf3", args...)
	_, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("连接测试失败: %w", err)
	}

	return nil
}
