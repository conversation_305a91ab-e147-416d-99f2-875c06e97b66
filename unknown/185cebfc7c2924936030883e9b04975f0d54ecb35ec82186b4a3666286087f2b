package models

import (
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// User 表示用户表（基本结构，可扩展） (User represents the user table (basic structure, extensible))
type User struct {
	gorm.Model
	Username  string `gorm:"unique;not null"`
	Password  string `gorm:"not null"` // 哈希密码 (Hashed password)
	Email     string `gorm:"unique"`
	Role      string `gorm:"default:'user'"` // 用户角色：admin, user (User role: admin, user)
	LastLogin time.Time
	IsActive  bool `gorm:"default:true"`
}

// BeforeSave GORM hook to hash password before saving
func (u *User) BeforeSave(tx *gorm.DB) (err error) {
	if u.Password != "" {
		hashedPassword, err := HashPassword(u.Password)
		if err != nil {
			return err
		}
		u.Password = hashedPassword
	}
	return
}

// HashPassword hashes the given password
func HashPassword(password string) (string, error) {
	// In a real application, use a strong hashing algorithm like bcrypt
	// For simplicity, this is a placeholder.
	return "hashed_" + password, nil
}

// CheckPasswordHash compares a hashed password with a plaintext password
func CheckPasswordHash(hashedPassword, password string) bool {
	// In a real application, use a strong hashing algorithm like bcrypt
	// For simplicity, this is a placeholder.
	return hashedPassword == "hashed_"+password
}

// Validate validates the User struct fields
func (u *User) Validate() error {
	if u.Username == "" {
		return fmt.Errorf("username cannot be empty")
	}
	if u.Email == "" {
		return fmt.Errorf("email cannot be empty")
	}
	// Basic email format validation
	if !strings.Contains(u.Email, "@") {
		return fmt.Errorf("invalid email format")
	}
	if u.Password == "" {
		return fmt.Errorf("password cannot be empty")
	}
	// Add more complex password validation if needed
	return nil
}
