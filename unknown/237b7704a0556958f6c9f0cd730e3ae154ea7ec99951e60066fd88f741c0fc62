package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"server-monitor/internal/monitor"
)

func main() {
	var (
		interval = flag.Duration("interval", 5*time.Second, "监控间隔")
		once     = flag.Bool("once", false, "只执行一次监控")
		format   = flag.String("format", "text", "输出格式 (text/json)")
		detailed = flag.Bool("detailed", false, "显示详细信息")
	)
	flag.Parse()

	fmt.Printf("系统监控测试程序\n")
	fmt.Printf("监控间隔: %v\n", *interval)
	fmt.Printf("输出格式: %s\n", *format)
	fmt.Printf("详细模式: %t\n", *detailed)
	fmt.Println("---")

	// 创建系统监控器
	systemMonitor := monitor.NewSystemMonitor(*interval)

	if *once {
		// 只执行一次
		runOnce(systemMonitor, *format, *detailed)
		return
	}

	// 持续监控
	runContinuous(systemMonitor, *format, *detailed)
}

func runOnce(systemMonitor *monitor.SystemMonitor, format string, detailed bool) {
	fmt.Println("执行单次系统监控...")

	// 获取系统信息
	info, err := systemMonitor.GetSystemInfo()
	if err != nil {
		log.Fatalf("获取系统信息失败: %v", err)
	}

	// 输出系统信息
	outputSystemInfo(info, format)

	if detailed {
		outputDetailedInfo(systemMonitor)
	}
}

func runContinuous(systemMonitor *monitor.SystemMonitor, format string, detailed bool) {
	fmt.Println("启动持续监控...")

	// 设置信号处理
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// 启动监控
	err := systemMonitor.Start(func(info *monitor.SystemInfo) {
		fmt.Printf("\n=== 监控时间: %s ===\n", time.Unix(info.Timestamp, 0).Format("2006-01-02 15:04:05"))
		outputSystemInfo(info, format)

		if detailed {
			outputDetailedInfo(systemMonitor)
		}
	})

	if err != nil {
		log.Fatalf("启动监控失败: %v", err)
	}

	fmt.Println("监控已启动，按 Ctrl+C 停止...")

	// 等待信号
	<-sigCh
	fmt.Println("\n收到停止信号，正在停止监控...")

	// 停止监控
	systemMonitor.Stop()
	fmt.Println("监控已停止")
}

func outputSystemInfo(info *monitor.SystemInfo, format string) {
	switch format {
	case "json":
		data, err := json.MarshalIndent(info, "", "  ")
		if err != nil {
			log.Printf("JSON序列化失败: %v", err)
			return
		}
		fmt.Println(string(data))
	case "text", "":
		fmt.Printf("主机名: %s\n", info.Hostname)
		fmt.Printf("操作系统: %s (%s)\n", info.OS, info.Platform)
		fmt.Printf("架构: %s\n", info.Architecture)
		fmt.Printf("运行时间: %d 秒 (%.1f 小时)\n", info.Uptime, float64(info.Uptime)/3600)
		fmt.Printf("CPU使用率: %.2f%%\n", info.CPUUsage)
		fmt.Printf("内存使用率: %.2f%% (%s / %s)\n",
			info.MemoryUsage,
			formatBytes(info.MemoryUsed),
			formatBytes(info.MemoryTotal))
		fmt.Printf("磁盘使用率: %.2f%% (%s / %s)\n",
			info.DiskUsage,
			formatBytes(info.DiskUsed),
			formatBytes(info.DiskTotal))
		fmt.Printf("网络接收: %s\n", formatBytes(info.NetworkRx))
		fmt.Printf("网络发送: %s\n", formatBytes(info.NetworkTx))
		if info.LoadAverage > 0 {
			fmt.Printf("负载平均值: %.2f\n", info.LoadAverage)
		}
	}
}

func outputDetailedInfo(systemMonitor *monitor.SystemMonitor) {
	fmt.Println("\n--- 详细信息 ---")

	// CPU详细信息
	cpuStats, err := systemMonitor.GetCPUStats()
	if err != nil {
		log.Printf("获取CPU详细信息失败: %v", err)
	} else {
		fmt.Printf("CPU详细信息 (%d个核心):\n", len(cpuStats))
		for i, stat := range cpuStats {
			if i >= 4 { // 只显示前4个核心
				fmt.Printf("  ... (还有 %d 个核心)\n", len(cpuStats)-4)
				break
			}
			fmt.Printf("  %s: User=%.1f%% System=%.1f%% Idle=%.1f%%\n",
				stat.CPU, stat.User, stat.System, stat.Idle)
		}
	}

	// 网络接口详细信息
	netStats, err := systemMonitor.GetNetworkStats()
	if err != nil {
		log.Printf("获取网络详细信息失败: %v", err)
	} else {
		fmt.Printf("网络接口详细信息 (%d个接口):\n", len(netStats))
		for i, stat := range netStats {
			if i >= 3 { // 只显示前3个接口
				fmt.Printf("  ... (还有 %d 个接口)\n", len(netStats)-3)
				break
			}
			fmt.Printf("  %s: RX=%s TX=%s\n",
				stat.Interface,
				formatBytes(stat.BytesRecv),
				formatBytes(stat.BytesSent))
		}
	}

	// 磁盘详细信息
	diskStats, err := systemMonitor.GetDiskStats()
	if err != nil {
		log.Printf("获取磁盘详细信息失败: %v", err)
	} else {
		fmt.Printf("磁盘详细信息 (%d个分区):\n", len(diskStats))
		for _, stat := range diskStats {
			fmt.Printf("  %s (%s): %.1f%% (%s / %s)\n",
				stat.Mountpoint,
				stat.Fstype,
				stat.UsedPercent,
				formatBytes(stat.Used),
				formatBytes(stat.Total))
		}
	}
}

func formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
