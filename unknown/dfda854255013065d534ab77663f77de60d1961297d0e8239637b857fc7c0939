package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// Config 应用程序配置结构体
type Config struct {
	System    SystemConfig    `yaml:"system" mapstructure:"system"`
	Database  DatabaseConfig  `yaml:"database" mapstructure:"database"`
	Scheduler SchedulerConfig `yaml:"scheduler" mapstructure:"scheduler"`
	Servers   []ServerConfig  `yaml:"servers" mapstructure:"servers"`
	Peer      PeerConfig      `yaml:"peer" mapstructure:"peer"`
	Test      TestConfig      `yaml:"test" mapstructure:"test"`
	Web       WebConfig       `yaml:"web" mapstructure:"web"`
	API       APIConfig       `yaml:"api" mapstructure:"api"`
	IPerf     IPerfConfig     `yaml:"iperf" mapstructure:"iperf"`
	Sync      SyncConfig      `yaml:"sync" mapstructure:"sync"`
	Log       LogConfig       `yaml:"log" mapstructure:"log"`
	Security  SecurityConfig  `yaml:"security" mapstructure:"security"`
}

// SystemConfig 系统配置结构体
type SystemConfig struct {
	Name        string   `yaml:"name" mapstructure:"name"`
	Version     string   `yaml:"version" mapstructure:"version"`
	Environment string   `yaml:"environment" mapstructure:"environment"`
	DataDir     string   `yaml:"data_dir" mapstructure:"data_dir"`
	PidFile     string   `yaml:"pid_file" mapstructure:"pid_file"`
	Mode        string   `yaml:"mode" mapstructure:"mode"` // server, client, both
	Description string   `yaml:"description" mapstructure:"description"`
	Tags        []string `yaml:"tags" mapstructure:"tags"`
	Interval    int      `yaml:"interval" mapstructure:"interval"`
}

// DatabaseConfig 数据库配置结构体
type DatabaseConfig struct {
	Path             string        `yaml:"path" mapstructure:"path"`
	MaxOpenConns     int           `yaml:"max_open_conns" mapstructure:"max_open_conns"`
	MaxIdleConns     int           `yaml:"max_idle_conns" mapstructure:"max_idle_conns"`
	ConnMaxLifetime  time.Duration `yaml:"conn_max_lifetime" mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime  time.Duration `yaml:"conn_max_idle_time" mapstructure:"conn_max_idle_time"`
	EnableWAL        bool          `yaml:"enable_wal" mapstructure:"enable_wal"`
	EnableForeignKey bool          `yaml:"enable_foreign_key" mapstructure:"enable_foreign_key"`
	BusyTimeout      time.Duration `yaml:"busy_timeout" mapstructure:"busy_timeout"`
	RetentionDays    int           `yaml:"retention_days" mapstructure:"retention_days"`
}

// SchedulerConfig 调度器配置结构体
type SchedulerConfig struct {
	Enabled       bool          `yaml:"enabled" mapstructure:"enabled"`
	Mode          string        `yaml:"mode" mapstructure:"mode"` // odd, even, both
	Timezone      string        `yaml:"timezone" mapstructure:"timezone"`
	StartHour     int           `yaml:"start_hour" mapstructure:"start_hour"`
	EndHour       int           `yaml:"end_hour" mapstructure:"end_hour"`
	MaxConcurrent int           `yaml:"max_concurrent" mapstructure:"max_concurrent"`
	RetryAttempts int           `yaml:"retry_attempts" mapstructure:"retry_attempts"`
	RetryInterval time.Duration `yaml:"retry_interval" mapstructure:"retry_interval"`
}

// ServerConfig 服务器配置结构体
type ServerConfig struct {
	Name     string   `yaml:"name" mapstructure:"name"`
	IP       string   `yaml:"ip" mapstructure:"ip"`
	Port     int      `yaml:"port" mapstructure:"port"`
	Location string   `yaml:"location" mapstructure:"location"`
	Provider string   `yaml:"provider" mapstructure:"provider"`
	Enabled  bool     `yaml:"enabled" mapstructure:"enabled"`
	Priority int      `yaml:"priority" mapstructure:"priority"`
	Tags     []string `yaml:"tags" mapstructure:"tags"`
}

// PeerConfig 对等体配置结构体
type PeerConfig struct {
	IP       string        `yaml:"ip" mapstructure:"ip"`
	Port     int           `yaml:"port" mapstructure:"port"`
	Username string        `yaml:"username" mapstructure:"username"`
	Password string        `yaml:"password" mapstructure:"password"`
	Timeout  time.Duration `yaml:"timeout" mapstructure:"timeout"`
	Enabled  bool          `yaml:"enabled" mapstructure:"enabled"`
}

// TestConfig 测试配置结构体
type TestConfig struct {
	Duration       time.Duration `yaml:"duration" mapstructure:"duration"`
	Parallel       int           `yaml:"parallel" mapstructure:"parallel"`
	WindowSize     string        `yaml:"window_size" mapstructure:"window_size"`
	BufferLength   string        `yaml:"buffer_length" mapstructure:"buffer_length"`
	Bandwidth      string        `yaml:"bandwidth" mapstructure:"bandwidth"`
	Protocol       string        `yaml:"protocol" mapstructure:"protocol"` // tcp, udp, both
	Reverse        bool          `yaml:"reverse" mapstructure:"reverse"`
	Bidir          bool          `yaml:"bidir" mapstructure:"bidir"`
	Timeout        time.Duration `yaml:"timeout" mapstructure:"timeout"`
	ConnectTimeout time.Duration `yaml:"connect_timeout" mapstructure:"connect_timeout"`
	MaxRetries     int           `yaml:"max_retries" mapstructure:"max_retries"`
	RetryDelay     time.Duration `yaml:"retry_delay" mapstructure:"retry_delay"`
}

// WebConfig Web界面配置结构体
type WebConfig struct {
	Enabled     bool      `yaml:"enabled" mapstructure:"enabled"`
	Port        int       `yaml:"port" mapstructure:"port"`
	Host        string    `yaml:"host" mapstructure:"host"`
	StaticDir   string    `yaml:"static_dir" mapstructure:"static_dir"`
	TemplateDir string    `yaml:"template_dir" mapstructure:"template_dir"`
	TLS         TLSConfig `yaml:"tls" mapstructure:"tls"`
}

// APIConfig API服务配置结构体
type APIConfig struct {
	Enabled   bool          `yaml:"enabled" mapstructure:"enabled"`
	Port      int           `yaml:"port" mapstructure:"port"`
	Host      string        `yaml:"host" mapstructure:"host"`
	Prefix    string        `yaml:"prefix" mapstructure:"prefix"`
	Timeout   time.Duration `yaml:"timeout" mapstructure:"timeout"`
	RateLimit int           `yaml:"rate_limit" mapstructure:"rate_limit"`
	TLS       TLSConfig     `yaml:"tls" mapstructure:"tls"`
	CORS      CORSConfig    `yaml:"cors" mapstructure:"cors"`
}

// TLSConfig TLS配置结构体
type TLSConfig struct {
	Enabled  bool   `yaml:"enabled" mapstructure:"enabled"`
	CertFile string `yaml:"cert_file" mapstructure:"cert_file"`
	KeyFile  string `yaml:"key_file" mapstructure:"key_file"`
}

// CORSConfig CORS配置结构体
type CORSConfig struct {
	Enabled      bool     `yaml:"enabled" mapstructure:"enabled"`
	AllowOrigins []string `yaml:"allow_origins" mapstructure:"allow_origins"`
	AllowMethods []string `yaml:"allow_methods" mapstructure:"allow_methods"`
	AllowHeaders []string `yaml:"allow_headers" mapstructure:"allow_headers"`
}

// SyncConfig 同步配置结构体
type SyncConfig struct {
	Enabled       bool          `yaml:"enabled" mapstructure:"enabled"`
	Interval      time.Duration `yaml:"interval" mapstructure:"interval"`
	BatchSize     int           `yaml:"batch_size" mapstructure:"batch_size"`
	MaxRetries    int           `yaml:"max_retries" mapstructure:"max_retries"`
	RetryInterval time.Duration `yaml:"retry_interval" mapstructure:"retry_interval"`
	Timeout       time.Duration `yaml:"timeout" mapstructure:"timeout"`
}

// LogConfig 日志配置结构体
type LogConfig struct {
	Level      string `yaml:"level" mapstructure:"level"`
	Format     string `yaml:"format" mapstructure:"format"` // json, text
	Output     string `yaml:"output" mapstructure:"output"` // stdout, stderr, file
	File       string `yaml:"file" mapstructure:"file"`
	MaxSize    int    `yaml:"max_size" mapstructure:"max_size"`       // MB
	MaxBackups int    `yaml:"max_backups" mapstructure:"max_backups"` // 保留文件数
	MaxAge     int    `yaml:"max_age" mapstructure:"max_age"`         // 天数
	Compress   bool   `yaml:"compress" mapstructure:"compress"`
}

// IPerfConfig iPerf3测试配置结构体
type IPerfConfig struct {
	Enabled            bool `yaml:"enabled" mapstructure:"enabled"`                           // 是否启用iPerf3测试
	TestInterval       int  `yaml:"test_interval" mapstructure:"test_interval"`               // 测试间隔(分钟)
	TestDuration       int  `yaml:"test_duration" mapstructure:"test_duration"`               // 单次测试持续时间(秒)
	TestTimeout        int  `yaml:"test_timeout" mapstructure:"test_timeout"`                 // 测试超时时间(秒)
	ParallelStreams    int  `yaml:"parallel_streams" mapstructure:"parallel_streams"`         // 并行流数量
	MaxConcurrentTests int  `yaml:"max_concurrent_tests" mapstructure:"max_concurrent_tests"` // 最大并发测试数
}

// SecurityConfig 安全配置结构体 (Security configuration structure)
type SecurityConfig struct {
	Encryption       EncryptionConfig       `yaml:"encryption" mapstructure:"encryption"`               // 加密配置 (Encryption configuration)
	KeyManager       KeyManagerConfig       `yaml:"key_manager" mapstructure:"key_manager"`             // 密钥管理配置 (Key manager configuration)
	Auth             AuthConfig             `yaml:"auth" mapstructure:"auth"`                           // 认证配置 (Authentication configuration)
	ReplayProtection ReplayProtectionConfig `yaml:"replay_protection" mapstructure:"replay_protection"` // 重放攻击防护配置 (Replay protection configuration)
	MessageIntegrity MessageIntegrityConfig `yaml:"message_integrity" mapstructure:"message_integrity"` // 消息完整性配置 (Message integrity configuration)
	RateLimiting     RateLimitingConfig     `yaml:"rate_limiting" mapstructure:"rate_limiting"`         // 速率限制配置 (Rate limiting configuration)
}

// EncryptionConfig 加密配置结构体 (Encryption configuration structure)
type EncryptionConfig struct {
	Enabled   bool   `yaml:"enabled" mapstructure:"enabled"`     // 是否启用加密 (Whether encryption is enabled)
	Algorithm string `yaml:"algorithm" mapstructure:"algorithm"` // 加密算法 (Encryption algorithm)
	KeySize   int    `yaml:"key_size" mapstructure:"key_size"`   // 密钥大小 (Key size)
}

// KeyManagerConfig 密钥管理配置结构体 (Key manager configuration structure)
type KeyManagerConfig struct {
	Enabled          bool          `yaml:"enabled" mapstructure:"enabled"`                     // 是否启用密钥管理 (Whether key management is enabled)
	RotationInterval time.Duration `yaml:"rotation_interval" mapstructure:"rotation_interval"` // 密钥轮换间隔 (Key rotation interval)
	KeyTTL           time.Duration `yaml:"key_ttl" mapstructure:"key_ttl"`                     // 密钥生存时间 (Key time to live)
	MaxKeyHistory    int           `yaml:"max_key_history" mapstructure:"max_key_history"`     // 最大密钥历史数量 (Maximum key history count)
	AutoRotate       bool          `yaml:"auto_rotate" mapstructure:"auto_rotate"`             // 是否自动轮换 (Whether to auto rotate)
	MasterPassword   string        `yaml:"master_password" mapstructure:"master_password"`     // 主密码 (Master password)
}

// AuthConfig 认证配置结构体 (Authentication configuration structure)
type AuthConfig struct {
	Enabled                bool          `yaml:"enabled" mapstructure:"enabled"`                                   // 是否启用认证 (Whether authentication is enabled)
	TokenTTL               time.Duration `yaml:"token_ttl" mapstructure:"token_ttl"`                               // 令牌生存时间 (Token time to live)
	RefreshTokenTTL        time.Duration `yaml:"refresh_token_ttl" mapstructure:"refresh_token_ttl"`               // 刷新令牌生存时间 (Refresh token time to live)
	MaxLoginAttempts       int           `yaml:"max_login_attempts" mapstructure:"max_login_attempts"`             // 最大登录尝试次数 (Maximum login attempts)
	LockoutDuration        time.Duration `yaml:"lockout_duration" mapstructure:"lockout_duration"`                 // 锁定持续时间 (Lockout duration)
	EnableReplayProtection bool          `yaml:"enable_replay_protection" mapstructure:"enable_replay_protection"` // 启用重放攻击防护 (Enable replay protection)
}

// ReplayProtectionConfig 重放攻击防护配置结构体 (Replay protection configuration structure)
type ReplayProtectionConfig struct {
	Enabled    bool          `yaml:"enabled" mapstructure:"enabled"`         // 是否启用重放攻击防护 (Whether replay protection is enabled)
	TimeWindow time.Duration `yaml:"time_window" mapstructure:"time_window"` // 时间窗口 (Time window)
	NonceCache int           `yaml:"nonce_cache" mapstructure:"nonce_cache"` // Nonce缓存大小 (Nonce cache size)
	CacheTTL   time.Duration `yaml:"cache_ttl" mapstructure:"cache_ttl"`     // 缓存生存时间 (Cache time to live)
}

// MessageIntegrityConfig 消息完整性配置结构体 (Message integrity configuration structure)
type MessageIntegrityConfig struct {
	Enabled                  bool   `yaml:"enabled" mapstructure:"enabled"`                                       // 是否启用消息完整性检查 (Whether message integrity check is enabled)
	ChecksumAlgorithm        string `yaml:"checksum_algorithm" mapstructure:"checksum_algorithm"`                 // 校验和算法 (Checksum algorithm)
	EnableSequenceValidation bool   `yaml:"enable_sequence_validation" mapstructure:"enable_sequence_validation"` // 启用序列号验证 (Enable sequence validation)
	MaxSequenceGap           int    `yaml:"max_sequence_gap" mapstructure:"max_sequence_gap"`                     // 最大序列号间隔 (Maximum sequence gap)
}

// RateLimitingConfig 速率限制配置结构体 (Rate limiting configuration structure)
type RateLimitingConfig struct {
	Enabled         bool          `yaml:"enabled" mapstructure:"enabled"`                   // 是否启用速率限制 (Whether rate limiting is enabled)
	RequestsPerSec  int           `yaml:"requests_per_sec" mapstructure:"requests_per_sec"` // 每秒请求数限制 (Requests per second limit)
	BurstSize       int           `yaml:"burst_size" mapstructure:"burst_size"`             // 突发大小 (Burst size)
	CleanupInterval time.Duration `yaml:"cleanup_interval" mapstructure:"cleanup_interval"` // 清理间隔 (Cleanup interval)
	BanDuration     time.Duration `yaml:"ban_duration" mapstructure:"ban_duration"`         // 封禁持续时间 (Ban duration)
	MaxViolations   int           `yaml:"max_violations" mapstructure:"max_violations"`     // 最大违规次数 (Maximum violations)
}

// Manager 配置管理器
type Manager struct {
	config     *Config
	configFile string
	viper      *viper.Viper
	mu         sync.RWMutex
	callbacks  []func(*Config)
}

// NewManager 创建一个新的配置管理器实例
func NewManager(configFile string) *Manager {
	return &Manager{
		configFile: configFile,
		viper:      viper.New(),
	}
}

// Load 从文件或环境变量加载配置
func (m *Manager) Load() error {
	// 设置配置文件路径
	if m.configFile != "" {
		m.viper.SetConfigFile(m.configFile)
	} else {
		// 默认配置文件搜索路径
		m.viper.SetConfigName("config")
		m.viper.SetConfigType("yaml")
		m.viper.AddConfigPath("./configs")
		m.viper.AddConfigPath(".")
		m.viper.AddConfigPath("/etc/monitor")
	}

	// 设置环境变量前缀，允许通过环境变量覆盖配置
	m.viper.SetEnvPrefix("MONITOR")
	m.viper.AutomaticEnv()
	m.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 设置所有配置项的默认值
	m.setDefaults()

	// 从指定路径读取配置文件
	if err := m.viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 如果配置文件不存在，则使用默认配置并打印警告
			fmt.Printf("Warning: Config file not found, using defaults\n")
		} else {
			return fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// 将解析后的配置映射到Config结构体
	config := &Config{}
	if err := m.viper.Unmarshal(config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	m.config = config
	return nil
}

// setDefaults 设置默认值
func (m *Manager) setDefaults() {
	// System defaults
	m.viper.SetDefault("system.name", "Server Monitor")
	m.viper.SetDefault("system.version", "1.0.0")
	m.viper.SetDefault("system.environment", "production")
	m.viper.SetDefault("system.data_dir", "./data")
	m.viper.SetDefault("system.pid_file", "./data/monitor.pid")
	m.viper.SetDefault("system.mode", "both")

	// Database defaults
	m.viper.SetDefault("database.path", "./data/monitor.db")
	m.viper.SetDefault("database.max_open_conns", 25)
	m.viper.SetDefault("database.max_idle_conns", 5)
	m.viper.SetDefault("database.conn_max_lifetime", "1h")
	m.viper.SetDefault("database.conn_max_idle_time", "10m")
	m.viper.SetDefault("database.enable_wal", true)
	m.viper.SetDefault("database.enable_foreign_key", true)
	m.viper.SetDefault("database.busy_timeout", "30s")

	// Scheduler defaults
	m.viper.SetDefault("scheduler.enabled", true)
	m.viper.SetDefault("scheduler.mode", "odd")
	m.viper.SetDefault("scheduler.timezone", "Asia/Shanghai")
	m.viper.SetDefault("scheduler.start_hour", 0)
	m.viper.SetDefault("scheduler.end_hour", 23)
	m.viper.SetDefault("scheduler.max_concurrent", 4)
	m.viper.SetDefault("scheduler.retry_attempts", 3)
	m.viper.SetDefault("scheduler.retry_interval", "5m")

	// Test defaults
	m.viper.SetDefault("test.duration", "30s")
	m.viper.SetDefault("test.parallel", 4)
	m.viper.SetDefault("test.window_size", "64K")
	m.viper.SetDefault("test.buffer_length", "128K")
	m.viper.SetDefault("test.bandwidth", "0")
	m.viper.SetDefault("test.protocol", "tcp")
	m.viper.SetDefault("test.reverse", false)
	m.viper.SetDefault("test.bidir", false)
	m.viper.SetDefault("test.timeout", "60s")
	m.viper.SetDefault("test.connect_timeout", "10s")
	m.viper.SetDefault("test.max_retries", 3)
	m.viper.SetDefault("test.retry_delay", "5s")

	// Web defaults
	m.viper.SetDefault("web.enabled", true)
	m.viper.SetDefault("web.port", 8080)
	m.viper.SetDefault("web.host", "0.0.0.0")
	m.viper.SetDefault("web.static_dir", "./web/static")
	m.viper.SetDefault("web.template_dir", "./web/templates")
	m.viper.SetDefault("web.tls.enabled", false)

	// API defaults
	m.viper.SetDefault("api.enabled", true)
	m.viper.SetDefault("api.port", 8443)
	m.viper.SetDefault("api.host", "0.0.0.0")
	m.viper.SetDefault("api.prefix", "/api/v1")
	m.viper.SetDefault("api.timeout", "30s")
	m.viper.SetDefault("api.rate_limit", 100)
	m.viper.SetDefault("api.tls.enabled", false)
	m.viper.SetDefault("api.cors.enabled", true)
	m.viper.SetDefault("api.cors.allow_origins", []string{"*"})
	m.viper.SetDefault("api.cors.allow_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	m.viper.SetDefault("api.cors.allow_headers", []string{"*"})

	// Sync defaults
	m.viper.SetDefault("sync.enabled", false)
	m.viper.SetDefault("sync.interval", "5m")
	m.viper.SetDefault("sync.batch_size", 100)
	m.viper.SetDefault("sync.max_retries", 3)
	m.viper.SetDefault("sync.retry_interval", "30s")
	m.viper.SetDefault("sync.timeout", "30s")

	// Log defaults
	m.viper.SetDefault("log.level", "info")
	m.viper.SetDefault("log.format", "text")
	m.viper.SetDefault("log.output", "stdout")
	m.viper.SetDefault("log.max_size", 100)
	m.viper.SetDefault("log.max_backups", 3)
	m.viper.SetDefault("log.max_age", 28)
	m.viper.SetDefault("log.compress", true)

	// IPerf defaults
	m.viper.SetDefault("iperf.enabled", true)
	m.viper.SetDefault("iperf.test_interval", 60)       // 60分钟
	m.viper.SetDefault("iperf.test_duration", 30)       // 30秒
	m.viper.SetDefault("iperf.test_timeout", 120)       // 120秒
	m.viper.SetDefault("iperf.parallel_streams", 4)     // 4个并行流
	m.viper.SetDefault("iperf.max_concurrent_tests", 3) // 最大3个并发测试

	// Peer defaults
	m.viper.SetDefault("peer.port", 8443)
	m.viper.SetDefault("peer.timeout", "30s")
	m.viper.SetDefault("peer.enabled", false)

	// Security defaults
	m.viper.SetDefault("security.encryption.enabled", true)
	m.viper.SetDefault("security.encryption.algorithm", "AES-256-GCM")
	m.viper.SetDefault("security.encryption.key_size", 256)

	// Key manager defaults
	m.viper.SetDefault("security.key_manager.enabled", true)
	m.viper.SetDefault("security.key_manager.rotation_interval", "24h")
	m.viper.SetDefault("security.key_manager.key_ttl", "72h")
	m.viper.SetDefault("security.key_manager.max_key_history", 10)
	m.viper.SetDefault("security.key_manager.auto_rotate", true)
	m.viper.SetDefault("security.key_manager.master_password", "")

	// Auth defaults
	m.viper.SetDefault("security.auth.enabled", true)
	m.viper.SetDefault("security.auth.token_ttl", "1h")
	m.viper.SetDefault("security.auth.refresh_token_ttl", "24h")
	m.viper.SetDefault("security.auth.max_login_attempts", 5)
	m.viper.SetDefault("security.auth.lockout_duration", "15m")
	m.viper.SetDefault("security.auth.enable_replay_protection", true)

	// Replay protection defaults
	m.viper.SetDefault("security.replay_protection.enabled", true)
	m.viper.SetDefault("security.replay_protection.time_window", "5m")
	m.viper.SetDefault("security.replay_protection.nonce_cache", 10000)
	m.viper.SetDefault("security.replay_protection.cache_ttl", "10m")

	// Message integrity defaults
	m.viper.SetDefault("security.message_integrity.enabled", true)
	m.viper.SetDefault("security.message_integrity.checksum_algorithm", "SHA256")
	m.viper.SetDefault("security.message_integrity.enable_sequence_validation", true)
	m.viper.SetDefault("security.message_integrity.max_sequence_gap", 100)

	// Rate limiting defaults
	m.viper.SetDefault("security.rate_limiting.enabled", true)
	m.viper.SetDefault("security.rate_limiting.requests_per_sec", 100)
	m.viper.SetDefault("security.rate_limiting.burst_size", 200)
	m.viper.SetDefault("security.rate_limiting.cleanup_interval", "1m")
	m.viper.SetDefault("security.rate_limiting.ban_duration", "1h")
	m.viper.SetDefault("security.rate_limiting.max_violations", 10)
}

// Get 获取当前加载的配置
func (m *Manager) Get() *Config {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.config
}

// GetViper 获取Viper实例，用于高级配置操作
func (m *Manager) GetViper() *viper.Viper {
	return m.viper
}

// Reload 重新加载配置，等同于再次调用Load方法
func (m *Manager) Reload() error {
	return m.Load()
}

// Save 将当前配置保存到指定文件
func (m *Manager) Save(filename string) error {
	if filename == "" {
		filename = m.configFile
	}

	// 确保配置文件所在的目录存在，如果不存在则创建
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	return m.viper.WriteConfigAs(filename)
}

// OnConfigChange 注册一个回调函数，在配置发生变更时执行
func (m *Manager) OnConfigChange(callback func(*Config)) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.callbacks = append(m.callbacks, callback)
}

// WatchConfig 启动配置文件的监听，当文件内容变化时自动重新加载
func (m *Manager) WatchConfig() {
	m.viper.WatchConfig()
	m.viper.OnConfigChange(func(e fsnotify.Event) {
		fmt.Printf("Config file changed: %s\n", e.Name)

		// 重新加载配置并应用变更
		if err := m.reloadConfig(); err != nil {
			fmt.Printf("Failed to reload config: %v\n", err)
			return
		}

		// 调用所有已注册的回调函数
		m.mu.RLock()
		config := m.config
		callbacks := make([]func(*Config), len(m.callbacks))
		copy(callbacks, m.callbacks)
		m.mu.RUnlock()

		for _, callback := range callbacks {
			callback(config)
		}
	})
}

// reloadConfig 重新加载配置的内部方法，不触发外部回调
func (m *Manager) reloadConfig() error {
	// 将更新后的配置解析到Config结构体
	config := &Config{}
	if err := m.viper.Unmarshal(config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证加载的配置是否有效
	if err := config.Validate(); err != nil {
		return fmt.Errorf("config validation failed: %w", err)
	}

	m.mu.Lock()
	m.config = config
	m.mu.Unlock()

	return nil
}
