package main

import (
	"flag"
	"fmt"
	"context" // Add context for graceful shutdown
	"log"     // Add log for error handling
	"net/http" // Add net/http for http.ErrServerClosed
	"os"
	"os/signal" // Add os/signal for graceful shutdown
	"server-monitor/internal/config"
	"server-monitor/internal/database" // Add database package
	"server-monitor/internal/monitor"  // Add monitor package
	"server-monitor/internal/web"      // Add web package
	"strings"                           // Added import for strings.Split
	"syscall"                           // Add syscall for graceful shutdown
	"time"                              // Add time for graceful shutdown
)

func main() {
	// 定义命令行参数
	isServer := flag.Bool("s", false, "以服务器模式运行")
	isClient := flag.Bool("c", false, "以客户端模式运行")

	// 服务端参数
	serverPort := flag.Int("p", 8443, "API监听端口（服务端）")
	serverPassword := flag.String("pw", "", "加密密码（服务端）")
	webPort := flag.Int("web-port", 8080, "Web界面端口（可选，默认8080）")
	dbPath := flag.String("db", "./data/monitor.db", "数据库路径（可选）")
	logLevel := flag.String("log-level", "info", "日志级别（可选）")
	retention := flag.Int("retention", 30, "数据保留天数（可选）")
	dataDir := flag.String("data-dir", "./data", "数据目录（可选）")

	// 客户端参数
	clientName := flag.String("n", "", "服务器名称（客户端）")
	serverIP := flag.String("ip", "", "服务端IP（客户端）")
	clientPassword := flag.String("pw-client", "", "加密密码（客户端，与服务端相同）") // 客户端密码使用不同的flag名以避免冲突
	description := flag.String("desc", "", "服务器描述（可选，客户端）")
	tags := flag.String("tags", "", "标签，逗号分隔（可选，客户端）")
	interval := flag.Int("interval", 30, "监控间隔秒数（可选，客户端，默认30）")

	flag.Parse()

	// 检查模式参数
	if *isServer && *isClient {
		fmt.Println("错误：不能同时指定服务端和客户端模式。")
		os.Exit(1)
	}
	if !*isServer && !*isClient {
		fmt.Println("错误：必须指定服务端（-s）或客户端（-c）模式。")
		os.Exit(1)
	}

	cfg := config.NewManager("") // 使用默认配置文件路径，或者后续从命令行参数中获取
	if err := cfg.Load(); err != nil { // 添加这一行
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 根据模式设置配置
	if *isServer {
		fmt.Println("以服务端模式运行...")
		// 验证服务端必需参数
		if *serverPassword == "" {
			fmt.Println("错误：服务端模式下加密密码（-pw）是必需的。")
			os.Exit(1)
		}

		// 设置服务端配置
		cfg.Get().System.Mode = "server"
		cfg.Get().Peer.Password = *serverPassword // 使用PeerConfig.Password作为共享密码
		cfg.Get().API.Port = *serverPort
		cfg.Get().Web.Port = *webPort
		cfg.Get().Database.Path = *dbPath
		cfg.Get().Log.Level = *logLevel
		cfg.Get().System.DataDir = *dataDir
		cfg.Get().Database.RetentionDays = *retention // 设置数据保留天数

	} else if *isClient {
		fmt.Println("以客户端模式运行...")
		// 验证客户端必需参数
		if *clientName == "" || *serverIP == "" || *serverPort == 0 || *clientPassword == "" {
			fmt.Println("错误：客户端模式下服务器名称（-n）、服务端IP（-ip）、服务端端口（-p）和加密密码（-pw-client）是必需的。")
			os.Exit(1)
		}

		// 设置客户端配置
		cfg.Get().System.Mode = "client"
		cfg.Get().System.Name = *clientName // 将客户端名称设置到SystemConfig.Name
		cfg.Get().Peer.IP = *serverIP
		cfg.Get().Peer.Port = *serverPort
		cfg.Get().Peer.Password = *clientPassword
		cfg.Get().System.Description = *description       // 设置服务器描述
		cfg.Get().System.Tags = strings.Split(*tags, ",") // 设置标签
		cfg.Get().System.Interval = *interval             // 设置监控间隔
		cfg.Get().Log.Level = *logLevel                   // 客户端也应有日志级别
	}

	// 加载并验证配置
	if err := cfg.Load(); err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	// logger.InitLogger(cfg.Get().Log) // 假设有一个初始化日志的函数

	// 根据模式启动不同的服务
	if *isServer {
		fmt.Println("启动服务端...")

		// 初始化数据库
		err := database.Init(&cfg.Get().Database) // Pass address of DatabaseConfig
		if err != nil {
			log.Fatalf("Failed to initialize database: %v", err)
		}
		defer database.Close() // Ensure database connection is closed

		repo := database.NewRepository(database.DB) // Use the global GORM DB instance directly

		// 初始化系统监控
		sysMon := monitor.NewSystemMonitor(time.Duration(cfg.Get().System.Interval) * time.Second)

		// 创建Web服务器
		webServer := web.NewServer(cfg.Get(), repo, sysMon)

		// 加载HTML模板
		if err := webServer.LoadTemplates(); err != nil {
			log.Fatalf("Failed to load web templates: %v", err)
		}

		// 启动Web服务器
		go func() {
			if err := webServer.Start(); err != nil && err != http.ErrServerClosed {
				log.Fatalf("Web server failed to start: %v", err)
			}
		}()

		// 优雅关闭
		quit := make(chan os.Signal, 1)
		signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
		<-quit
		log.Println("Shutting down server...")

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := webServer.Stop(ctx); err != nil {
			log.Fatalf("Server forced to shutdown: %v", err)
		}
		log.Println("Server exited gracefully.")

	} else if *isClient {
		fmt.Println("启动客户端...")
		// 客户端启动逻辑 (目前为空，待实现)
	}
}
