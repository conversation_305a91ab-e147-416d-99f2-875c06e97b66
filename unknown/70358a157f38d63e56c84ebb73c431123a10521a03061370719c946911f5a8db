package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"
	"time"
)

// SecurityTestRunner 安全测试运行器 (Security test runner)
type SecurityTestRunner struct {
	verbose     bool
	benchmark   bool
	longRunning bool
	parallel    bool
	timeout     time.Duration
}

// NewSecurityTestRunner 创建新的安全测试运行器 (Creates a new security test runner)
func NewSecurityTestRunner() *SecurityTestRunner {
	return &SecurityTestRunner{
		timeout: 10 * time.Minute, // 默认10分钟超时 (Default 10-minute timeout)
	}
}

// RunTests 运行安全测试 (Runs security tests)
func (r *SecurityTestRunner) RunTests() error {
	fmt.Println("🔒 Starting Security Feature Integration Tests")
	fmt.Println("=" * 50)
	
	// 构建测试命令 (Build test command)
	args := []string{"test"}
	
	if r.verbose {
		args = append(args, "-v")
	}
	
	if r.parallel {
		args = append(args, "-parallel", "4")
	}
	
	// 设置超时 (Set timeout)
	args = append(args, "-timeout", r.timeout.String())
	
	// 添加测试包 (Add test packages)
	testPackages := []string{
		"./internal/security",
		"./internal/crypto",
		"./internal/protocol",
		"./internal/config",
	}
	
	for _, pkg := range testPackages {
		fmt.Printf("📦 Testing package: %s\n", pkg)
		
		pkgArgs := append(args, pkg)
		
		// 添加测试过滤器 (Add test filters)
		if !r.longRunning {
			pkgArgs = append(pkgArgs, "-short")
		}
		
		// 运行测试 (Run tests)
		cmd := exec.Command("go", pkgArgs...)
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("tests failed for package %s: %w", pkg, err)
		}
		
		fmt.Printf("✅ Package %s tests passed\n\n", pkg)
	}
	
	// 运行基准测试 (Run benchmarks)
	if r.benchmark {
		fmt.Println("🚀 Running Performance Benchmarks")
		fmt.Println("-" * 30)
		
		benchArgs := []string{"test", "-bench=.", "-benchmem", "./internal/security"}
		if r.verbose {
			benchArgs = append(benchArgs, "-v")
		}
		
		cmd := exec.Command("go", benchArgs...)
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("benchmark tests failed: %w", err)
		}
		
		fmt.Println("✅ Benchmark tests completed\n")
	}
	
	// 运行集成测试 (Run integration tests)
	fmt.Println("🔗 Running Integration Tests")
	fmt.Println("-" * 25)
	
	integrationArgs := []string{"test", "-v", "./internal/security", "-run", "TestSecure.*|TestReplay.*|TestKey.*|TestConcurrent.*"}
	if r.longRunning {
		integrationArgs = append(integrationArgs, "-run", "TestLongRunning.*")
	}
	
	cmd := exec.Command("go", integrationArgs...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("integration tests failed: %w", err)
	}
	
	fmt.Println("✅ Integration tests completed\n")
	
	return nil
}

// RunSecurityAudit 运行安全审计 (Runs security audit)
func (r *SecurityTestRunner) RunSecurityAudit() error {
	fmt.Println("🛡️  Running Security Audit")
	fmt.Println("-" * 20)
	
	// 检查依赖漏洞 (Check dependency vulnerabilities)
	fmt.Println("Checking for known vulnerabilities...")
	cmd := exec.Command("go", "list", "-json", "-m", "all")
	output, err := cmd.Output()
	if err != nil {
		log.Printf("Warning: Could not check dependencies: %v", err)
	} else {
		// 简单检查是否有已知的不安全依赖 (Simple check for known unsafe dependencies)
		if strings.Contains(string(output), "vulnerable") {
			fmt.Println("⚠️  Warning: Potentially vulnerable dependencies detected")
		} else {
			fmt.Println("✅ No obvious vulnerable dependencies found")
		}
	}
	
	// 检查代码中的潜在安全问题 (Check for potential security issues in code)
	fmt.Println("Scanning for potential security issues...")
	
	securityPatterns := []string{
		"password.*=.*\".*\"", // 硬编码密码 (Hardcoded passwords)
		"key.*=.*\".*\"",      // 硬编码密钥 (Hardcoded keys)
		"token.*=.*\".*\"",    // 硬编码令牌 (Hardcoded tokens)
	}
	
	for _, pattern := range securityPatterns {
		cmd := exec.Command("grep", "-r", "-i", pattern, "internal/")
		output, err := cmd.Output()
		if err == nil && len(output) > 0 {
			fmt.Printf("⚠️  Warning: Potential security issue found with pattern '%s'\n", pattern)
			if r.verbose {
				fmt.Printf("   %s\n", string(output))
			}
		}
	}
	
	fmt.Println("✅ Security audit completed\n")
	return nil
}

// PrintSummary 打印测试摘要 (Prints test summary)
func (r *SecurityTestRunner) PrintSummary() {
	fmt.Println("📊 Security Test Summary")
	fmt.Println("=" * 25)
	fmt.Println("✅ All security feature tests completed successfully!")
	fmt.Println()
	fmt.Println("Tested Components:")
	fmt.Println("  🔐 AES-256-GCM Encryption")
	fmt.Println("  🔑 Key Management & Rotation")
	fmt.Println("  🛡️  Message Integrity Validation")
	fmt.Println("  🚫 Replay Attack Protection")
	fmt.Println("  📨 Reliable Message Delivery")
	fmt.Println("  ⚙️  Security Configuration Management")
	fmt.Println()
	fmt.Println("Test Types:")
	fmt.Println("  🧪 Unit Tests")
	fmt.Println("  🔗 Integration Tests")
	fmt.Println("  🏃 Concurrent Operations Tests")
	if r.longRunning {
		fmt.Println("  ⏱️  Long-running Stability Tests")
	}
	if r.benchmark {
		fmt.Println("  🚀 Performance Benchmarks")
	}
	fmt.Println()
	fmt.Println("🎉 Security implementation is ready for production!")
}

func main() {
	runner := NewSecurityTestRunner()
	
	// 解析命令行参数 (Parse command line arguments)
	flag.BoolVar(&runner.verbose, "v", false, "Verbose output")
	flag.BoolVar(&runner.benchmark, "bench", false, "Run performance benchmarks")
	flag.BoolVar(&runner.longRunning, "long", false, "Run long-running stability tests")
	flag.BoolVar(&runner.parallel, "parallel", true, "Run tests in parallel")
	flag.DurationVar(&runner.timeout, "timeout", 10*time.Minute, "Test timeout duration")
	
	var audit bool
	flag.BoolVar(&audit, "audit", false, "Run security audit")
	
	flag.Parse()
	
	startTime := time.Now()
	
	// 运行安全审计 (Run security audit)
	if audit {
		if err := runner.RunSecurityAudit(); err != nil {
			log.Fatalf("Security audit failed: %v", err)
		}
	}
	
	// 运行测试 (Run tests)
	if err := runner.RunTests(); err != nil {
		log.Fatalf("Tests failed: %v", err)
	}
	
	// 打印摘要 (Print summary)
	runner.PrintSummary()
	
	fmt.Printf("⏱️  Total execution time: %v\n", time.Since(startTime))
	fmt.Println("🔒 Security testing completed successfully!")
}
