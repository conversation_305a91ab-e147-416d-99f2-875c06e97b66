# 安全配置示例 (Security Configuration Example)
# 此文件展示了所有可用的安全配置选项 (This file shows all available security configuration options)

security:
  # 加密配置 (Encryption Configuration)
  encryption:
    enabled: true                    # 是否启用加密 (Whether encryption is enabled)
    algorithm: "AES-256-GCM"        # 加密算法 (Encryption algorithm: AES-256-GCM, AES-192-GCM, AES-128-GCM)
    key_size: 256                   # 密钥大小 (Key size: 128, 192, 256)

  # 密钥管理配置 (Key Manager Configuration)
  key_manager:
    enabled: true                   # 是否启用密钥管理 (Whether key management is enabled)
    rotation_interval: "24h"        # 密钥轮换间隔 (Key rotation interval)
    key_ttl: "72h"                 # 密钥生存时间 (Key time to live)
    max_key_history: 10            # 最大密钥历史数量 (Maximum key history count)
    auto_rotate: true              # 是否自动轮换 (Whether to auto rotate)
    master_password: "${MASTER_PASSWORD}" # 主密码，建议使用环境变量 (Master password, recommend using environment variable)

  # 认证配置 (Authentication Configuration)
  auth:
    enabled: true                   # 是否启用认证 (Whether authentication is enabled)
    token_ttl: "1h"                # 令牌生存时间 (Token time to live)
    refresh_token_ttl: "24h"       # 刷新令牌生存时间 (Refresh token time to live)
    max_login_attempts: 5          # 最大登录尝试次数 (Maximum login attempts)
    lockout_duration: "15m"        # 锁定持续时间 (Lockout duration)
    enable_replay_protection: true # 启用重放攻击防护 (Enable replay protection)

  # 重放攻击防护配置 (Replay Protection Configuration)
  replay_protection:
    enabled: true                   # 是否启用重放攻击防护 (Whether replay protection is enabled)
    time_window: "5m"              # 时间窗口 (Time window for replay detection)
    nonce_cache: 10000             # Nonce缓存大小 (Nonce cache size)
    cache_ttl: "10m"               # 缓存生存时间 (Cache time to live)

  # 消息完整性配置 (Message Integrity Configuration)
  message_integrity:
    enabled: true                   # 是否启用消息完整性检查 (Whether message integrity check is enabled)
    checksum_algorithm: "SHA256"    # 校验和算法 (Checksum algorithm: SHA256, SHA512, SHA3-256, SHA3-512)
    enable_sequence_validation: true # 启用序列号验证 (Enable sequence validation)
    max_sequence_gap: 100          # 最大序列号间隔 (Maximum sequence gap)

  # 速率限制配置 (Rate Limiting Configuration)
  rate_limiting:
    enabled: true                   # 是否启用速率限制 (Whether rate limiting is enabled)
    requests_per_sec: 100          # 每秒请求数限制 (Requests per second limit)
    burst_size: 200                # 突发大小 (Burst size)
    cleanup_interval: "1m"         # 清理间隔 (Cleanup interval)
    ban_duration: "1h"             # 封禁持续时间 (Ban duration)
    max_violations: 10             # 最大违规次数 (Maximum violations)

# 环境变量覆盖示例 (Environment Variable Override Examples)
# 以下配置可以通过环境变量覆盖 (The following configurations can be overridden by environment variables):
#
# SECURITY_ENCRYPTION_ENABLED=true
# SECURITY_ENCRYPTION_ALGORITHM=AES-256-GCM
# SECURITY_ENCRYPTION_KEY_SIZE=256
#
# SECURITY_KEY_MANAGER_ENABLED=true
# SECURITY_KEY_MANAGER_ROTATION_INTERVAL=24h
# SECURITY_KEY_MANAGER_KEY_TTL=72h
# SECURITY_KEY_MANAGER_MAX_KEY_HISTORY=10
# SECURITY_KEY_MANAGER_AUTO_ROTATE=true
# SECURITY_KEY_MANAGER_MASTER_PASSWORD=your-secure-master-password
#
# SECURITY_AUTH_ENABLED=true
# SECURITY_AUTH_TOKEN_TTL=1h
# SECURITY_AUTH_REFRESH_TOKEN_TTL=24h
# SECURITY_AUTH_MAX_LOGIN_ATTEMPTS=5
# SECURITY_AUTH_LOCKOUT_DURATION=15m
# SECURITY_AUTH_ENABLE_REPLAY_PROTECTION=true
#
# SECURITY_REPLAY_PROTECTION_ENABLED=true
# SECURITY_REPLAY_PROTECTION_TIME_WINDOW=5m
# SECURITY_REPLAY_PROTECTION_NONCE_CACHE=10000
# SECURITY_REPLAY_PROTECTION_CACHE_TTL=10m
#
# SECURITY_MESSAGE_INTEGRITY_ENABLED=true
# SECURITY_MESSAGE_INTEGRITY_CHECKSUM_ALGORITHM=SHA256
# SECURITY_MESSAGE_INTEGRITY_ENABLE_SEQUENCE_VALIDATION=true
# SECURITY_MESSAGE_INTEGRITY_MAX_SEQUENCE_GAP=100
#
# SECURITY_RATE_LIMITING_ENABLED=true
# SECURITY_RATE_LIMITING_REQUESTS_PER_SEC=100
# SECURITY_RATE_LIMITING_BURST_SIZE=200
# SECURITY_RATE_LIMITING_CLEANUP_INTERVAL=1m
# SECURITY_RATE_LIMITING_BAN_DURATION=1h
# SECURITY_RATE_LIMITING_MAX_VIOLATIONS=10

# 安全最佳实践建议 (Security Best Practices Recommendations)
# 1. 使用强密码作为主密码 (Use strong passwords as master password)
# 2. 定期轮换密钥 (Rotate keys regularly)
# 3. 启用所有安全功能 (Enable all security features)
# 4. 监控安全事件 (Monitor security events)
# 5. 定期更新配置 (Update configurations regularly)
# 6. 使用环境变量存储敏感信息 (Use environment variables for sensitive information)
# 7. 限制网络访问 (Restrict network access)
# 8. 启用日志记录 (Enable logging)
# 9. 定期备份配置 (Backup configurations regularly)
# 10. 测试安全配置 (Test security configurations)

# 生产环境建议配置 (Production Environment Recommended Configuration)
# - 启用所有安全功能 (Enable all security features)
# - 使用较短的令牌TTL (Use shorter token TTL)
# - 增加密钥轮换频率 (Increase key rotation frequency)
# - 降低速率限制阈值 (Lower rate limiting thresholds)
# - 启用严格的序列号验证 (Enable strict sequence validation)
# - 使用强加密算法 (Use strong encryption algorithms)

# 开发环境建议配置 (Development Environment Recommended Configuration)
# - 可以禁用某些安全功能以便调试 (Can disable some security features for debugging)
# - 使用较长的令牌TTL (Use longer token TTL)
# - 降低密钥轮换频率 (Reduce key rotation frequency)
# - 放宽速率限制 (Relax rate limiting)
# - 使用较大的序列号间隔 (Use larger sequence gaps)
