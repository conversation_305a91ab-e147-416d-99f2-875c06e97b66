package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/logger"
	"server-monitor/internal/monitor"
	"server-monitor/internal/reporter"
)

func main() {
	var (
		testType = flag.String("type", "all", "测试类型: all, client, reporter, retry")
		duration = flag.Int("duration", 30, "测试持续时间(秒)")
		workers  = flag.Int("workers", 4, "并发工作者数量")
		verbose  = flag.Bool("verbose", false, "显示详细日志")
	)
	flag.Parse()

	fmt.Printf("数据上报模块测试程序\n")
	fmt.Printf("测试类型: %s\n", *testType)
	fmt.Printf("测试时长: %d秒\n", *duration)
	fmt.Printf("并发数: %d\n", *workers)
	fmt.Println("---")

	switch *testType {
	case "client":
		testHTTPClient(*verbose)
	case "reporter":
		testReporter(*duration, *workers, *verbose)
	case "retry":
		testRetryMechanism(*verbose)
	case "all":
		testAll(*duration, *workers, *verbose)
	default:
		log.Fatalf("未知测试类型: %s", *testType)
	}
}

// testAll 测试所有功能
func testAll(duration, workers int, verbose bool) {
	fmt.Println("=== 数据上报模块综合测试 ===")

	testHTTPClient(verbose)
	fmt.Println()

	testRetryMechanism(verbose)
	fmt.Println()

	testReporter(duration, workers, verbose)

	fmt.Println("\n✅ 所有测试完成！")
}

// testHTTPClient 测试HTTP客户端
func testHTTPClient(verbose bool) {
	fmt.Println("=== HTTP客户端测试 ===")

	// 创建配置
	cfg := createTestConfig()

	// 创建日志器
	logConfig := &logger.Config{
		Level:  logger.LevelInfo,
		Format: "text",
		Output: "stdout",
	}
	if verbose {
		logConfig.Level = logger.LevelDebug
	}

	testLogger, err := logger.NewLogger(logConfig)
	if err != nil {
		fmt.Printf("❌ 创建日志器失败: %v\n", err)
		return
	}
	defer testLogger.Close()

	// 创建HTTP客户端
	httpClient := reporter.NewHTTPClient(cfg, testLogger)
	defer httpClient.Close()

	ctx := context.Background()

	// 测试GET请求
	fmt.Println("1. 测试GET请求...")
	response, err := httpClient.Get(ctx, "https://httpbin.org/get", nil)
	if err != nil {
		fmt.Printf("❌ GET请求失败: %v\n", err)
	} else {
		fmt.Printf("✅ GET请求成功，状态码: %d, 延迟: %v\n",
			response.StatusCode, response.Latency)
	}

	// 测试POST请求
	fmt.Println("2. 测试POST请求...")
	testData := map[string]interface{}{
		"message":   "Hello from server monitor",
		"timestamp": time.Now(),
	}

	response, err = httpClient.Post(ctx, "https://httpbin.org/post", testData, nil)
	if err != nil {
		fmt.Printf("❌ POST请求失败: %v\n", err)
	} else {
		fmt.Printf("✅ POST请求成功，状态码: %d, 延迟: %v\n",
			response.StatusCode, response.Latency)
	}

	// 显示统计信息
	stats := httpClient.GetStats()
	fmt.Printf("HTTP客户端统计:\n")
	fmt.Printf("  总请求数: %d\n", stats.TotalRequests)
	fmt.Printf("  成功请求: %d\n", stats.SuccessRequests)
	fmt.Printf("  失败请求: %d\n", stats.FailedRequests)
	fmt.Printf("  平均延迟: %v\n", stats.AverageLatency)
	fmt.Printf("  总字节数: %d\n", stats.TotalBytes)

	fmt.Println("✅ HTTP客户端测试完成")
}

// testRetryMechanism 测试重试机制
func testRetryMechanism(verbose bool) {
	fmt.Println("=== 重试机制测试 ===")

	// 创建日志器
	logConfig := &logger.Config{
		Level:  logger.LevelInfo,
		Format: "text",
		Output: "stdout",
	}
	if verbose {
		logConfig.Level = logger.LevelDebug
	}

	testLogger, err := logger.NewLogger(logConfig)
	if err != nil {
		fmt.Printf("❌ 创建日志器失败: %v\n", err)
		return
	}
	defer testLogger.Close()

	// 创建重试配置
	retryConfig := &reporter.RetryConfig{
		MaxAttempts:  3,
		InitialDelay: 100 * time.Millisecond,
		MaxDelay:     1 * time.Second,
		Multiplier:   2.0,
		Strategy:     reporter.RetryStrategyExponential,
		JitterFactor: 0.1,
		Timeout:      10 * time.Second,
	}

	// 创建重试器
	retryer := reporter.NewRetryer(retryConfig, testLogger)

	ctx := context.Background()

	// 测试1: 总是失败的函数
	fmt.Println("1. 测试总是失败的函数...")
	result := retryer.Execute(ctx, func(ctx context.Context, attempt int) error {
		return fmt.Errorf("模拟失败 (尝试 %d)", attempt)
	})

	if result.Success {
		fmt.Printf("❌ 预期失败但成功了\n")
	} else {
		fmt.Printf("✅ 按预期失败，尝试次数: %d, 总时间: %v\n",
			result.Attempts, result.TotalTime)
	}

	// 测试2: 第3次成功的函数
	fmt.Println("2. 测试第3次成功的函数...")
	attemptCount := 0
	result = retryer.Execute(ctx, func(ctx context.Context, attempt int) error {
		attemptCount++
		if attemptCount < 3 {
			return fmt.Errorf("模拟失败 (尝试 %d)", attempt)
		}
		return nil
	})

	if !result.Success {
		fmt.Printf("❌ 预期成功但失败了: %v\n", result.LastError)
	} else {
		fmt.Printf("✅ 按预期成功，尝试次数: %d, 总时间: %v\n",
			result.Attempts, result.TotalTime)
	}

	// 显示重试统计
	stats := retryer.GetStats()
	fmt.Printf("重试统计:\n")
	fmt.Printf("  总重试次数: %d\n", stats.TotalRetries)
	fmt.Printf("  成功重试: %d\n", stats.SuccessRetries)
	fmt.Printf("  失败重试: %d\n", stats.FailedRetries)
	fmt.Printf("  平均尝试次数: %.2f\n", stats.AverageAttempts)
	fmt.Printf("  平均时间: %v\n", stats.AverageTime)

	fmt.Println("✅ 重试机制测试完成")
}

// testReporter 测试数据上报器
func testReporter(duration, workers int, verbose bool) {
	fmt.Println("=== 数据上报器测试 ===")

	// 创建配置
	cfg := createTestConfig()

	// 创建日志器
	logConfig := &logger.Config{
		Level:  logger.LevelInfo,
		Format: "text",
		Output: "stdout",
	}
	if verbose {
		logConfig.Level = logger.LevelDebug
	}

	testLogger, err := logger.NewLogger(logConfig)
	if err != nil {
		fmt.Printf("❌ 创建日志器失败: %v\n", err)
		return
	}
	defer testLogger.Close()

	// 创建上报器
	httpReporter := reporter.NewHTTPReporter(cfg, testLogger)

	ctx := context.Background()

	// 启动上报器
	if err := httpReporter.Start(ctx); err != nil {
		fmt.Printf("❌ 启动上报器失败: %v\n", err)
		return
	}
	defer httpReporter.Stop(ctx)

	fmt.Printf("✅ 上报器启动成功\n")

	// 测试单个数据上报
	fmt.Println("1. 测试单个数据上报...")
	testData := &reporter.ReportData{
		Type: reporter.ReportTypeSystem,
		Data: map[string]interface{}{
			"cpu_usage":    45.6,
			"memory_usage": 78.2,
			"disk_usage":   23.1,
		},
		Metadata: map[string]interface{}{
			"test": true,
		},
	}

	// 注意：这里使用队列上报，因为我们没有真实的服务器
	if err := httpReporter.QueueReport(testData); err != nil {
		fmt.Printf("❌ 队列上报失败: %v\n", err)
	} else {
		fmt.Printf("✅ 队列上报成功\n")
	}

	// 测试批量数据上报
	fmt.Println("2. 测试批量数据上报...")
	batchData := make([]*reporter.ReportData, 5)
	for i := 0; i < 5; i++ {
		batchData[i] = &reporter.ReportData{
			Type: reporter.ReportTypeMetrics,
			Data: map[string]interface{}{
				"metric_id": i,
				"value":     float64(i * 10),
				"timestamp": time.Now(),
			},
		}

		// 使用队列上报
		if err := httpReporter.QueueReport(batchData[i]); err != nil {
			fmt.Printf("❌ 批量数据 %d 队列上报失败: %v\n", i, err)
		}
	}
	fmt.Printf("✅ 批量数据队列上报完成\n")

	// 测试系统信息上报
	fmt.Println("3. 测试系统信息上报...")
	systemMonitor := monitor.NewSystemMonitor(30 * time.Second)
	systemInfo, err := systemMonitor.GetSystemInfo()
	if err != nil {
		fmt.Printf("❌ 获取系统信息失败: %v\n", err)
	} else {
		if err := httpReporter.ReportSystemInfo(ctx, systemInfo); err != nil {
			fmt.Printf("❌ 系统信息上报失败: %v\n", err)
		} else {
			fmt.Printf("✅ 系统信息上报成功\n")
		}
	}

	// 测试心跳上报
	fmt.Println("4. 测试心跳上报...")
	if err := httpReporter.ReportHeartbeat(ctx); err != nil {
		fmt.Printf("❌ 心跳上报失败: %v\n", err)
	} else {
		fmt.Printf("✅ 心跳上报成功\n")
	}

	// 等待一段时间让上报器处理队列
	fmt.Printf("等待 %d 秒让上报器处理队列...\n", duration)
	time.Sleep(time.Duration(duration) * time.Second)

	// 显示统计信息
	stats := httpReporter.GetStats()
	fmt.Printf("上报器统计:\n")
	fmt.Printf("  总上报数: %d\n", stats.TotalReports)
	fmt.Printf("  成功上报: %d\n", stats.SuccessReports)
	fmt.Printf("  失败上报: %d\n", stats.FailedReports)
	fmt.Printf("  批量上报: %d\n", stats.BatchReports)
	fmt.Printf("  队列大小: %d\n", stats.QueueSize)
	fmt.Printf("  错误率: %.2f%%\n", stats.ErrorRate)
	fmt.Printf("  平均延迟: %v\n", stats.AverageLatency)

	fmt.Println("✅ 数据上报器测试完成")
}

// createTestConfig 创建测试配置
func createTestConfig() *config.Config {
	return &config.Config{
		Servers: []config.ServerConfig{
			{
				Name:     "test-server",
				IP:       "httpbin.org",
				Port:     443,
				Enabled:  true,
				Priority: 1,
			},
		},
	}
}

// 显示健康检查信息
func showHealthCheck(httpClient *reporter.HTTPClient, httpReporter *reporter.HTTPReporter) {
	fmt.Println("\n=== 健康检查信息 ===")

	if httpClient != nil {
		clientHealth := httpClient.HealthCheck()
		if healthData, err := json.MarshalIndent(clientHealth, "", "  "); err == nil {
			fmt.Printf("HTTP客户端健康状态:\n%s\n", string(healthData))
		}
	}

	if httpReporter != nil {
		fmt.Printf("上报器健康状态: %v\n", httpReporter.IsHealthy())
		stats := httpReporter.GetStats()
		if statsData, err := json.MarshalIndent(stats, "", "  "); err == nil {
			fmt.Printf("上报器统计信息:\n%s\n", string(statsData))
		}
	}
}
