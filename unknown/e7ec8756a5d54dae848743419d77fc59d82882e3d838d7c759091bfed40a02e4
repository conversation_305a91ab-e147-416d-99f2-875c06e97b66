# Server Monitor Configuration Example
# 服务器监控系统配置示例文件
# 复制此文件为 config.yaml 并根据实际环境修改配置

# 系统配置
system:
  name: "Server Monitor"
  version: "1.0.0"
  environment: "production"  # 环境: development, testing, staging, production
  data_dir: "./data"         # 数据目录
  pid_file: "./data/monitor.pid"  # PID文件路径
  mode: "both"              # 运行模式: server(仅服务端), client(仅客户端), both(双模式)

# 数据库配置
database:
  path: "./data/monitor.db"  # SQLite数据库文件路径
  max_open_conns: 25        # 最大打开连接数
  max_idle_conns: 5         # 最大空闲连接数
  conn_max_lifetime: "1h"   # 连接最大生存时间
  conn_max_idle_time: "10m" # 连接最大空闲时间
  enable_wal: true          # 启用WAL模式
  enable_foreign_key: true  # 启用外键约束
  busy_timeout: "30s"       # 忙碌超时时间

# 调度器配置 - 控制测试执行时间
scheduler:
  enabled: true             # 是否启用调度器
  mode: "odd"              # 调度模式: odd(奇数小时), even(偶数小时), both(全部小时)
  timezone: "Asia/Shanghai" # 时区设置
  start_hour: 0            # 开始小时 (0-23)
  end_hour: 23             # 结束小时 (0-23)
  max_concurrent: 4        # 最大并发测试数
  retry_attempts: 3        # 重试次数
  retry_interval: "5m"     # 重试间隔

# 服务器列表配置 - 定义要测试的12台服务器
servers:
  # 主要服务器组
  - name: "server-01"
    ip: "*************"
    port: 5201
    location: "Beijing"
    provider: "Alibaba Cloud"
    enabled: true
    priority: 1
    tags: ["primary", "beijing", "tier1"]
    
  - name: "server-02"
    ip: "*************"
    port: 5201
    location: "Shanghai"
    provider: "Tencent Cloud"
    enabled: true
    priority: 2
    tags: ["primary", "shanghai", "tier1"]
    
  - name: "server-03"
    ip: "*************"
    port: 5201
    location: "Guangzhou"
    provider: "Huawei Cloud"
    enabled: true
    priority: 3
    tags: ["secondary", "guangzhou", "tier2"]
    
  - name: "server-04"
    ip: "*************"
    port: 5201
    location: "Shenzhen"
    provider: "Baidu Cloud"
    enabled: true
    priority: 4
    tags: ["secondary", "shenzhen", "tier2"]
    
  # 更多服务器配置...
  # 根据实际需要添加剩余的8台服务器

# 对端OpenWRT配置 - 双机同步设置
peer:
  ip: "*************"       # 对端OpenWRT IP地址
  port: 8443               # 对端API端口
  username: "admin"        # 认证用户名
  password: "your_password_here"  # 认证密码 (请修改为实际密码)
  timeout: "30s"           # 连接超时时间
  enabled: false           # 是否启用对端同步 (生产环境请设为true)

# iPerf3测试参数配置
test:
  duration: "30s"          # 单次测试持续时间
  parallel: 4              # 并行连接数
  window_size: "64K"       # TCP窗口大小
  buffer_length: "128K"    # 缓冲区长度
  bandwidth: "0"           # 带宽限制 (0表示不限制)
  protocol: "tcp"          # 协议类型: tcp, udp, both
  reverse: false           # 是否反向测试
  bidir: false            # 是否双向测试
  timeout: "60s"          # 测试超时时间
  connect_timeout: "10s"   # 连接超时时间
  max_retries: 3          # 最大重试次数
  retry_delay: "5s"       # 重试延迟

# Web界面配置
web:
  enabled: true           # 是否启用Web界面
  port: 8080             # Web服务端口
  host: "0.0.0.0"        # 监听地址 (0.0.0.0表示所有接口)
  static_dir: "./web/static"      # 静态文件目录
  template_dir: "./web/templates" # 模板文件目录
  tls:
    enabled: false       # 是否启用HTTPS
    cert_file: "/path/to/cert.pem"  # SSL证书文件
    key_file: "/path/to/key.pem"    # SSL私钥文件

# API服务配置
api:
  enabled: true          # 是否启用API服务
  port: 8443            # API服务端口
  host: "0.0.0.0"       # 监听地址
  prefix: "/api/v1"     # API路径前缀
  timeout: "30s"        # 请求超时时间
  rate_limit: 100       # 速率限制 (请求/分钟)
  tls:
    enabled: false      # 是否启用HTTPS
    cert_file: "/path/to/cert.pem"  # SSL证书文件
    key_file: "/path/to/key.pem"    # SSL私钥文件
  cors:
    enabled: true       # 是否启用CORS
    allow_origins: ["*"]  # 允许的源
    allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allow_headers: ["*"]

# 数据同步配置 - 双OpenWRT数据同步
sync:
  enabled: false        # 是否启用数据同步 (生产环境请设为true)
  interval: "5m"        # 同步间隔
  batch_size: 100       # 批量同步大小
  max_retries: 3        # 最大重试次数
  retry_interval: "30s" # 重试间隔
  timeout: "30s"        # 同步超时时间

# 日志配置
log:
  level: "info"         # 日志级别: debug, info, warn, error, fatal, panic
  format: "text"        # 日志格式: json, text
  output: "stdout"      # 输出目标: stdout, stderr, file
  file: "./logs/monitor.log"  # 日志文件路径 (当output为file时)
  max_size: 100         # 单个日志文件最大大小 (MB)
  max_backups: 3        # 保留的日志文件数量
  max_age: 28           # 日志文件保留天数
  compress: true        # 是否压缩旧日志文件

# 环境变量覆盖说明:
# 所有配置项都可以通过环境变量覆盖，格式为: MONITOR_<SECTION>_<KEY>
# 例如:
#   MONITOR_SYSTEM_MODE=server
#   MONITOR_DATABASE_PATH=/custom/path/monitor.db
#   MONITOR_WEB_PORT=9090
#   MONITOR_LOG_LEVEL=debug

# 生产环境建议:
# 1. 设置 system.environment 为 "production"
# 2. 启用 peer.enabled 和 sync.enabled 进行双机同步
# 3. 配置适当的 log.level (建议 "info" 或 "warn")
# 4. 根据服务器性能调整 scheduler.max_concurrent
# 5. 为安全考虑，启用 API 和 Web 的 TLS 配置
