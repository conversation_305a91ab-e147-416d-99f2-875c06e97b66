package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/monitor"
	"server-monitor/internal/web"
)

func main() {
	var (
		configFile = flag.String("config", "./configs/config.yaml", "配置文件路径")
		port       = flag.Int("port", 8080, "Web服务端口")
		host       = flag.String("host", "127.0.0.1", "Web服务监听地址")
	)
	flag.Parse()

	fmt.Printf("Web界面测试程序\n")
	fmt.Printf("配置文件: %s\n", *configFile)
	fmt.Printf("监听地址: %s:%d\n", *host, *port)
	fmt.Println("---")

	// 加载配置
	configManager := config.NewManager(*configFile)
	if err := configManager.Load(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	cfg := configManager.Get()

	// 覆盖Web配置
	cfg.Web.Host = *host
	cfg.Web.Port = *port
	cfg.Web.Enabled = true
	cfg.Web.TemplateDir = "" // 使用默认模板

	fmt.Printf("配置加载成功\n")

	// 初始化数据库
	repo, err := database.InitDatabase(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.Close()

	fmt.Printf("数据库初始化成功\n")

	// 初始化测试数据
	if err := database.InitTestServers(repo); err != nil {
		log.Printf("Warning: Failed to initialize test data: %v", err)
	} else {
		fmt.Printf("测试数据初始化成功\n")
	}

	// 创建系统监控器
	systemMonitor := monitor.NewSystemMonitor(5 * time.Second)

	// 创建Web服务器
	webServer := web.NewServer(cfg, repo, systemMonitor)

	// 加载模板
	if err := webServer.LoadTemplates(); err != nil {
		log.Fatalf("Failed to load templates: %v", err)
	}

	fmt.Printf("模板加载成功\n")

	// 设置信号处理
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// 启动Web服务器
	go func() {
		fmt.Printf("启动Web服务器...\n")
		if err := webServer.Start(); err != nil {
			log.Printf("Web server error: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(1 * time.Second)

	// 显示访问信息
	showAccessInfo(*host, *port)

	fmt.Println("\nWeb服务器已启动，按 Ctrl+C 停止...")

	// 等待信号
	<-sigCh
	fmt.Println("\n收到停止信号，正在停止Web服务器...")

	// 停止Web服务器
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := webServer.Stop(ctx); err != nil {
		log.Printf("Error stopping Web server: %v", err)
	}

	fmt.Println("Web服务器已停止")
}

func showAccessInfo(host string, port int) {
	baseURL := fmt.Sprintf("http://%s:%d", host, port)

	fmt.Printf("\n=== Web界面访问地址 ===\n")

	fmt.Printf("主页: %s/\n", baseURL)
	fmt.Printf("系统监控: %s/system\n", baseURL)
	fmt.Printf("服务器管理: %s/servers\n", baseURL)
	fmt.Printf("统计信息: %s/stats\n", baseURL)

	fmt.Printf("\n=== 数据API端点 ===\n")
	fmt.Printf("系统数据: %s/data/system\n", baseURL)
	fmt.Printf("服务器数据: %s/data/servers\n", baseURL)
	fmt.Printf("统计数据: %s/data/stats\n", baseURL)

	fmt.Printf("\n=== 功能特性 ===\n")
	fmt.Printf("✅ 响应式设计，支持移动设备\n")
	fmt.Printf("✅ 实时数据更新（自动刷新）\n")
	fmt.Printf("✅ 系统资源监控可视化\n")
	fmt.Printf("✅ 服务器管理界面\n")
	fmt.Printf("✅ 统计信息展示\n")
	fmt.Printf("✅ 现代化UI设计\n")

	fmt.Printf("\n请在浏览器中访问: %s\n", baseURL)
}
