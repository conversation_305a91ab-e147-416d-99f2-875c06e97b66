# Server Monitor Basic Configuration Template
# 服务器监控系统基础配置模板
# 
# 使用说明：
# 1. 复制此文件为 config.yaml
# 2. 根据实际环境修改配置项
# 3. 取消注释需要的配置项

# 系统基础配置
system:
  name: "Server Monitor"           # 系统名称
  version: "1.0.0"                # 版本号
  environment: "production"       # 运行环境: development, testing, staging, production
  data_dir: "./data"              # 数据存储目录
  pid_file: "./data/monitor.pid"  # 进程ID文件路径
  mode: "both"                    # 运行模式: server, client, both

# 数据库配置
database:
  path: "./data/monitor.db"       # SQLite数据库文件路径
  max_open_conns: 25             # 最大打开连接数
  max_idle_conns: 5              # 最大空闲连接数
  conn_max_lifetime: "1h"        # 连接最大生存时间
  conn_max_idle_time: "10m"      # 连接最大空闲时间
  enable_wal: true               # 启用WAL模式
  enable_foreign_key: true       # 启用外键约束
  busy_timeout: "30s"            # 忙碌超时时间

# 调度器配置
scheduler:
  enabled: true                  # 是否启用调度器
  mode: "odd"                   # 调度模式: odd(奇数小时), even(偶数小时), both(全部小时)
  timezone: "Asia/Shanghai"      # 时区设置
  start_hour: 0                 # 开始小时 (0-23)
  end_hour: 23                  # 结束小时 (0-23)
  max_concurrent: 4             # 最大并发测试数
  retry_attempts: 3             # 重试次数
  retry_interval: "5m"          # 重试间隔

# 服务器列表配置（示例）
servers:
  - name: "server-01"            # 服务器名称
    ip: "*************"         # 服务器IP地址
    port: 5201                  # iPerf3服务端口
    location: "Beijing"         # 服务器位置
    provider: "Alibaba Cloud"   # 服务提供商
    enabled: true               # 是否启用
    priority: 1                 # 优先级
    tags: ["primary", "beijing"] # 标签

# 对端OpenWRT配置（双机同步）
peer:
  ip: ""                        # 对端IP地址（留空表示禁用）
  port: 8443                   # 对端API端口
  username: "admin"            # 认证用户名
  password: ""                 # 认证密码（请设置强密码）
  timeout: "30s"               # 连接超时时间
  enabled: false               # 是否启用对端同步

# iPerf3测试参数配置
test:
  duration: "30s"              # 单次测试持续时间
  parallel: 4                  # 并行连接数
  window_size: "64K"           # TCP窗口大小
  buffer_length: "128K"        # 缓冲区长度
  bandwidth: "0"               # 带宽限制 (0表示不限制)
  protocol: "tcp"              # 协议类型: tcp, udp, both
  reverse: false               # 是否反向测试
  bidir: false                 # 是否双向测试
  timeout: "60s"               # 测试超时时间
  connect_timeout: "10s"       # 连接超时时间
  max_retries: 3               # 最大重试次数
  retry_delay: "5s"            # 重试延迟

# Web界面配置
web:
  enabled: true                # 是否启用Web界面
  port: 8080                   # Web服务端口
  host: "0.0.0.0"             # 监听地址
  static_dir: "./web/static"   # 静态文件目录
  template_dir: "./web/templates" # 模板文件目录
  tls:
    enabled: false             # 是否启用HTTPS
    cert_file: ""              # SSL证书文件路径
    key_file: ""               # SSL私钥文件路径

# API服务配置
api:
  enabled: true                # 是否启用API服务
  port: 8443                   # API服务端口
  host: "0.0.0.0"             # 监听地址
  prefix: "/api/v1"            # API路径前缀
  timeout: "30s"               # 请求超时时间
  rate_limit: 100              # 速率限制 (请求/分钟)
  tls:
    enabled: false             # 是否启用HTTPS
    cert_file: ""              # SSL证书文件路径
    key_file: ""               # SSL私钥文件路径
  cors:
    enabled: true              # 是否启用CORS
    allow_origins: ["*"]       # 允许的源
    allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allow_headers: ["*"]

# 数据同步配置（双OpenWRT数据同步）
sync:
  enabled: false               # 是否启用数据同步
  interval: "5m"               # 同步间隔
  batch_size: 100              # 批量同步大小
  max_retries: 3               # 最大重试次数
  retry_interval: "30s"        # 重试间隔
  timeout: "30s"               # 同步超时时间

# 日志配置
log:
  level: "info"                # 日志级别: debug, info, warn, error, fatal, panic
  format: "text"               # 日志格式: json, text
  output: "stdout"             # 输出目标: stdout, stderr, file
  file: "./logs/monitor.log"   # 日志文件路径 (当output为file时)
  max_size: 100                # 单个日志文件最大大小 (MB)
  max_backups: 3               # 保留的日志文件数量
  max_age: 28                  # 日志文件保留天数
  compress: true               # 是否压缩旧日志文件

# 环境变量覆盖说明:
# 所有配置项都可以通过环境变量覆盖，格式为: MONITOR_<SECTION>_<KEY>
# 例如:
#   MONITOR_SYSTEM_MODE=server
#   MONITOR_DATABASE_PATH=/custom/path/monitor.db
#   MONITOR_WEB_PORT=9090
#   MONITOR_LOG_LEVEL=debug

# 快速配置示例:
# 
# 1. 仅服务端模式:
#    system.mode: "server"
#    web.enabled: true
#    api.enabled: true
#
# 2. 仅客户端模式:
#    system.mode: "client"
#    web.enabled: false
#    api.enabled: false
#
# 3. 双机同步模式:
#    peer.enabled: true
#    peer.ip: "*************"
#    peer.password: "your_password"
#    sync.enabled: true
