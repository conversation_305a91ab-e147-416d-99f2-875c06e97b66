package api

import (
	"fmt"
	"net"
	"regexp"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
)

// CustomValidator 自定义验证器
type CustomValidator struct {
	validator *validator.Validate
}

// NewCustomValidator 创建新的自定义验证器
func NewCustomValidator() *CustomValidator {
	v := validator.New()

	// 注册自定义验证规则
	v.RegisterValidation("hostname", validateHostname)
	v.RegisterValidation("ip_address", validateIPAddress)
	v.RegisterValidation("server_name", validateServerName)
	v.RegisterValidation("alert_level", validateAlertLevel)
	v.RegisterValidation("alert_status", validateAlertStatus)
	v.RegisterValidation("alert_operator", validateAlertOperator)
	v.RegisterValidation("os_type", validateOSType)
	v.RegisterValidation("time_range", validateTimeRange)

	return &CustomValidator{validator: v}
}

// ValidateStruct 验证结构体
func (cv *CustomValidator) ValidateStruct(obj interface{}) error {
	return cv.validator.Struct(obj)
}

// 自定义验证规则实现

// validateHostname 验证主机名格式
func validateHostname(fl validator.FieldLevel) bool {
	hostname := fl.Field().String()
	if hostname == "" {
		return false
	}

	// 主机名长度限制
	if len(hostname) > 253 {
		return false
	}

	// 主机名格式验证
	hostnameRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
	return hostnameRegex.MatchString(hostname)
}

// validateIPAddress 验证IP地址格式
func validateIPAddress(fl validator.FieldLevel) bool {
	ip := fl.Field().String()
	return net.ParseIP(ip) != nil
}

// validateServerName 验证服务器名称
func validateServerName(fl validator.FieldLevel) bool {
	name := fl.Field().String()
	if name == "" {
		return false
	}

	// 长度限制
	if len(name) < 2 || len(name) > 100 {
		return false
	}

	// 只允许字母、数字、下划线、连字符和空格
	nameRegex := regexp.MustCompile(`^[a-zA-Z0-9_\-\s]+$`)
	return nameRegex.MatchString(name)
}

// validateAlertLevel 验证告警级别
func validateAlertLevel(fl validator.FieldLevel) bool {
	level := fl.Field().String()
	validLevels := map[string]bool{
		"info":     true,
		"warning":  true,
		"critical": true,
	}
	return validLevels[level]
}

// validateAlertStatus 验证告警状态
func validateAlertStatus(fl validator.FieldLevel) bool {
	status := fl.Field().String()
	validStatuses := map[string]bool{
		"active":   true,
		"resolved": true,
	}
	return validStatuses[status]
}

// validateAlertOperator 验证告警操作符
func validateAlertOperator(fl validator.FieldLevel) bool {
	operator := fl.Field().String()
	validOperators := map[string]bool{
		">":  true,
		"<":  true,
		"=":  true,
		">=": true,
		"<=": true,
		"!=": true,
	}
	return validOperators[operator]
}

// validateOSType 验证操作系统类型
func validateOSType(fl validator.FieldLevel) bool {
	osType := fl.Field().String()
	validOSTypes := map[string]bool{
		"linux":   true,
		"windows": true,
		"macos":   true,
		"freebsd": true,
		"openbsd": true,
		"netbsd":  true,
		"solaris": true,
	}
	return validOSTypes[strings.ToLower(osType)]
}

// validateTimeRange 验证时间范围
func validateTimeRange(fl validator.FieldLevel) bool {
	timeStr := fl.Field().String()
	if timeStr == "" {
		return true // 空值允许
	}

	// 尝试解析RFC3339格式
	_, err := time.Parse(time.RFC3339, timeStr)
	return err == nil
}

// 请求验证结构体

// CreateServerRequest 创建服务器请求
type CreateServerRequest struct {
	Name        string `json:"name" validate:"required,server_name"`
	IPAddress   string `json:"ip_address" validate:"required,ip_address"`
	Hostname    string `json:"hostname" validate:"required,hostname"`
	OSType      string `json:"os_type" validate:"required,os_type"`
	OSVersion   string `json:"os_version" validate:"required,min=1,max=100"`
	Description string `json:"description" validate:"max=500"`
	Tags        string `json:"tags" validate:"max=200"`
}

// UpdateServerRequest 更新服务器请求
type UpdateServerRequest struct {
	Name        string `json:"name" validate:"omitempty,server_name"`
	IPAddress   string `json:"ip_address" validate:"omitempty,ip_address"`
	Hostname    string `json:"hostname" validate:"omitempty,hostname"`
	OSType      string `json:"os_type" validate:"omitempty,os_type"`
	OSVersion   string `json:"os_version" validate:"omitempty,min=1,max=100"`
	Description string `json:"description" validate:"max=500"`
	Tags        string `json:"tags" validate:"max=200"`
	IsActive    *bool  `json:"is_active"` // 使用指针以区分false和未设置
}

// CreateAlertRequest 创建告警请求
type CreateAlertRequest struct {
	ServerID  uint    `json:"server_id" validate:"required,min=1"`
	Metric    string  `json:"metric" validate:"required,min=1,max=50"`
	Threshold float64 `json:"threshold" validate:"required"`
	Operator  string  `json:"operator" validate:"required,alert_operator"`
	Value     float64 `json:"value" validate:"required"`
	Message   string  `json:"message" validate:"max=1000"`
	Level     string  `json:"level" validate:"required,alert_level"`
}

// UpdateAlertStatusRequest 更新告警状态请求
type UpdateAlertStatusRequest struct {
	Status string `json:"status" validate:"required,alert_status"`
}

// QueryTimeRangeRequest 时间范围查询请求
type QueryTimeRangeRequest struct {
	StartTime string `json:"start_time" validate:"omitempty,time_range"`
	EndTime   string `json:"end_time" validate:"omitempty,time_range"`
}

// 业务规则验证

// ValidateCreateServer 验证创建服务器请求
func (cv *CustomValidator) ValidateCreateServer(req *CreateServerRequest) error {
	if err := cv.ValidateStruct(req); err != nil {
		return cv.formatValidationError(err)
	}

	// 业务规则验证
	if strings.TrimSpace(req.Name) == "" {
		return fmt.Errorf("server name cannot be empty or whitespace only")
	}

	return nil
}

// ValidateUpdateServer 验证更新服务器请求
func (cv *CustomValidator) ValidateUpdateServer(req *UpdateServerRequest) error {
	if err := cv.ValidateStruct(req); err != nil {
		return cv.formatValidationError(err)
	}

	// 至少要有一个字段被更新
	hasUpdate := req.Name != "" || req.IPAddress != "" || req.Hostname != "" ||
		req.OSType != "" || req.OSVersion != "" || req.Description != "" ||
		req.Tags != "" || req.IsActive != nil

	if !hasUpdate {
		return fmt.Errorf("at least one field must be provided for update")
	}

	return nil
}

// ValidateCreateAlert 验证创建告警请求
func (cv *CustomValidator) ValidateCreateAlert(req *CreateAlertRequest) error {
	if err := cv.ValidateStruct(req); err != nil {
		return cv.formatValidationError(err)
	}

	// 业务规则验证
	if req.Threshold < 0 || req.Threshold > 100 {
		return fmt.Errorf("threshold must be between 0 and 100")
	}

	if req.Value < 0 {
		return fmt.Errorf("value cannot be negative")
	}

	return nil
}

// ValidateTimeRange 验证时间范围
func (cv *CustomValidator) ValidateTimeRange(startTime, endTime string) error {
	if startTime == "" && endTime == "" {
		return nil // 都为空是允许的
	}

	var start, end time.Time
	var err error

	if startTime != "" {
		start, err = time.Parse(time.RFC3339, startTime)
		if err != nil {
			return fmt.Errorf("invalid start_time format: %v", err)
		}
	}

	if endTime != "" {
		end, err = time.Parse(time.RFC3339, endTime)
		if err != nil {
			return fmt.Errorf("invalid end_time format: %v", err)
		}
	}

	// 如果两个时间都提供了，检查顺序
	if !start.IsZero() && !end.IsZero() {
		if start.After(end) {
			return fmt.Errorf("start_time cannot be after end_time")
		}

		// 检查时间范围是否合理（不超过1年）
		if end.Sub(start) > 365*24*time.Hour {
			return fmt.Errorf("time range cannot exceed 1 year")
		}
	}

	return nil
}

// formatValidationError 格式化验证错误
func (cv *CustomValidator) formatValidationError(err error) error {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		var messages []string
		for _, e := range validationErrors {
			message := cv.getValidationMessage(e)
			messages = append(messages, message)
		}
		return fmt.Errorf("validation failed: %s", strings.Join(messages, "; "))
	}
	return err
}

// getValidationMessage 获取验证错误消息
func (cv *CustomValidator) getValidationMessage(e validator.FieldError) string {
	field := e.Field()
	tag := e.Tag()

	switch tag {
	case "required":
		return fmt.Sprintf("%s is required", field)
	case "min":
		return fmt.Sprintf("%s must be at least %s characters", field, e.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters", field, e.Param())
	case "hostname":
		return fmt.Sprintf("%s must be a valid hostname", field)
	case "ip_address":
		return fmt.Sprintf("%s must be a valid IP address", field)
	case "server_name":
		return fmt.Sprintf("%s must be a valid server name (2-100 characters, alphanumeric, underscore, hyphen, space)", field)
	case "alert_level":
		return fmt.Sprintf("%s must be one of: info, warning, critical", field)
	case "alert_status":
		return fmt.Sprintf("%s must be one of: active, resolved", field)
	case "alert_operator":
		return fmt.Sprintf("%s must be one of: >, <, =, >=, <=, !=", field)
	case "os_type":
		return fmt.Sprintf("%s must be a valid OS type", field)
	case "time_range":
		return fmt.Sprintf("%s must be in RFC3339 format", field)
	default:
		return fmt.Sprintf("%s validation failed", field)
	}
}
