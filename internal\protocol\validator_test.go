package protocol

import (
	"testing"
	"time"
)

func TestNewMessageValidator(t *testing.T) {
	// 测试默认配置 (Test default configuration)
	validator := NewMessageValidator(nil)
	if validator == nil {
		t.Fatal("Validator should not be nil")
	}

	// 测试自定义配置 (Test custom configuration)
	config := &ValidatorConfig{
		EnableSequenceValidation: false,
		EnableChecksumValidation: true,
		EnableReplayProtection:   false,
		MaxSequenceGap:           500,
	}

	validator = NewMessageValidator(config)
	if validator.enableSequenceValidation {
		t.Error("Sequence validation should be disabled")
	}
	if !validator.enableChecksumValidation {
		t.Error("Checksum validation should be enabled")
	}
	if validator.enableReplayProtection {
		t.Error("Replay protection should be disabled")
	}
	if validator.maxSequenceGap != 500 {
		t.Errorf("Expected max sequence gap 500, got %d", validator.maxSequenceGap)
	}
}

func TestMessageValidatorValidateMessage(t *testing.T) {
	validator := NewMessageValidator(DefaultValidatorConfig())

	// 创建有效消息 (Create valid message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	// 准备消息 (Prepare message)
	msg.SetSequenceNumber(1)
	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetChecksum()

	// 验证应该通过 (Validation should pass)
	err := validator.ValidateMessage(msg)
	if err != nil {
		t.Errorf("Message validation should pass: %v", err)
	}

	// 测试序列号验证 (Test sequence number validation)
	msg2 := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser2",
		Password: "testpass2",
		ClientID: "client_002",
		Version:  "1.0.0",
	})
	msg2.SetSequenceNumber(3) // 跳过序列号2 (Skip sequence number 2)
	msg2.Timestamp = time.Now().UnixNano()
	msg2.GenerateAndSetChecksum()

	err = validator.ValidateMessage(msg2)
	if err != nil {
		t.Errorf("Message validation should pass with sequence gap: %v", err)
	}

	// 测试序列号回退 (Test sequence number rollback)
	msg3 := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser3",
		Password: "testpass3",
		ClientID: "client_003",
		Version:  "1.0.0",
	})
	msg3.SetSequenceNumber(2) // 序列号回退 (Sequence number rollback)
	msg3.Timestamp = time.Now().UnixNano()
	msg3.GenerateAndSetChecksum()

	err = validator.ValidateMessage(msg3)
	if err == nil {
		t.Error("Message validation should fail with sequence number rollback")
	}
}

func TestMessageValidatorSequenceNumberTracking(t *testing.T) {
	validator := NewMessageValidator(DefaultValidatorConfig())

	sender := "client1"

	// 初始状态应该没有序列号记录 (Initially should have no sequence number record)
	_, exists := validator.GetLastSequenceNumber(sender)
	if exists {
		t.Error("Should not have sequence number record initially")
	}

	// 发送第一条消息 (Send first message)
	msg1 := NewApplicationMessage(MessageTypeAuth, sender, "server1", AuthData{
		Username: "user1",
		Password: "pass1",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg1.SetSequenceNumber(10)
	msg1.Timestamp = time.Now().UnixNano()
	msg1.GenerateAndSetChecksum()

	err := validator.ValidateMessage(msg1)
	if err != nil {
		t.Errorf("First message should be valid: %v", err)
	}

	// 检查序列号记录 (Check sequence number record)
	lastSeq, exists := validator.GetLastSequenceNumber(sender)
	if !exists {
		t.Error("Should have sequence number record after first message")
	}
	if lastSeq != 10 {
		t.Errorf("Expected last sequence number 10, got %d", lastSeq)
	}

	// 重置序列号跟踪 (Reset sequence number tracking)
	validator.ResetSequenceTracker(sender)
	_, exists = validator.GetLastSequenceNumber(sender)
	if exists {
		t.Error("Should not have sequence number record after reset")
	}
}

func TestMessageValidatorBatchValidation(t *testing.T) {
	validator := NewMessageValidator(DefaultValidatorConfig())

	// 创建消息批次 (Create message batch)
	messages := make([]*ApplicationMessage, 3)
	for i := 0; i < 3; i++ {
		msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
			Username: "testuser",
			Password: "testpass",
			ClientID: "client_001",
			Version:  "1.0.0",
		})
		msg.SetSequenceNumber(uint64(i + 1))
		msg.Timestamp = time.Now().UnixNano()
		msg.GenerateAndSetChecksum()
		messages[i] = msg
	}

	// 批量验证 (Batch validation)
	errors := validator.ValidateMessageBatch(messages)
	if len(errors) != 3 {
		t.Errorf("Expected 3 validation results, got %d", len(errors))
	}

	// 前两条消息应该有效 (First two messages should be valid)
	for i := 0; i < 2; i++ {
		if errors[i] != nil {
			t.Errorf("Message %d should be valid: %v", i, errors[i])
		}
	}
}

func TestMessageIntegrityChecker(t *testing.T) {
	checker := NewMessageIntegrityChecker()

	// 创建有效消息 (Create valid message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg.SetSequenceNumber(1)
	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetChecksum()

	// 完整性检查应该通过 (Integrity check should pass)
	err := checker.CheckMessageIntegrity(msg)
	if err != nil {
		t.Errorf("Integrity check should pass: %v", err)
	}

	// 测试未来时间戳 (Test future timestamp)
	futureMsg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	futureMsg.SetSequenceNumber(2)
	futureMsg.Timestamp = (time.Now().Unix() + 400) * 1e9 // 400秒后 (400 seconds in future)
	futureMsg.GenerateAndSetChecksum()

	err = checker.CheckMessageIntegrity(futureMsg)
	if err == nil {
		t.Error("Integrity check should fail for future timestamp")
	}

	// 测试过期时间戳 (Test expired timestamp)
	expiredMsg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	expiredMsg.SetSequenceNumber(3)
	expiredMsg.Timestamp = (time.Now().Unix() - 3700) * 1e9 // 3700秒前 (3700 seconds ago)
	expiredMsg.GenerateAndSetChecksum()

	err = checker.CheckMessageIntegrity(expiredMsg)
	if err == nil {
		t.Error("Integrity check should fail for expired timestamp")
	}
}

func TestSequenceNumberManager(t *testing.T) {
	manager := NewSequenceNumberManager()

	// 创建消息 (Create message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	// 初始状态应该没有序列号和校验和 (Initially should have no sequence number and checksum)
	_, hasSeqNum := msg.GetSequenceNumber()
	_, hasChecksum := msg.GetChecksum()
	if hasSeqNum {
		t.Error("Message should not have sequence number initially")
	}
	if hasChecksum {
		t.Error("Message should not have checksum initially")
	}

	// 准备消息 (Prepare message)
	manager.PrepareMessage(msg)

	// 验证序列号和校验和已设置 (Verify sequence number and checksum are set)
	seqNum, hasSeqNum := msg.GetSequenceNumber()
	checksum, hasChecksum := msg.GetChecksum()
	if !hasSeqNum {
		t.Error("Message should have sequence number after preparation")
	}
	if !hasChecksum {
		t.Error("Message should have checksum after preparation")
	}
	if seqNum == 0 {
		t.Error("Sequence number should not be zero")
	}
	if checksum == "" {
		t.Error("Checksum should not be empty")
	}

	// 验证校验和正确性 (Verify checksum correctness)
	err := msg.ValidateChecksum()
	if err != nil {
		t.Errorf("Prepared message checksum should be valid: %v", err)
	}
}

func TestValidatorConfigUpdate(t *testing.T) {
	validator := NewMessageValidator(DefaultValidatorConfig())

	// 更新配置 (Update configuration)
	newConfig := &ValidatorConfig{
		EnableSequenceValidation: false,
		EnableChecksumValidation: false,
		EnableReplayProtection:   false,
		MaxSequenceGap:           2000,
	}

	validator.UpdateConfig(newConfig)

	// 验证配置已更新 (Verify configuration is updated)
	if validator.enableSequenceValidation {
		t.Error("Sequence validation should be disabled")
	}
	if validator.enableChecksumValidation {
		t.Error("Checksum validation should be disabled")
	}
	if validator.enableReplayProtection {
		t.Error("Replay protection should be disabled")
	}
	if validator.maxSequenceGap != 2000 {
		t.Errorf("Expected max sequence gap 2000, got %d", validator.maxSequenceGap)
	}
}
