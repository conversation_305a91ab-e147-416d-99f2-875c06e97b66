<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - 服务器监控系统</title>
    
    <!-- Tailwind CSS -->
    <link href="/static/css/output.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        .progress-bar {
            transition: width 0.5s ease-in-out;
        }
        
        .server-card {
            transition: all 0.3s ease;
        }
        
        .server-card:hover {
            transform: translateY(-2px);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online {
            background-color: #10b981;
            box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
        }
        
        .status-offline {
            background-color: #ef4444;
            box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
        }
        
        .status-warning {
            background-color: #f59e0b;
            box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-white via-gray-50 to-gray-100 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">服务器监控系统</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="/" class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">仪表板</a>
                        <a href="/servers" class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium">服务器管理</a>
                        <a href="/system" class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium">系统监控</a>
                        <a href="/stats" class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium">统计信息</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <span class="text-sm text-gray-500">{{.timestamp}}</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {{template "content" .}}
    </main>

    <!-- JavaScript 文件 -->
    <script src="/static/js/charts.js"></script>
    <script src="/static/js/websocket.js"></script>
    <script src="/static/js/dashboard.js"></script>
    <script src="/static/js/services.js"></script>
    
    <!-- 初始化脚本 -->
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Base template loaded');
            
            // 如果存在仪表板初始化函数，则调用
            if (typeof initDashboard === 'function') {
                initDashboard();
            }
        });
    </script>
</body>
</html>
