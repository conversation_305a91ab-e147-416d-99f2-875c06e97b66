#!/bin/sh

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "${DEBUG:-false}" = "true" ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 检查配置文件
check_config() {
    if [ ! -f "./configs/config.yaml" ]; then
        log_warn "配置文件不存在，使用默认配置"
        if [ -f "./configs/config.yaml.example" ]; then
            cp ./configs/config.yaml.example ./configs/config.yaml
            log_info "已复制示例配置文件"
        fi
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    if [ ! -f "./data/monitor.db" ]; then
        ./monitor --once --config ./configs/config.yaml
        log_info "数据库初始化完成"
    else
        log_info "数据库已存在，跳过初始化"
    fi
}

# 等待依赖服务
wait_for_service() {
    local host=$1
    local port=$2
    local timeout=${3:-30}
    
    log_info "等待服务 $host:$port 启动..."
    
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port" 2>/dev/null; then
            log_info "服务 $host:$port 已就绪"
            return 0
        fi
        sleep 1
    done
    
    log_error "等待服务 $host:$port 超时"
    return 1
}

# 健康检查
health_check() {
    local service=$1
    local port=$2
    local path=${3:-"/"}
    
    log_info "执行健康检查: $service"
    
    if curl -f "http://localhost:$port$path" >/dev/null 2>&1; then
        log_info "$service 健康检查通过"
        return 0
    else
        log_error "$service 健康检查失败"
        return 1
    fi
}

# 信号处理
cleanup() {
    log_info "收到停止信号，正在清理..."
    if [ -n "$PID" ]; then
        kill -TERM "$PID" 2>/dev/null || true
        wait "$PID" 2>/dev/null || true
    fi
    log_info "清理完成"
    exit 0
}

# 设置信号处理
trap cleanup TERM INT

# 显示环境信息
show_env_info() {
    log_info "=== 环境信息 ==="
    log_info "服务类型: ${SERVICE_TYPE:-web}"
    log_info "工作目录: $(pwd)"
    log_info "用户: $(whoami)"
    log_info "时区: $(date)"
    
    if [ "${DEBUG:-false}" = "true" ]; then
        log_debug "环境变量:"
        env | grep -E "^(SERVICE_|WEB_|API_|MONITOR_|DB_)" | sort
    fi
    
    log_info "================="
}

# 主函数
main() {
    show_env_info
    check_config
    
    # 根据服务类型执行不同操作
    SERVICE_TYPE=${1:-${SERVICE_TYPE:-web}}
    
    case "$SERVICE_TYPE" in
        "api")
            log_info "启动API服务器..."
            init_database
            
            API_PORT=${API_PORT:-8443}
            API_HOST=${API_HOST:-0.0.0.0}
            
            log_info "API服务器配置: $API_HOST:$API_PORT"
            exec ./api --port "$API_PORT" --host "$API_HOST" --config ./configs/config.yaml
            ;;
            
        "web")
            log_info "启动Web服务器..."
            init_database
            
            WEB_PORT=${WEB_PORT:-8080}
            WEB_HOST=${WEB_HOST:-0.0.0.0}
            
            log_info "Web服务器配置: $WEB_HOST:$WEB_PORT"
            exec ./web --port "$WEB_PORT" --host "$WEB_HOST" --config ./configs/config.yaml
            ;;
            
        "websocket")
            log_info "启动WebSocket服务器..."
            init_database
            
            WS_PORT=${WS_PORT:-8090}
            WS_HOST=${WS_HOST:-0.0.0.0}
            
            log_info "WebSocket服务器配置: $WS_HOST:$WS_PORT"
            exec ./websocket --port "$WS_PORT" --host "$WS_HOST" --config ./configs/config.yaml
            ;;
            
        "monitor")
            log_info "启动监控服务..."
            init_database
            
            MONITOR_INTERVAL=${MONITOR_INTERVAL:-30s}
            
            log_info "监控间隔: $MONITOR_INTERVAL"
            exec ./monitor --interval "$MONITOR_INTERVAL" --config ./configs/config.yaml
            ;;
            
        "iperf")
            log_info "启动iPerf3测试服务..."
            init_database
            
            IPERF_MODE=${IPERF_MODE:-schedule}
            
            log_info "iPerf3模式: $IPERF_MODE"
            exec ./iperf --mode "$IPERF_MODE" --config ./configs/config.yaml
            ;;
            
        "alert")
            log_info "启动告警服务..."
            init_database
            
            ALERT_MODE=${ALERT_MODE:-monitor}
            
            log_info "告警模式: $ALERT_MODE"
            exec ./alert --mode "$ALERT_MODE" --config ./configs/config.yaml
            ;;
            
        "all")
            log_info "启动所有服务..."
            init_database
            
            # 启动API服务器
            ./api --port 8443 --config ./configs/config.yaml &
            API_PID=$!
            
            # 启动Web服务器
            ./web --port 8080 --config ./configs/config.yaml &
            WEB_PID=$!
            
            # 启动WebSocket服务器
            ./websocket --port 8090 --config ./configs/config.yaml &
            WS_PID=$!
            
            # 启动监控服务
            ./monitor --interval 30s --config ./configs/config.yaml &
            MONITOR_PID=$!
            
            log_info "所有服务已启动"
            log_info "API: http://localhost:8443"
            log_info "Web: http://localhost:8080"
            log_info "WebSocket: ws://localhost:8090"
            
            # 等待所有进程
            wait $API_PID $WEB_PID $WS_PID $MONITOR_PID
            ;;
            
        "init")
            log_info "仅初始化数据库..."
            init_database
            log_info "初始化完成"
            ;;
            
        "health")
            log_info "执行健康检查..."
            
            # 检查各个服务
            health_check "API" "8443" "/api/v1/health" || true
            health_check "Web" "8080" "/" || true
            health_check "WebSocket" "8090" "/" || true
            
            log_info "健康检查完成"
            ;;
            
        "version")
            log_info "显示版本信息..."
            echo "Server Monitor v1.0.0"
            echo "Build: $(date)"
            echo "Go version: $(go version 2>/dev/null || echo 'N/A')"
            ;;
            
        *)
            log_error "未知服务类型: $SERVICE_TYPE"
            log_info "支持的服务类型: api, web, websocket, monitor, iperf, alert, all, init, health, version"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
