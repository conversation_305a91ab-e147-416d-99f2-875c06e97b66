package database

import (
	"fmt"
	"server-monitor/internal/config"
	"server-monitor/internal/models"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DB global database instance
var DB *gorm.DB

// Init initializes the database connection
func Init(cfg *config.DatabaseConfig) error {
	var err error
	DB, err = gorm.Open(sqlite.Open(cfg.Path), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info), // Log SQL statements
	})
	if err != nil {
		return fmt.Errorf("failed to connect database: %w", err)
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(cfg.ConnMaxIdleTime)

	// Enable WAL mode for better concurrency and performance
	if cfg.EnableWAL {
		DB.Exec("PRAGMA journal_mode = WAL;")
	}

	// Enable foreign key constraints
	if cfg.EnableForeignKey {
		DB.Exec("PRAGMA foreign_keys = ON;")
	}

	// Set busy timeout for SQLite
	DB.Exec(fmt.Sprintf("PRAGMA busy_timeout = %d;", cfg.BusyTimeout/time.Millisecond))

	// AutoMigrate will create/update tables
	err = DB.AutoMigrate(
		&models.Server{},
		&models.ServerMetric{},
		&models.Service{},
		&models.ServiceStatus{},
		&models.User{},
		&models.Alert{},
	)
	if err != nil {
		return fmt.Errorf("failed to auto migrate database: %w", err)
	}

	return nil
}

// Close closes the database connection
func Close() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return fmt.Errorf("failed to get underlying sql.DB for closing: %w", err)
		}
		return sqlDB.Close()
	}
	return nil
}
