# 多阶段构建 Dockerfile
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache git sqlite-dev gcc musl-dev

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 编译应用程序
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o monitor ./cmd/monitor-test
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o api ./cmd/api-test
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o web ./cmd/web-test
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o iperf ./cmd/iperf-test
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o client ./cmd/client-test
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o websocket ./cmd/websocket-test
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o alert ./cmd/alert-test

# 运行时镜像
FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache \
    ca-certificates \
    sqlite \
    iperf3 \
    curl \
    tzdata

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用用户
RUN addgroup -g 1000 monitor && \
    adduser -D -s /bin/sh -u 1000 -G monitor monitor

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/monitor .
COPY --from=builder /app/api .
COPY --from=builder /app/web .
COPY --from=builder /app/iperf .
COPY --from=builder /app/client .
COPY --from=builder /app/websocket .
COPY --from=builder /app/alert .

# 复制配置文件和模板
COPY configs/ ./configs/
COPY web/templates/ ./web/templates/

# 创建必要的目录
RUN mkdir -p /app/data /app/logs && \
    chown -R monitor:monitor /app

# 复制启动脚本
COPY docker-entrypoint.sh .
RUN chmod +x docker-entrypoint.sh

# 切换到应用用户
USER monitor

# 暴露端口
EXPOSE 8080 8443 8090

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# 设置启动命令
ENTRYPOINT ["./docker-entrypoint.sh"]
CMD ["web"]
