package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"server-monitor/internal/api"
	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/monitor"
)

func main() {
	var (
		configFile = flag.String("config", "./configs/config.yaml", "配置文件路径")
		port       = flag.Int("port", 8443, "API服务端口")
		host       = flag.String("host", "127.0.0.1", "API服务监听地址")
	)
	flag.Parse()

	fmt.Printf("API服务器测试程序\n")
	fmt.Printf("配置文件: %s\n", *configFile)
	fmt.Printf("监听地址: %s:%d\n", *host, *port)
	fmt.Println("---")

	// 加载配置
	configManager := config.NewManager(*configFile)
	if err := configManager.Load(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	cfg := configManager.Get()

	// 覆盖API配置
	cfg.API.Host = *host
	cfg.API.Port = *port
	cfg.API.Enabled = true

	fmt.Printf("配置加载成功\n")

	// 初始化数据库
	repo, err := database.InitDatabase(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.Close()

	fmt.Printf("数据库初始化成功\n")

	// 初始化测试数据
	if err := database.InitTestServers(repo); err != nil {
		log.Printf("Warning: Failed to initialize test data: %v", err)
	} else {
		fmt.Printf("测试数据初始化成功\n")
	}

	// 创建系统监控器
	systemMonitor := monitor.NewSystemMonitor(5 * time.Second)

	// 创建API服务器
	apiServer := api.NewServer(cfg, repo, systemMonitor)

	// 设置信号处理
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// 启动API服务器
	go func() {
		fmt.Printf("启动API服务器...\n")
		if err := apiServer.Start(); err != nil {
			log.Printf("API server error: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(1 * time.Second)

	// 显示API端点信息
	showAPIEndpoints(*host, *port)

	fmt.Println("\nAPI服务器已启动，按 Ctrl+C 停止...")

	// 等待信号
	<-sigCh
	fmt.Println("\n收到停止信号，正在停止API服务器...")

	// 停止API服务器
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := apiServer.Stop(ctx); err != nil {
		log.Printf("Error stopping API server: %v", err)
	}

	fmt.Println("API服务器已停止")
}

func showAPIEndpoints(host string, port int) {
	baseURL := fmt.Sprintf("http://%s:%d/api/v1", host, port)

	fmt.Printf("\n=== 可用的API端点 ===\n")

	fmt.Printf("健康检查:\n")
	fmt.Printf("  GET  %s/health\n", baseURL)

	fmt.Printf("\n系统信息:\n")
	fmt.Printf("  GET  %s/system/info\n", baseURL)
	fmt.Printf("  GET  %s/system/cpu\n", baseURL)
	fmt.Printf("  GET  %s/system/memory\n", baseURL)
	fmt.Printf("  GET  %s/system/disk\n", baseURL)
	fmt.Printf("  GET  %s/system/network\n", baseURL)

	fmt.Printf("\n服务器管理:\n")
	fmt.Printf("  GET    %s/servers\n", baseURL)
	fmt.Printf("  POST   %s/servers\n", baseURL)
	fmt.Printf("  GET    %s/servers/{id}\n", baseURL)
	fmt.Printf("  PUT    %s/servers/{id}\n", baseURL)
	fmt.Printf("  DELETE %s/servers/{id}\n", baseURL)

	fmt.Printf("\n统计信息:\n")
	fmt.Printf("  GET  %s/stats/summary\n", baseURL)
	fmt.Printf("  GET  %s/stats/servers\n", baseURL)

	fmt.Printf("\n示例curl命令:\n")
	fmt.Printf("# 健康检查\n")
	fmt.Printf("curl %s/health\n", baseURL)

	fmt.Printf("\n# 获取系统信息\n")
	fmt.Printf("curl %s/system/info\n", baseURL)

	fmt.Printf("\n# 列出服务器\n")
	fmt.Printf("curl %s/servers\n", baseURL)

	fmt.Printf("\n# 创建服务器\n")
	fmt.Printf("curl -X POST %s/servers \\\n", baseURL)
	fmt.Printf("  -H \"Content-Type: application/json\" \\\n")
	fmt.Printf("  -d '{\"name\":\"test-server\",\"ip\":\"*************\",\"port\":5201,\"location\":\"Test\",\"is_active\":true}'\n")

	fmt.Printf("\n# 获取统计摘要\n")
	fmt.Printf("curl %s/stats/summary\n", baseURL)
}
