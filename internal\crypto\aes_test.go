package crypto

import (
	"bytes"
	"crypto/rand"
	"testing"
)

func TestGenerateSalt(t *testing.T) {
	salt1, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate salt: %v", err)
	}

	if len(salt1) != saltLen {
		t.<PERSON><PERSON>rf("Expected salt length %d, got %d", saltLen, len(salt1))
	}

	// Generate another salt to ensure they're different
	salt2, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate second salt: %v", err)
	}

	if bytes.Equal(salt1, salt2) {
		t.Error("Generated salts should be different")
	}
}

func TestDeriveKey(t *testing.T) {
	password := []byte("test_password_123")
	salt, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate salt: %v", err)
	}

	key1 := DeriveKey(password, salt)
	if len(key1) != keyLen {
		t.<PERSON><PERSON><PERSON>("Expected key length %d, got %d", keyLen, len(key1))
	}

	// Same password and salt should produce same key
	key2 := DeriveKey(password, salt)
	if !bytes.Equal(key1, key2) {
		t.Error("Same password and salt should produce same key")
	}

	// Different salt should produce different key
	salt2, _ := GenerateSalt()
	key3 := DeriveKey(password, salt2)
	if bytes.Equal(key1, key3) {
		t.Error("Different salt should produce different key")
	}

	// Different password should produce different key
	password2 := []byte("different_password")
	key4 := DeriveKey(password2, salt)
	if bytes.Equal(key1, key4) {
		t.Error("Different password should produce different key")
	}
}

func TestEncryptDecrypt(t *testing.T) {
	// Generate a random key
	key := make([]byte, keyLen)
	if _, err := rand.Read(key); err != nil {
		t.Fatalf("Failed to generate key: %v", err)
	}

	testCases := []struct {
		name      string
		plaintext []byte
	}{
		{
			name:      "empty data",
			plaintext: []byte{},
		},
		{
			name:      "short text",
			plaintext: []byte("Hello, World!"),
		},
		{
			name:      "long text",
			plaintext: []byte("This is a longer text that should be encrypted and decrypted successfully. It contains multiple sentences and various characters including numbers 123456789 and symbols !@#$%^&*()"),
		},
		{
			name:      "binary data",
			plaintext: []byte{0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE, 0xFD, 0xFC},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Encrypt
			ciphertext, err := Encrypt(key, tc.plaintext)
			if err != nil {
				t.Fatalf("Failed to encrypt: %v", err)
			}

			// Ciphertext should be longer than plaintext (due to nonce)
			if len(ciphertext) <= len(tc.plaintext) {
				t.Error("Ciphertext should be longer than plaintext")
			}

			// Decrypt
			decrypted, err := Decrypt(key, ciphertext)
			if err != nil {
				t.Fatalf("Failed to decrypt: %v", err)
			}

			// Verify decrypted data matches original
			if !bytes.Equal(tc.plaintext, decrypted) {
				t.Errorf("Decrypted data doesn't match original.\nOriginal: %v\nDecrypted: %v", tc.plaintext, decrypted)
			}
		})
	}
}

func TestEncryptDecryptWithPassword(t *testing.T) {
	password := "test_password_123"
	plaintext := []byte("Secret message that needs to be encrypted")

	// Generate salt
	salt, err := GenerateSalt()
	if err != nil {
		t.Fatalf("Failed to generate salt: %v", err)
	}

	// Derive key from password
	key := DeriveKey([]byte(password), salt)

	// Encrypt
	ciphertext, err := Encrypt(key, plaintext)
	if err != nil {
		t.Fatalf("Failed to encrypt: %v", err)
	}

	// Decrypt
	decrypted, err := Decrypt(key, ciphertext)
	if err != nil {
		t.Fatalf("Failed to decrypt: %v", err)
	}

	// Verify
	if !bytes.Equal(plaintext, decrypted) {
		t.Error("Decrypted data doesn't match original")
	}
}

func TestDecryptWithWrongKey(t *testing.T) {
	// Generate two different keys
	key1 := make([]byte, keyLen)
	key2 := make([]byte, keyLen)
	rand.Read(key1)
	rand.Read(key2)

	plaintext := []byte("Secret message")

	// Encrypt with key1
	ciphertext, err := Encrypt(key1, plaintext)
	if err != nil {
		t.Fatalf("Failed to encrypt: %v", err)
	}

	// Try to decrypt with key2 (should fail)
	_, err = Decrypt(key2, ciphertext)
	if err == nil {
		t.Error("Decryption with wrong key should fail")
	}
}

func TestDecryptInvalidCiphertext(t *testing.T) {
	key := make([]byte, keyLen)
	rand.Read(key)

	testCases := []struct {
		name       string
		ciphertext []byte
	}{
		{
			name:       "too short",
			ciphertext: []byte{0x01, 0x02},
		},
		{
			name:       "empty",
			ciphertext: []byte{},
		},
		{
			name:       "corrupted",
			ciphertext: make([]byte, nonceLen+16), // Valid length but random data
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			_, err := Decrypt(key, tc.ciphertext)
			if err == nil {
				t.Error("Decryption of invalid ciphertext should fail")
			}
		})
	}
}

func TestEncryptionRandomness(t *testing.T) {
	key := make([]byte, keyLen)
	rand.Read(key)

	plaintext := []byte("Same message")

	// Encrypt the same message multiple times
	ciphertext1, err := Encrypt(key, plaintext)
	if err != nil {
		t.Fatalf("Failed to encrypt: %v", err)
	}

	ciphertext2, err := Encrypt(key, plaintext)
	if err != nil {
		t.Fatalf("Failed to encrypt: %v", err)
	}

	// Ciphertexts should be different (due to random nonce)
	if bytes.Equal(ciphertext1, ciphertext2) {
		t.Error("Encrypting same message should produce different ciphertexts")
	}

	// But both should decrypt to the same plaintext
	decrypted1, err := Decrypt(key, ciphertext1)
	if err != nil {
		t.Fatalf("Failed to decrypt first ciphertext: %v", err)
	}

	decrypted2, err := Decrypt(key, ciphertext2)
	if err != nil {
		t.Fatalf("Failed to decrypt second ciphertext: %v", err)
	}

	if !bytes.Equal(decrypted1, plaintext) || !bytes.Equal(decrypted2, plaintext) {
		t.Error("Both decryptions should produce original plaintext")
	}
}

func BenchmarkEncrypt(b *testing.B) {
	key := make([]byte, keyLen)
	rand.Read(key)
	plaintext := make([]byte, 1024) // 1KB of data
	rand.Read(plaintext)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := Encrypt(key, plaintext)
		if err != nil {
			b.Fatalf("Encryption failed: %v", err)
		}
	}
}

func BenchmarkDecrypt(b *testing.B) {
	key := make([]byte, keyLen)
	rand.Read(key)
	plaintext := make([]byte, 1024) // 1KB of data
	rand.Read(plaintext)

	ciphertext, err := Encrypt(key, plaintext)
	if err != nil {
		b.Fatalf("Failed to encrypt test data: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := Decrypt(key, ciphertext)
		if err != nil {
			b.Fatalf("Decryption failed: %v", err)
		}
	}
}

func BenchmarkDeriveKey(b *testing.B) {
	password := []byte("test_password_123")
	salt, _ := GenerateSalt()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		DeriveKey(password, salt)
	}
}
