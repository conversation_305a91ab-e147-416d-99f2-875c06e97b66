{"start_time": "2025-07-31T07:29:34.1568704+08:00", "end_time": "2025-07-31T07:29:47.7145739+08:00", "total_duration": 13551001900, "total_modules": 2, "passed_count": 2, "failed_count": 0, "skipped_count": 0, "success_rate": 100, "results": {"cli-parsing": {"module": "cli-parsing", "status": "passed", "duration": 11425687100, "output": "=== RUN   TestParseBasicArgs\n--- PASS: TestParseBasicArgs (0.00s)\n=== RUN   TestParseServerMode\n--- PASS: TestParseServerMode (0.00s)\n=== RUN   TestParseClientMode\n--- PASS: TestParseClientMode (0.00s)\n=== RUN   TestParseBothMode\n--- PASS: TestParseBothMode (0.00s)\n=== RUN   TestParseShortFlags\n--- PASS: TestParseShortFlags (0.00s)\n=== RUN   TestValidationErrors\n=== RUN   TestValidationErrors/invalid_mode\n=== RUN   TestValidationErrors/invalid_server_port\n=== RUN   TestValidationErrors/invalid_web_port\n=== RUN   TestValidationErrors/TLS_without_cert\n=== RUN   TestValidationErrors/TLS_without_key\n=== RUN   TestValidationErrors/client_mode_without_server_IP\n=== RUN   TestValidationErrors/invalid_test_parallel\n=== RUN   TestValidationErrors/invalid_test_protocol\n=== RUN   TestValidationErrors/invalid_log_level\n--- PASS: TestValidationErrors (0.00s)\n    --- PASS: TestValidationErrors/invalid_mode (0.00s)\n    --- PASS: TestValidationErrors/invalid_server_port (0.00s)\n    --- PASS: TestValidationErrors/invalid_web_port (0.00s)\n    --- PASS: TestValidationErrors/TLS_without_cert (0.00s)\n    --- PASS: TestValidationErrors/TLS_without_key (0.00s)\n    --- PASS: TestValidationErrors/client_mode_without_server_IP (0.00s)\n    --- PASS: TestValidationErrors/invalid_test_parallel (0.00s)\n    --- PASS: TestValidationErrors/invalid_test_protocol (0.00s)\n    --- PASS: TestValidationErrors/invalid_log_level (0.00s)\n=== RUN   TestDefaultValues\n--- PASS: TestDefaultValues (0.00s)\n=== RUN   TestClientNameDefault\n--- PASS: TestClientNameDefault (0.00s)\nPASS\nok  \tserver-monitor/internal/cli\t0.770s\n", "error": "", "start_time": "2025-07-31T07:29:36.2888868+08:00", "end_time": "2025-07-31T07:29:47.7145739+08:00"}, "project-init": {"module": "project-init", "status": "passed", "duration": 2125314800, "output": "all modules verified\n", "error": "", "start_time": "2025-07-31T07:29:34.163572+08:00", "end_time": "2025-07-31T07:29:36.2888868+08:00"}}}