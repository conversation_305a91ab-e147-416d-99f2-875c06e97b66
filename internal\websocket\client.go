package websocket

import (
	"bytes"
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

const (
	// 写入等待时间
	writeWait = 10 * time.Second

	// Pong等待时间
	pongWait = 60 * time.Second

	// Ping发送周期，必须小于pongWait
	pingPeriod = (pongWait * 9) / 10

	// 最大消息大小
	maxMessageSize = 512
)

var (
	newline = []byte{'\n'}
	space   = []byte{' '}
)

// Client WebSocket客户端连接
type Client struct {
	hub *Hub

	// WebSocket连接
	conn *websocket.Conn

	// 发送消息的缓冲通道
	send chan []byte
}

// readPump 从WebSocket连接读取消息并转发到hub
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket错误: %v", err)
			}
			break
		}

		message = bytes.TrimSpace(bytes.Replace(message, newline, space, -1))

		// 处理客户端发送的消息
		c.handleMessage(message)
	}
}

// writePump 从hub接收消息并写入WebSocket连接
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// Hub关闭了通道
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 将队列中的其他消息一起发送
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write(newline)
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理客户端发送的消息
func (c *Client) handleMessage(message []byte) {
	// 这里可以处理客户端发送的命令
	// 例如：请求特定数据、订阅特定事件等

	log.Printf("收到客户端消息: %s", string(message))

	// 简单的命令处理示例
	switch string(message) {
	case "ping":
		c.sendMessage("pong")
	case "get_system_info":
		// 立即发送系统信息
		if c.hub.systemMonitor != nil {
			systemInfo, err := c.hub.systemMonitor.GetSystemInfo()
			if err == nil {
				c.hub.BroadcastMessage("system_info", systemInfo)
			}
		}
	case "get_client_count":
		count := c.hub.GetClientCount()
		c.sendMessage(map[string]interface{}{
			"type": "client_count",
			"data": count,
		})
	default:
		// 未知命令
		c.sendMessage(map[string]interface{}{
			"type":      "error",
			"data":      "未知命令: " + string(message),
			"timestamp": time.Now().Unix(),
		})
	}
}

// sendMessage 向客户端发送消息
func (c *Client) sendMessage(data interface{}) {
	message := Message{
		Type:      "response",
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	if jsonData, err := json.Marshal(message); err == nil {
		select {
		case c.send <- jsonData:
		default:
			close(c.send)
		}
	}
}

// serveWS 处理WebSocket请求的HTTP处理函数
func ServeWS(hub *Hub, w http.ResponseWriter, r *http.Request) {
	hub.ServeWS(w, r)
}
