package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// DockerManager Docker容器管理器
type DockerManager struct{}

// NewDockerManager 创建Docker管理器
func NewDockerManager() *DockerManager {
	return &DockerManager{}
}

// DockerContainer Docker容器信息
type DockerContainer struct {
	ID      string            `json:"Id"`
	Names   []string          `json:"Names"`
	Image   string            `json:"Image"`
	Command string            `json:"Command"`
	Created int64             `json:"Created"`
	State   string            `json:"State"`
	Status  string            `json:"Status"`
	Ports   []DockerPort      `json:"Ports"`
	Labels  map[string]string `json:"Labels"`
}

// DockerPort Docker端口信息
type DockerPort struct {
	IP          string `json:"IP"`
	PrivatePort int    `json:"PrivatePort"`
	PublicPort  int    `json:"PublicPort"`
	Type        string `json:"Type"`
}

// ListServices 列出所有Docker容器
func (d *DockerManager) ListServices(ctx context.Context) ([]*Service, error) {
	// 获取所有容器（包括停止的）
	output, err := executeCommand(ctx, "docker", "ps", "-a", "--format", "json")
	if err != nil {
		return nil, fmt.Errorf("获取Docker容器列表失败: %w", err)
	}

	var services []*Service
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		
		var container DockerContainer
		if err := json.Unmarshal([]byte(line), &container); err != nil {
			continue
		}
		
		// 获取容器名称（去掉前缀斜杠）
		name := container.ID[:12] // 使用短ID作为默认名称
		if len(container.Names) > 0 {
			name = strings.TrimPrefix(container.Names[0], "/")
		}
		
		service := &Service{
			Name:        name,
			DisplayName: name,
			Status:      parseDockerStatus(container.State),
			Type:        "docker",
			Description: fmt.Sprintf("Docker容器: %s", container.Image),
		}
		
		// 设置创建时间
		if container.Created > 0 {
			created := time.Unix(container.Created, 0)
			service.StartTime = &created
		}
		
		services = append(services, service)
	}

	return services, nil
}

// GetService 获取指定容器的详细信息
func (d *DockerManager) GetService(ctx context.Context, name string) (*Service, error) {
	if !d.ServiceExists(ctx, name) {
		return nil, fmt.Errorf("容器不存在: %s", name)
	}

	// 获取容器详细信息
	output, err := executeCommand(ctx, "docker", "inspect", name)
	if err != nil {
		return nil, fmt.Errorf("获取容器信息失败: %w", err)
	}

	var containers []map[string]interface{}
	if err := json.Unmarshal([]byte(output), &containers); err != nil {
		return nil, fmt.Errorf("解析容器信息失败: %w", err)
	}

	if len(containers) == 0 {
		return nil, fmt.Errorf("容器不存在: %s", name)
	}

	container := containers[0]
	
	service := &Service{
		Name:        name,
		DisplayName: name,
		Type:        "docker",
	}

	// 解析状态
	if state, ok := container["State"].(map[string]interface{}); ok {
		if running, ok := state["Running"].(bool); ok {
			if running {
				service.Status = StatusRunning
			} else {
				service.Status = StatusStopped
			}
		}
		
		// 获取PID
		if pid, ok := state["Pid"].(float64); ok {
			service.PID = int(pid)
		}
		
		// 获取启动时间
		if startedAt, ok := state["StartedAt"].(string); ok && startedAt != "" {
			if startTime, err := time.Parse(time.RFC3339Nano, startedAt); err == nil {
				service.StartTime = &startTime
			}
		}
	}

	// 获取镜像信息
	if config, ok := container["Config"].(map[string]interface{}); ok {
		if image, ok := config["Image"].(string); ok {
			service.Description = fmt.Sprintf("Docker容器: %s", image)
		}
	}

	return service, nil
}

// StartService 启动容器
func (d *DockerManager) StartService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "docker", "start", name)
	if err != nil {
		return fmt.Errorf("启动容器失败: %w", err)
	}
	return nil
}

// StopService 停止容器
func (d *DockerManager) StopService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "docker", "stop", name)
	if err != nil {
		return fmt.Errorf("停止容器失败: %w", err)
	}
	return nil
}

// RestartService 重启容器
func (d *DockerManager) RestartService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "docker", "restart", name)
	if err != nil {
		return fmt.Errorf("重启容器失败: %w", err)
	}
	return nil
}

// EnableService Docker容器没有启用概念，返回不支持错误
func (d *DockerManager) EnableService(ctx context.Context, name string) error {
	return fmt.Errorf("Docker容器不支持启用操作")
}

// DisableService Docker容器没有禁用概念，返回不支持错误
func (d *DockerManager) DisableService(ctx context.Context, name string) error {
	return fmt.Errorf("Docker容器不支持禁用操作")
}

// ServiceExists 检查容器是否存在
func (d *DockerManager) ServiceExists(ctx context.Context, name string) bool {
	_, err := executeCommand(ctx, "docker", "inspect", name)
	return err == nil
}

// GetServiceLogs 获取容器日志
func (d *DockerManager) GetServiceLogs(ctx context.Context, name string, lines int) ([]string, error) {
	args := []string{"docker", "logs", "--tail", strconv.Itoa(lines), name}
	output, err := executeCommand(ctx, args[0], args[1:]...)
	if err != nil {
		return nil, fmt.Errorf("获取容器日志失败: %w", err)
	}

	logLines := strings.Split(output, "\n")
	
	// 过滤空行
	var result []string
	for _, line := range logLines {
		if strings.TrimSpace(line) != "" {
			result = append(result, line)
		}
	}

	return result, nil
}

// parseDockerStatus 解析Docker容器状态
func parseDockerStatus(state string) ServiceStatus {
	state = strings.ToLower(strings.TrimSpace(state))
	
	switch state {
	case "running":
		return StatusRunning
	case "exited", "stopped":
		return StatusStopped
	case "paused":
		return StatusStopped
	case "restarting":
		return StatusRunning
	case "dead":
		return StatusFailed
	default:
		return StatusUnknown
	}
}

// GetContainerStats 获取容器统计信息
func (d *DockerManager) GetContainerStats(ctx context.Context, name string) (map[string]interface{}, error) {
	output, err := executeCommand(ctx, "docker", "stats", name, "--no-stream", "--format", "json")
	if err != nil {
		return nil, fmt.Errorf("获取容器统计信息失败: %w", err)
	}

	var stats map[string]interface{}
	if err := json.Unmarshal([]byte(output), &stats); err != nil {
		return nil, fmt.Errorf("解析容器统计信息失败: %w", err)
	}

	return stats, nil
}

// PauseService 暂停容器
func (d *DockerManager) PauseService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "docker", "pause", name)
	if err != nil {
		return fmt.Errorf("暂停容器失败: %w", err)
	}
	return nil
}

// UnpauseService 恢复容器
func (d *DockerManager) UnpauseService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "docker", "unpause", name)
	if err != nil {
		return fmt.Errorf("恢复容器失败: %w", err)
	}
	return nil
}

// RemoveService 删除容器
func (d *DockerManager) RemoveService(ctx context.Context, name string, force bool) error {
	args := []string{"docker", "rm"}
	if force {
		args = append(args, "-f")
	}
	args = append(args, name)
	
	_, err := executeCommand(ctx, args[0], args[1:]...)
	if err != nil {
		return fmt.Errorf("删除容器失败: %w", err)
	}
	return nil
}

// ExecCommand 在容器中执行命令
func (d *DockerManager) ExecCommand(ctx context.Context, name string, command []string) (string, error) {
	args := append([]string{"docker", "exec", name}, command...)
	output, err := executeCommand(ctx, args[0], args[1:]...)
	if err != nil {
		return "", fmt.Errorf("在容器中执行命令失败: %w", err)
	}
	return output, nil
}
