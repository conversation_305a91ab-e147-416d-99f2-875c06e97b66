# Server Monitor Production Configuration Template
# 服务器监控系统生产环境配置模板

# 系统配置
system:
  name: "Server Monitor"
  version: "1.0.0"
  environment: "production"
  data_dir: "/opt/monitor/data"
  pid_file: "/var/run/monitor.pid"
  mode: "both"

# 数据库配置
database:
  path: "/opt/monitor/data/monitor.db"
  max_open_conns: 50
  max_idle_conns: 10
  conn_max_lifetime: "2h"
  conn_max_idle_time: "30m"
  enable_wal: true
  enable_foreign_key: true
  busy_timeout: "60s"

# 调度器配置
scheduler:
  enabled: true
  mode: "odd"
  timezone: "Asia/Shanghai"
  start_hour: 0
  end_hour: 23
  max_concurrent: 8
  retry_attempts: 3
  retry_interval: "10m"

# 对端配置（生产环境双机同步）
peer:
  ip: "*************"
  port: 8443
  username: "monitor"
  password: "CHANGE_THIS_PASSWORD"
  timeout: "60s"
  enabled: true

# 测试参数配置
test:
  duration: "60s"
  parallel: 8
  window_size: "128K"
  buffer_length: "256K"
  protocol: "tcp"
  timeout: "120s"
  connect_timeout: "30s"
  max_retries: 5
  retry_delay: "10s"

# Web界面配置
web:
  enabled: true
  port: 80
  host: "0.0.0.0"
  static_dir: "/opt/monitor/web/static"
  template_dir: "/opt/monitor/web/templates"
  tls:
    enabled: true
    cert_file: "/etc/ssl/certs/monitor.crt"
    key_file: "/etc/ssl/private/monitor.key"

# API配置
api:
  enabled: true
  port: 443
  host: "0.0.0.0"
  prefix: "/api/v1"
  timeout: "60s"
  rate_limit: 1000
  tls:
    enabled: true
    cert_file: "/etc/ssl/certs/monitor.crt"
    key_file: "/etc/ssl/private/monitor.key"

# 数据同步配置
sync:
  enabled: true
  interval: "5m"
  batch_size: 500
  max_retries: 5
  retry_interval: "60s"
  timeout: "120s"

# 日志配置
log:
  level: "info"
  format: "json"
  output: "file"
  file: "/var/log/monitor/monitor.log"
  max_size: 500
  max_backups: 10
  max_age: 90
  compress: true

# 生产环境服务器列表示例
servers:
  - name: "prod-server-01"
    ip: "**********"
    port: 5201
    location: "Beijing-DC1"
    provider: "Alibaba Cloud"
    enabled: true
    priority: 1
    tags: ["primary", "beijing", "tier1"]
    
  - name: "prod-server-02"
    ip: "**********"
    port: 5201
    location: "Shanghai-DC1"
    provider: "Tencent Cloud"
    enabled: true
    priority: 2
    tags: ["primary", "shanghai", "tier1"]
