package database

import (
	"time"

	"server-monitor/internal/models" // Import the models package

	"gorm.io/gorm" // Import gorm
)

// Repository 数据访问接口
type Repository interface {
	// Server operations
	CreateServer(server *models.Server) error
	GetServer(id int) (*models.Server, error)
	GetServerByName(name string) (*models.Server, error)
	UpdateServer(server *models.Server) error
	DeleteServer(id int) error
	ListServers(active bool) ([]*models.Server, error)

	// HourlyResult operations
	CreateHourlyResult(result *HourlyResult) error
	GetHourlyResult(serverID uint, testHour time.Time) (*HourlyResult, error)
	UpdateHourlyResult(result *HourlyResult) error
	DeleteHourlyResult(id int) error
	ListHourlyResults(serverID uint, startTime, endTime time.Time) ([]*HourlyResult, error)

	// SyncStatus operations
	CreateSyncStatus(status *SyncStatus) error
	GetSyncStatus(peerIP string) (*SyncStatus, error)
	UpdateSyncStatus(status *SyncStatus) error
	DeleteSyncStatus(peerIP string) error
	ListSyncStatus() ([]*SyncStatus, error)

	// SystemConfig operations
	SetConfig(key, value, description, configType string, isSystem bool) error
	GetConfig(key string) (*SystemConfig, error)
	DeleteConfig(key string) error
	ListConfigs(isSystem bool) ([]*SystemConfig, error)

	// SystemInfo operations
	CreateSystemInfo(info *models.SystemInfo) error
	GetSystemInfo(serverID uint, startTime, endTime time.Time) ([]*models.SystemInfo, error)

	// Alert operations
	CreateAlert(alert *models.Alert) error
	ListAlerts(serverID uint, level string, status string, startTime, endTime time.Time) ([]*models.Alert, error)
	UpdateAlertStatus(alertID uint, status string) error
}

// SQLiteRepository SQLite数据访问实现
type SQLiteRepository struct {
	db *gorm.DB // Change to *gorm.DB
}

// NewRepository 创建新的数据访问实例
func NewRepository(db *gorm.DB) Repository { // Change to *gorm.DB
	return &SQLiteRepository{db: db}
}

// Server operations

func (r *SQLiteRepository) CreateSystemInfo(info *models.SystemInfo) error {
	return r.db.Create(info).Error
}

func (r *SQLiteRepository) GetSystemInfo(serverID uint, startTime, endTime time.Time) ([]*models.SystemInfo, error) {
	var systemInfos []*models.SystemInfo
	query := r.db.Model(&models.SystemInfo{})

	if serverID != 0 {
		query = query.Where("server_id = ?", serverID)
	}
	if !startTime.IsZero() {
		query = query.Where("timestamp >= ?", startTime)
	}
	if !endTime.IsZero() {
		query = query.Where("timestamp <= ?", endTime)
	}

	err := query.Order("timestamp DESC").Find(&systemInfos).Error
	if err != nil {
		return nil, err
	}
	return systemInfos, nil
}

// Alert operations

func (r *SQLiteRepository) CreateAlert(alert *models.Alert) error {
	alert.CreatedAt = time.Now()
	alert.UpdatedAt = time.Now()
	return r.db.Create(alert).Error
}

func (r *SQLiteRepository) ListAlerts(serverID uint, level string, status string, startTime, endTime time.Time) ([]*models.Alert, error) {
	var alerts []*models.Alert
	query := r.db.Model(&models.Alert{})

	if serverID != 0 {
		query = query.Where("server_id = ?", serverID)
	}
	if level != "" {
		query = query.Where("level = ?", level)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if !startTime.IsZero() {
		query = query.Where("timestamp >= ?", startTime)
	}
	if !endTime.IsZero() {
		query = query.Where("timestamp <= ?", endTime)
	}

	err := query.Order("timestamp DESC").Find(&alerts).Error
	if err != nil {
		return nil, err
	}
	return alerts, nil
}

func (r *SQLiteRepository) UpdateAlertStatus(alertID uint, status string) error {
	result := r.db.Model(&models.Alert{}).Where("id = ?", alertID).Updates(map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return ErrRecordNotFound
	}
	return nil
}

func (r *SQLiteRepository) CreateServer(server *models.Server) error {
	// Removed server.Validate() as models.Server does not have it
	return r.db.Create(server).Error
}

func (r *SQLiteRepository) GetServer(id int) (*models.Server, error) {
	var server models.Server
	err := r.db.First(&server, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &server, nil
}

func (r *SQLiteRepository) GetServerByName(name string) (*models.Server, error) {
	var server models.Server
	err := r.db.Where("name = ?", name).First(&server).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &server, nil
}

func (r *SQLiteRepository) UpdateServer(server *models.Server) error {
	// No server.Validate() for models.Server
	server.UpdatedAt = time.Now()
	result := r.db.Save(server)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return ErrRecordNotFound
	}
	return nil
}

func (r *SQLiteRepository) DeleteServer(id int) error {
	result := r.db.Delete(&models.Server{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return ErrRecordNotFound
	}
	return nil
}

func (r *SQLiteRepository) ListServers(active bool) ([]*models.Server, error) {
	var servers []*models.Server
	query := r.db.Order("name")
	if active {
		query = query.Where("is_active = ?", true)
	}
	err := query.Find(&servers).Error
	if err != nil {
		return nil, err
	}
	return servers, nil
}

// HourlyResult operations

func (r *SQLiteRepository) CreateHourlyResult(result *HourlyResult) error {
	if err := result.Validate(); err != nil {
		return err
	}
	result.CreatedAt = time.Now()
	return r.db.Create(result).Error
}

func (r *SQLiteRepository) GetHourlyResult(serverID uint, testHour time.Time) (*HourlyResult, error) {
	var result HourlyResult
	err := r.db.Where("server_id = ? AND test_hour = ?", serverID, testHour).First(&result).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &result, nil
}

func (r *SQLiteRepository) UpdateHourlyResult(result *HourlyResult) error {
	if err := result.Validate(); err != nil {
		return err
	}
	res := r.db.Save(result)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return ErrRecordNotFound
	}
	return nil
}

func (r *SQLiteRepository) DeleteHourlyResult(id int) error {
	result := r.db.Delete(&HourlyResult{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return ErrRecordNotFound
	}
	return nil
}

func (r *SQLiteRepository) ListHourlyResults(serverID uint, startTime, endTime time.Time) ([]*HourlyResult, error) {
	var results []*HourlyResult
	err := r.db.Where("server_id = ? AND test_hour BETWEEN ? AND ?", serverID, startTime, endTime).
		Order("test_hour DESC").
		Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// SyncStatus operations

func (r *SQLiteRepository) CreateSyncStatus(status *SyncStatus) error {
	if err := status.Validate(); err != nil {
		return err
	}
	status.CreatedAt = time.Now()
	status.UpdatedAt = time.Now()
	return r.db.Create(status).Error
}

func (r *SQLiteRepository) GetSyncStatus(peerIP string) (*SyncStatus, error) {
	var status SyncStatus
	err := r.db.Where("peer_ip = ?", peerIP).First(&status).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &status, nil
}

func (r *SQLiteRepository) UpdateSyncStatus(status *SyncStatus) error {
	if err := status.Validate(); err != nil {
		return err
	}
	status.UpdatedAt = time.Now()
	result := r.db.Save(status)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return ErrRecordNotFound
	}
	return nil
}

func (r *SQLiteRepository) DeleteSyncStatus(peerIP string) error {
	result := r.db.Where("peer_ip = ?", peerIP).Delete(&SyncStatus{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return ErrRecordNotFound
	}
	return nil
}

func (r *SQLiteRepository) ListSyncStatus() ([]*SyncStatus, error) {
	var statuses []*SyncStatus
	err := r.db.Order("peer_ip").Find(&statuses).Error
	if err != nil {
		return nil, err
	}
	return statuses, nil
}

// SystemConfig operations

func (r *SQLiteRepository) SetConfig(key, value, description, configType string, isSystem bool) error {
	if key == "" {
		return ErrInvalidConfigKey
	}
	if configType == "" {
		configType = ConfigTypeString
	}

	now := time.Now()
	config := SystemConfig{
		ConfigKey:   key,
		ConfigValue: value,
		Description: description,
		ConfigType:  configType,
		IsSystem:    isSystem,
		UpdatedAt:   now,
	}

	// 尝试更新现有配置
	result := r.db.Where("config_key = ?", key).Assign(config).FirstOrCreate(&config)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

func (r *SQLiteRepository) GetConfig(key string) (*SystemConfig, error) {
	var config SystemConfig
	err := r.db.Where("config_key = ?", key).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &config, nil
}

func (r *SQLiteRepository) DeleteConfig(key string) error {
	result := r.db.Where("config_key = ?", key).Delete(&SystemConfig{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return ErrRecordNotFound
	}
	return nil
}

func (r *SQLiteRepository) ListConfigs(isSystem bool) ([]*SystemConfig, error) {
	var configs []*SystemConfig
	query := r.db.Order("config_key")
	if isSystem {
		query = query.Where("is_system = ?", true)
	}
	err := query.Find(&configs).Error
	if err != nil {
		return nil, err
	}
	return configs, nil
}
