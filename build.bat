@echo off
REM Server Monitor Build Script for Windows
REM Windows批处理构建脚本

setlocal enabledelayedexpansion

REM 项目配置
set PROJECT_NAME=server-monitor
set MAIN_PATH=./cmd/monitor
set BUILD_DIR=build
set DIST_DIR=dist
set BINARY_NAME=monitor

REM 获取版本信息
for /f "tokens=*" %%i in ('git describe --tags --always --dirty 2^>nul') do set VERSION=%%i
if "%VERSION%"=="" set VERSION=v0.1.0-dev

for /f "tokens=*" %%i in ('git rev-parse --short HEAD 2^>nul') do set GIT_COMMIT=%%i
if "%GIT_COMMIT%"=="" set GIT_COMMIT=unknown

for /f "tokens=2 delims==" %%i in ('wmic OS Get localdatetime /value') do set datetime=%%i
set BUILD_TIME=%datetime:~0,4%-%datetime:~4,2%-%datetime:~6,2%_%datetime:~8,2%:%datetime:~10,2%:%datetime:~12,2%

REM 构建标志
set LDFLAGS=-ldflags "-X main.Version=%VERSION% -X main.BuildTime=%BUILD_TIME% -X main.GitCommit=%GIT_COMMIT% -w -s"

REM 显示帮助信息
if "%1"=="help" goto :help
if "%1"=="/?" goto :help
if "%1"=="-h" goto :help
if "%1"=="--help" goto :help

REM 处理命令
if "%1"=="" goto :build
if "%1"=="build" goto :build
if "%1"=="build-all" goto :build_all
if "%1"=="test" goto :test
if "%1"=="clean" goto :clean
if "%1"=="fmt" goto :fmt
if "%1"=="vet" goto :vet
if "%1"=="deps" goto :deps
if "%1"=="run" goto :run
if "%1"=="version" goto :version

echo Unknown command: %1
goto :help

:help
echo Server Monitor Build System for Windows
echo Usage: build.bat [command]
echo.
echo Available commands:
echo   build      - Build application for current platform
echo   build-all  - Build for all platforms
echo   test       - Run tests
echo   clean      - Clean build files
echo   fmt        - Format code
echo   vet        - Run go vet
echo   deps       - Download dependencies
echo   run        - Run application
echo   version    - Show version information
echo   help       - Show this help
goto :end

:build
echo Building %PROJECT_NAME% %VERSION%...
if not exist %BUILD_DIR% mkdir %BUILD_DIR%
go build %LDFLAGS% -o %BUILD_DIR%/%BINARY_NAME%.exe %MAIN_PATH%
if %errorlevel% equ 0 (
    echo Build completed: %BUILD_DIR%/%BINARY_NAME%.exe
) else (
    echo Build failed!
    exit /b 1
)
goto :end

:build_all
echo Building for all platforms...
call :build_platform windows amd64
call :build_platform linux amd64
call :build_platform darwin amd64
echo All builds completed
goto :end

:build_platform
set GOOS=%1
set GOARCH=%2
set OUTPUT_DIR=%DIST_DIR%\%GOOS%-%GOARCH%
if not exist %OUTPUT_DIR% mkdir %OUTPUT_DIR%

if "%GOOS%"=="windows" (
    set BINARY_EXT=.exe
) else (
    set BINARY_EXT=
)

echo Building for %GOOS%/%GOARCH%...
set GOOS=%GOOS%
set GOARCH=%GOARCH%
go build %LDFLAGS% -o %OUTPUT_DIR%/%BINARY_NAME%%BINARY_EXT% %MAIN_PATH%
if %errorlevel% equ 0 (
    echo Built %GOOS%/%GOARCH%: %OUTPUT_DIR%/%BINARY_NAME%%BINARY_EXT%
) else (
    echo Failed to build %GOOS%/%GOARCH%
)
goto :eof

:test
echo Running tests...
go test -v ./...
goto :end

:clean
echo Cleaning build files...
if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
if exist %DIST_DIR% rmdir /s /q %DIST_DIR%
if exist coverage.out del coverage.out
if exist coverage.html del coverage.html
if exist %BINARY_NAME%.exe del %BINARY_NAME%.exe
echo Clean completed
goto :end

:fmt
echo Formatting code...
go fmt ./...
goto :end

:vet
echo Running go vet...
go vet ./...
goto :end

:deps
echo Downloading dependencies...
go mod download
goto :end

:run
echo Running %PROJECT_NAME%...
go run %MAIN_PATH%
goto :end

:version
echo Project: %PROJECT_NAME%
echo Version: %VERSION%
echo Build Time: %BUILD_TIME%
echo Git Commit: %GIT_COMMIT%
for /f "tokens=*" %%i in ('go version') do echo Go Version: %%i
goto :end

:end
endlocal
