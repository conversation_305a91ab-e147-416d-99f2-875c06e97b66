package server

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/logger"
	"server-monitor/internal/processor"
	"server-monitor/internal/server/api"
	"server-monitor/websocket"
)

// Server represents the main server application.
type Server struct {
	Config    *config.Manager
	DB        *gorm.DB
	Repo      database.Repository
	Router    *gin.Engine
	Hub       *websocket.Hub
	SysProcessor *processor.Processor
	HTTPServer *http.Server
}

// NewServer creates a new Server instance.
func NewServer(cfg *config.Manager, db *gorm.DB) (*Server, error) {
	repo := database.NewRepository(db)
	hub := websocket.NewHub()
	sysProcessor := processor.NewProcessor(repo, hub)

	// Run the WebSocket hub in a goroutine
	go hub.Run()

	return &Server{
		Config:    cfg,
		DB:        db,
		Repo:      repo,
		Hub:       hub,
		SysProcessor: sysProcessor,
	}, nil
}

// Run starts the HTTP server and handles graceful shutdown.
func (s *Server) Run() error {
	appConfig := s.Config.Get()

	// Setup Gin router
	s.Router = api.SetupRouter(s.Config, s.Repo)

	// Create HTTP server
	s.HTTPServer = &http.Server{
		Addr:    fmt.Sprintf(":%d", appConfig.API.Port),
		Handler: s.Router,
	}

	// Start HTTP server in a goroutine
	go func() {
		logger.Info("Starting HTTP server on port %d", appConfig.API.Port)
		if err := s.HTTPServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("HTTP server failed to start: %v", err)
			os.Exit(1)
		}
	}()

	// Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := s.HTTPServer.Shutdown(ctx); err != nil {
		logger.Error("HTTP server forced to shutdown: %v", err)
		return err
	}

	// Close WebSocket hub
	s.Hub.Close()

	logger.Info("Server gracefully stopped.")
	return nil
}