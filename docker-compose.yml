version: '3.8'

services:
  # Web界面服务
  web:
    build: .
    container_name: server-monitor-web
    environment:
      - SERVICE_TYPE=web
      - WEB_PORT=8080
      - WEB_HOST=0.0.0.0
      - TZ=Asia/Shanghai
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - monitor-network
    depends_on:
      - api

  # API服务
  api:
    build: .
    container_name: server-monitor-api
    environment:
      - SERVICE_TYPE=api
      - API_PORT=8443
      - API_HOST=0.0.0.0
      - TZ=Asia/Shanghai
    ports:
      - "8443:8443"
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8443/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - monitor-network

  # WebSocket服务
  websocket:
    build: .
    container_name: server-monitor-websocket
    environment:
      - SERVICE_TYPE=websocket
      - WS_PORT=8090
      - WS_HOST=0.0.0.0
      - TZ=Asia/Shanghai
    ports:
      - "8090:8090"
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - monitor-network
    depends_on:
      - api

  # 监控服务
  monitor:
    build: .
    container_name: server-monitor-service
    environment:
      - SERVICE_TYPE=monitor
      - MONITOR_INTERVAL=60s
      - TZ=Asia/Shanghai
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - monitor-network
    depends_on:
      - api

  # 告警服务
  alert:
    build: .
    container_name: server-monitor-alert
    environment:
      - SERVICE_TYPE=alert
      - ALERT_MODE=monitor
      - TZ=Asia/Shanghai
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - monitor-network
    depends_on:
      - api

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: server-monitor-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    restart: unless-stopped
    networks:
      - monitor-network
    depends_on:
      - web
      - api
      - websocket
    profiles:
      - nginx

networks:
  monitor-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  monitor-data:
    driver: local
  monitor-logs:
    driver: local
