package processor

import (
	"fmt"
	"log"
	"time"

	"server-monitor/internal/database"
	"server-monitor/internal/models"
	"server-monitor/websocket" // Import the websocket package
)

// Processor handles the processing of system information data.
type Processor struct {
	repo database.Repository // Use interface type
	hub  *websocket.Hub
}

// NewProcessor creates a new Processor instance.
func NewProcessor(repo database.Repository, hub *websocket.Hub) *Processor {
	return &Processor{
		repo: repo,
		hub:  hub,
	}
}

// ProcessSystemInfo processes incoming system information.
func (p *Processor) ProcessSystemInfo(info *models.SystemInfo) error {
	// 1. Store data in the database
	if err := p.repo.CreateSystemInfo(info); err != nil {
		log.Printf("Error storing system info: %v", err)
		return err
	}
	log.Printf("Stored system info for server %d: CPU %.2f%%, Mem %.2f%%",
		info.ServerID, info.CPUUsage, info.MemoryUsage)

	// 2. Perform alert detection (placeholder)
	p.checkAlerts(info)

	// 3. Push data to WebSocket clients
	if p.hub != nil {
		// Send a simplified version of the system info to avoid sending too much data
		// or sensitive information over WebSocket.
		wsData := map[string]interface{}{
			"server_id":    info.ServerID,
			"cpu_usage":    info.CPUUsage,
			"memory_usage": info.MemoryUsage,
			"timestamp":    info.Timestamp.Format(time.RFC3339),
		}
		if err := p.hub.SendJSONMessage(wsData); err != nil {
			log.Printf("Error sending system info to WebSocket: %v", err)
		}
	}

	return nil
}

// checkAlerts performs basic alert detection (placeholder).
func (p *Processor) checkAlerts(info *models.SystemInfo) {
	// Example: If CPU usage is above 80%, create a warning alert.
	if info.CPUUsage > 80.0 {
		alert := &models.Alert{
			ServerID:  info.ServerID,
			Metric:    "cpu_usage",
			Threshold: 80.0,
			Operator:  ">",
			Value:     info.CPUUsage,
			Message:   fmt.Sprintf("CPU usage %.2f%% is above threshold 80%%", info.CPUUsage),
			Level:     "warning",
			Status:    "active",
			Timestamp: time.Now(),
		}
		if err := p.repo.CreateAlert(alert); err != nil {
			log.Printf("Error creating alert: %v", err)
		} else {
			log.Printf("Alert created for server %d: %s", info.ServerID, alert.Message)
		}
	}
	// Add more alert rules here (e.g., memory, disk, network)
}