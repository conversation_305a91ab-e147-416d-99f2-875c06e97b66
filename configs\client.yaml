name: server-monitor-client
description: 服务器监控客户端
version: 1.0.0
server:
    host: 127.0.0.1
    port: 8443
    protocol: http
    api_key: ""
    timeout: 30s
monitor:
    enabled: true
    interval: 30s
    report_interval: 60s
    metrics_enabled: true
    services_enabled: true
log:
    level: info
    format: text
    output: both
    file: ./logs/client.log
    max_size: 50
    max_backups: 5
    max_age: 30
    compress: true
network:
    connect_timeout: 10s
    read_timeout: 30s
    write_timeout: 30s
    max_retries: 3
    retry_interval: 5s
    keep_alive: true
security:
    tls_enabled: false
    tls_skip_verify: false
    cert_file: ""
    key_file: ""
    ca_file: ""
    encryption_key: ""
advanced:
    worker_count: 4
    buffer_size: 1000
    cache_enabled: true
    cache_ttl: 5m
    debug_mode: false
    profile_enabled: false
