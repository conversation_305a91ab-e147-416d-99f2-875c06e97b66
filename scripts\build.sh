#!/bin/bash

# Server Monitor Build Script
# 跨平台构建脚本

set -e

# 项目配置
PROJECT_NAME="server-monitor"
MAIN_PATH="./cmd/monitor"
BUILD_DIR="build"
DIST_DIR="dist"

# 版本信息
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "v0.1.0-dev")
BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建标志
LDFLAGS="-ldflags -X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GitCommit=${GIT_COMMIT} -w -s"

# 支持的平台
PLATFORMS=(
    "linux/amd64"
    "linux/arm64"
    "windows/amd64"
    "darwin/amd64"
    "darwin/arm64"
)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Server Monitor Build Script

Usage: $0 [OPTIONS]

Options:
    -h, --help          显示帮助信息
    -v, --version       显示版本信息
    -p, --platform      指定构建平台 (linux/amd64, windows/amd64, darwin/amd64, all)
    -o, --output        指定输出目录 (默认: ${DIST_DIR})
    -c, --clean         构建前清理
    --ldflags           自定义LDFLAGS
    --race              启用竞态检测
    --debug             启用调试模式

Examples:
    $0                          # 构建当前平台
    $0 -p all                   # 构建所有平台
    $0 -p linux/amd64          # 构建Linux AMD64
    $0 -c -p all               # 清理后构建所有平台
    $0 --race                  # 启用竞态检测构建

EOF
}

# 清理函数
clean_build() {
    log_info "Cleaning build directories..."
    rm -rf "${BUILD_DIR}" "${DIST_DIR}"
    log_success "Clean completed"
}

# 构建单个平台
build_platform() {
    local platform=$1
    local output_dir=$2
    
    IFS='/' read -r -a platform_split <<< "$platform"
    local goos="${platform_split[0]}"
    local goarch="${platform_split[1]}"
    
    local binary_name="monitor"
    if [ "$goos" = "windows" ]; then
        binary_name="monitor.exe"
    fi
    
    local output_path="${output_dir}/${goos}-${goarch}/${binary_name}"
    
    log_info "Building for ${goos}/${goarch}..."
    
    mkdir -p "$(dirname "$output_path")"
    
    env GOOS="$goos" GOARCH="$goarch" go build \
        $RACE_FLAG \
        $LDFLAGS \
        -o "$output_path" \
        "$MAIN_PATH"
    
    if [ $? -eq 0 ]; then
        local size=$(du -h "$output_path" | cut -f1)
        log_success "Built ${goos}/${goarch}: $output_path (${size})"
    else
        log_error "Failed to build ${goos}/${goarch}"
        return 1
    fi
}

# 构建所有平台
build_all() {
    local output_dir=$1
    local failed_builds=()
    
    log_info "Building for all platforms..."
    
    for platform in "${PLATFORMS[@]}"; do
        if ! build_platform "$platform" "$output_dir"; then
            failed_builds+=("$platform")
        fi
    done
    
    if [ ${#failed_builds[@]} -eq 0 ]; then
        log_success "All builds completed successfully"
    else
        log_warning "Some builds failed: ${failed_builds[*]}"
        return 1
    fi
}

# 构建当前平台
build_current() {
    local output_dir=$1
    local current_os=$(go env GOOS)
    local current_arch=$(go env GOARCH)
    local platform="${current_os}/${current_arch}"
    
    build_platform "$platform" "$output_dir"
}

# 主函数
main() {
    local platform=""
    local output_dir="$DIST_DIR"
    local clean_first=false
    local custom_ldflags=""
    local race_flag=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--version)
                echo "Version: $VERSION"
                echo "Build Time: $BUILD_TIME"
                echo "Git Commit: $GIT_COMMIT"
                exit 0
                ;;
            -p|--platform)
                platform="$2"
                shift 2
                ;;
            -o|--output)
                output_dir="$2"
                shift 2
                ;;
            -c|--clean)
                clean_first=true
                shift
                ;;
            --ldflags)
                custom_ldflags="$2"
                shift 2
                ;;
            --race)
                race_flag="-race"
                shift
                ;;
            --debug)
                set -x
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置全局变量
    RACE_FLAG="$race_flag"
    if [ -n "$custom_ldflags" ]; then
        LDFLAGS="$custom_ldflags"
    fi
    
    # 显示构建信息
    log_info "Server Monitor Build Script"
    log_info "Version: $VERSION"
    log_info "Build Time: $BUILD_TIME"
    log_info "Git Commit: $GIT_COMMIT"
    log_info "Output Directory: $output_dir"
    
    # 清理（如果需要）
    if [ "$clean_first" = true ]; then
        clean_build
    fi
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        log_error "Go is not installed or not in PATH"
        exit 1
    fi
    
    # 下载依赖
    log_info "Downloading dependencies..."
    go mod download
    
    # 执行构建
    case "$platform" in
        "")
            build_current "$output_dir"
            ;;
        "all")
            build_all "$output_dir"
            ;;
        */*)
            build_platform "$platform" "$output_dir"
            ;;
        *)
            log_error "Invalid platform format: $platform"
            log_error "Expected format: os/arch (e.g., linux/amd64)"
            exit 1
            ;;
    esac
    
    log_success "Build script completed"
}

# 运行主函数
main "$@"
