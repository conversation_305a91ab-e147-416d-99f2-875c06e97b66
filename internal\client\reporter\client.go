package reporter

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// HTTPClient defines the interface for an HTTP client capable of sending data.
type HTTPClient interface {
	Send(ctx context.Context, url string, data interface{}, headers map[string]string) (*http.Response, error)
}

// httpClient implements the HTTPClient interface.
type httpClient struct {
	client *http.Client
}

// NewHTTPClient creates a new HTTP client with a default timeout.
func NewHTTPClient(timeout time.Duration) HTTPClient {
	return &httpClient{
		client: &http.Client{
			Timeout: timeout,
		},
	}
}

// Send sends data as a JSON payload to the specified URL.
func (c *httpClient) Send(ctx context.Context, url string, data interface{}, headers map[string]string) (*http.Response, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	return resp, nil
}

// ResponseBodyToString reads the response body and returns it as a string.
func ResponseBodyToString(resp *http.Response) (string, error) {
	if resp == nil || resp.Body == nil {
		return "", nil
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}
	return string(bodyBytes), nil
}
