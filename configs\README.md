# 配置文件说明

本目录包含服务器监控系统的配置文件模板和示例。

## 配置文件列表

### 基础配置文件
- `config.yaml` - 当前使用的配置文件
- `config.yaml.example` - 基础配置模板
- `config-example.yaml` - 详细配置示例（包含注释说明）

### 环境特定配置模板
- `config-dev.yaml.example` - 开发环境配置模板
- `config-prod.yaml.example` - 生产环境配置模板
- `config-docker.yaml.example` - Docker环境配置模板

## 使用说明

### 1. 初次配置

```bash
# 复制基础模板
cp configs/config.yaml.example configs/config.yaml

# 或者复制详细示例
cp configs/config-example.yaml configs/config.yaml

# 编辑配置文件
vim configs/config.yaml
```

### 2. 环境特定配置

#### 开发环境
```bash
cp configs/config-dev.yaml.example configs/config.yaml
```

#### 生产环境
```bash
cp configs/config-prod.yaml.example configs/config.yaml
# 注意修改敏感信息如密码、证书路径等
```

#### Docker环境
```bash
cp configs/config-docker.yaml.example configs/config.yaml
# 配置相应的环境变量
```

### 3. 配置验证

```bash
# 验证配置文件语法
./monitor --config configs/config.yaml --validate

# 显示当前配置
./monitor --config configs/config.yaml --show
```

## 配置项说明

### 系统配置 (system)
- `name`: 系统名称
- `version`: 版本号
- `environment`: 运行环境 (development/testing/staging/production)
- `data_dir`: 数据存储目录
- `mode`: 运行模式 (server/client/both)

### 数据库配置 (database)
- `path`: SQLite数据库文件路径
- `max_open_conns`: 最大打开连接数
- `max_idle_conns`: 最大空闲连接数
- `enable_wal`: 是否启用WAL模式

### 调度器配置 (scheduler)
- `enabled`: 是否启用调度器
- `mode`: 调度模式 (odd/even/both)
- `timezone`: 时区设置
- `max_concurrent`: 最大并发测试数

### 服务器配置 (servers)
服务器列表，每个服务器包含：
- `name`: 服务器名称
- `ip`: IP地址
- `port`: iPerf3端口
- `location`: 地理位置
- `enabled`: 是否启用

### 对端配置 (peer)
双机同步配置：
- `ip`: 对端IP地址
- `port`: 对端API端口
- `username/password`: 认证信息
- `enabled`: 是否启用同步

### 测试配置 (test)
iPerf3测试参数：
- `duration`: 测试持续时间
- `parallel`: 并行连接数
- `protocol`: 协议类型 (tcp/udp/both)
- `timeout`: 超时时间

### Web界面配置 (web)
- `enabled`: 是否启用Web界面
- `port`: Web服务端口
- `host`: 监听地址
- `tls`: HTTPS配置

### API配置 (api)
- `enabled`: 是否启用API服务
- `port`: API端口
- `prefix`: API路径前缀
- `rate_limit`: 速率限制

### 日志配置 (log)
- `level`: 日志级别 (debug/info/warn/error)
- `format`: 日志格式 (json/text)
- `output`: 输出目标 (stdout/stderr/file)
- `file`: 日志文件路径

## 环境变量覆盖

所有配置项都可以通过环境变量覆盖，格式为：`MONITOR_<SECTION>_<KEY>`

示例：
```bash
export MONITOR_SYSTEM_MODE=server
export MONITOR_WEB_PORT=9090
export MONITOR_LOG_LEVEL=debug
```

## 配置文件优先级

1. 命令行参数
2. 环境变量
3. 配置文件
4. 默认值

## 安全注意事项

1. **密码安全**: 生产环境中请使用强密码
2. **文件权限**: 配置文件应设置适当的权限 (600)
3. **证书管理**: HTTPS证书应定期更新
4. **敏感信息**: 避免在配置文件中硬编码敏感信息，使用环境变量

## 故障排除

### 配置文件语法错误
```bash
# 检查YAML语法
yamllint configs/config.yaml
```

### 配置验证失败
```bash
# 查看详细错误信息
./monitor --config configs/config.yaml --validate --verbose
```

### 权限问题
```bash
# 检查文件权限
ls -la configs/config.yaml

# 设置正确权限
chmod 600 configs/config.yaml
```
