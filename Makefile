# Server Monitor Makefile
# Go项目构建配置

# 项目信息
PROJECT_NAME := server-monitor
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "v0.1.0-dev")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建配置
GO_VERSION := 1.19
MAIN_PATH := ./cmd/monitor
BINARY_NAME := monitor
BUILD_DIR := build
DIST_DIR := dist

# 构建标志
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT) -w -s"

# 默认目标
.DEFAULT_GOAL := help

# 帮助信息
.PHONY: help
help: ## 显示帮助信息
	@echo "Server Monitor Build System"
	@echo "Usage: make [target]"
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# 构建目标
.PHONY: build
build: ## 构建应用程序
	@echo "Building $(PROJECT_NAME) $(VERSION)..."
	@mkdir -p $(BUILD_DIR)
	go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "Build completed: $(BUILD_DIR)/$(BINARY_NAME)"

.PHONY: build-all
build-all: ## 构建所有平台版本
	@echo "Building for all platforms..."
	@$(MAKE) build-linux
	@$(MAKE) build-windows
	@$(MAKE) build-darwin
	@echo "All builds completed"

.PHONY: build-linux
build-linux: ## 构建Linux版本
	@echo "Building for Linux..."
	@mkdir -p $(DIST_DIR)/linux
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(DIST_DIR)/linux/$(BINARY_NAME) $(MAIN_PATH)

.PHONY: build-windows
build-windows: ## 构建Windows版本
	@echo "Building for Windows..."
	@mkdir -p $(DIST_DIR)/windows
	GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o $(DIST_DIR)/windows/$(BINARY_NAME).exe $(MAIN_PATH)

.PHONY: build-darwin
build-darwin: ## 构建macOS版本
	@echo "Building for macOS..."
	@mkdir -p $(DIST_DIR)/darwin
	GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o $(DIST_DIR)/darwin/$(BINARY_NAME) $(MAIN_PATH)

# 测试目标
.PHONY: test
test: ## 运行测试
	@echo "Running tests..."
	go test -v ./...

.PHONY: test-coverage
test-coverage: ## 运行测试并生成覆盖率报告
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

.PHONY: benchmark
benchmark: ## 运行基准测试
	@echo "Running benchmarks..."
	go test -bench=. -benchmem ./...

# 代码质量
.PHONY: fmt
fmt: ## 格式化代码
	@echo "Formatting code..."
	go fmt ./...

.PHONY: vet
vet: ## 运行go vet
	@echo "Running go vet..."
	go vet ./...

.PHONY: lint
lint: ## 运行golangci-lint
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed. Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

# 依赖管理
.PHONY: deps
deps: ## 下载依赖
	@echo "Downloading dependencies..."
	go mod download

.PHONY: deps-update
deps-update: ## 更新依赖
	@echo "Updating dependencies..."
	go get -u ./...
	go mod tidy

.PHONY: deps-verify
deps-verify: ## 验证依赖
	@echo "Verifying dependencies..."
	go mod verify

# 清理目标
.PHONY: clean
clean: ## 清理构建文件
	@echo "Cleaning build files..."
	@rm -rf $(BUILD_DIR)
	@rm -rf $(DIST_DIR)
	@rm -f coverage.out coverage.html
	@rm -f $(BINARY_NAME) $(BINARY_NAME).exe
	@echo "Clean completed"

# 安装目标
.PHONY: install
install: ## 安装到GOPATH/bin
	@echo "Installing $(PROJECT_NAME)..."
	go install $(LDFLAGS) $(MAIN_PATH)

# 运行目标
.PHONY: run
run: ## 运行应用程序
	@echo "Running $(PROJECT_NAME)..."
	go run $(MAIN_PATH)

.PHONY: run-server
run-server: ## 运行服务端模式
	@echo "Running server mode..."
	go run $(MAIN_PATH) -s -p 8443 -pw "dev-password" -web-port 8080

.PHONY: run-client
run-client: ## 运行客户端模式
	@echo "Running client mode..."
	go run $(MAIN_PATH) -c -n "dev-client" -ip localhost -p 8443 -pw "dev-password"

# 开发工具
.PHONY: dev-setup
dev-setup: ## 设置开发环境
	@echo "Setting up development environment..."
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/air-verse/air@latest
	@echo "Development tools installed"

.PHONY: dev
dev: ## 启动开发模式（热重载）
	@echo "Starting development mode with hot reload..."
	@if command -v air >/dev/null 2>&1; then \
		air; \
	else \
		echo "Air not installed. Run 'make dev-setup' first"; \
	fi

# 版本信息
.PHONY: version
version: ## 显示版本信息
	@echo "Project: $(PROJECT_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "Git Commit: $(GIT_COMMIT)"
	@echo "Go Version: $(shell go version)"
