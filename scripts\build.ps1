# Server Monitor PowerShell Build Script
# Windows PowerShell构建脚本

param(
    [string]$Command = "build",
    [string]$Platform = "",
    [string]$OutputDir = "dist",
    [switch]$Clean,
    [switch]$Race,
    [switch]$Help
)

# 项目配置
$ProjectName = "server-monitor"
$MainPath = "./cmd/monitor"
$BuildDir = "build"
$BinaryName = "monitor"

# 获取版本信息
try {
    $Version = git describe --tags --always --dirty 2>$null
    if (-not $Version) { $Version = "v0.1.0-dev" }
} catch {
    $Version = "v0.1.0-dev"
}

try {
    $GitCommit = git rev-parse --short HEAD 2>$null
    if (-not $GitCommit) { $GitCommit = "unknown" }
} catch {
    $GitCommit = "unknown"
}

$BuildTime = Get-Date -Format "yyyy-MM-dd_HH:mm:ss"

# 构建标志
$LDFlags = "-ldflags `"-X main.Version=$Version -X main.BuildTime=$BuildTime -X main.GitCommit=$GitCommit -w -s`""

# 支持的平台
$Platforms = @(
    @{OS="windows"; Arch="amd64"},
    @{OS="linux"; Arch="amd64"},
    @{OS="darwin"; Arch="amd64"},
    @{OS="darwin"; Arch="arm64"}
)

# 显示帮助信息
function Show-Help {
    Write-Host "Server Monitor PowerShell Build Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\build.ps1 [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Command <cmd>     Command to execute (build, build-all, test, clean, fmt, vet, deps, run, version)"
    Write-Host "  -Platform <plat>   Target platform (windows/amd64, linux/amd64, darwin/amd64, all)"
    Write-Host "  -OutputDir <dir>   Output directory (default: dist)"
    Write-Host "  -Clean             Clean before build"
    Write-Host "  -Race              Enable race detection"
    Write-Host "  -Help              Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Cyan
    Write-Host "  .\build.ps1                           # Build current platform"
    Write-Host "  .\build.ps1 -Command build-all        # Build all platforms"
    Write-Host "  .\build.ps1 -Platform linux/amd64     # Build for Linux"
    Write-Host "  .\build.ps1 -Clean -Command build-all # Clean and build all"
}

# 日志函数
function Write-Info($Message) {
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success($Message) {
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning($Message) {
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error($Message) {
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 清理函数
function Clean-Build {
    Write-Info "Cleaning build directories..."
    if (Test-Path $BuildDir) { Remove-Item -Recurse -Force $BuildDir }
    if (Test-Path $OutputDir) { Remove-Item -Recurse -Force $OutputDir }
    if (Test-Path "coverage.out") { Remove-Item "coverage.out" }
    if (Test-Path "coverage.html") { Remove-Item "coverage.html" }
    if (Test-Path "$BinaryName.exe") { Remove-Item "$BinaryName.exe" }
    Write-Success "Clean completed"
}

# 构建单个平台
function Build-Platform($OS, $Arch, $OutputPath) {
    $BinaryExt = if ($OS -eq "windows") { ".exe" } else { "" }
    $BinaryFile = "$BinaryName$BinaryExt"
    $FullOutputPath = Join-Path $OutputPath "$OS-$Arch" $BinaryFile
    
    Write-Info "Building for $OS/$Arch..."
    
    $OutputDirPath = Split-Path $FullOutputPath -Parent
    if (-not (Test-Path $OutputDirPath)) {
        New-Item -ItemType Directory -Path $OutputDirPath -Force | Out-Null
    }
    
    $env:GOOS = $OS
    $env:GOARCH = $Arch
    
    $BuildArgs = @()
    if ($Race) { $BuildArgs += "-race" }
    $BuildArgs += $LDFlags.Split(' ')
    $BuildArgs += "-o", $FullOutputPath, $MainPath
    
    $Process = Start-Process -FilePath "go" -ArgumentList ("build" + $BuildArgs) -Wait -PassThru -NoNewWindow
    
    if ($Process.ExitCode -eq 0) {
        $Size = (Get-Item $FullOutputPath).Length
        $SizeStr = if ($Size -gt 1MB) { "{0:N1} MB" -f ($Size / 1MB) } else { "{0:N0} KB" -f ($Size / 1KB) }
        Write-Success "Built $OS/$Arch`: $FullOutputPath ($SizeStr)"
        return $true
    } else {
        Write-Error "Failed to build $OS/$Arch"
        return $false
    }
}

# 构建当前平台
function Build-Current($OutputPath) {
    $CurrentOS = go env GOOS
    $CurrentArch = go env GOARCH
    return Build-Platform $CurrentOS $CurrentArch $OutputPath
}

# 构建所有平台
function Build-All($OutputPath) {
    Write-Info "Building for all platforms..."
    $FailedBuilds = @()
    
    foreach ($Platform in $Platforms) {
        if (-not (Build-Platform $Platform.OS $Platform.Arch $OutputPath)) {
            $FailedBuilds += "$($Platform.OS)/$($Platform.Arch)"
        }
    }
    
    if ($FailedBuilds.Count -eq 0) {
        Write-Success "All builds completed successfully"
        return $true
    } else {
        Write-Warning "Some builds failed: $($FailedBuilds -join ', ')"
        return $false
    }
}

# 运行测试
function Run-Tests {
    Write-Info "Running tests..."
    go test -v ./...
}

# 格式化代码
function Format-Code {
    Write-Info "Formatting code..."
    go fmt ./...
}

# 运行go vet
function Run-Vet {
    Write-Info "Running go vet..."
    go vet ./...
}

# 下载依赖
function Download-Dependencies {
    Write-Info "Downloading dependencies..."
    go mod download
}

# 运行应用
function Run-Application {
    Write-Info "Running $ProjectName..."
    go run $MainPath
}

# 显示版本信息
function Show-Version {
    Write-Host "Project: $ProjectName" -ForegroundColor Cyan
    Write-Host "Version: $Version" -ForegroundColor Cyan
    Write-Host "Build Time: $BuildTime" -ForegroundColor Cyan
    Write-Host "Git Commit: $GitCommit" -ForegroundColor Cyan
    $GoVersion = go version
    Write-Host "Go Version: $GoVersion" -ForegroundColor Cyan
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Info "Server Monitor PowerShell Build Script"
    Write-Info "Version: $Version"
    Write-Info "Build Time: $BuildTime"
    Write-Info "Git Commit: $GitCommit"
    
    if ($Clean) {
        Clean-Build
    }
    
    # 检查Go环境
    try {
        go version | Out-Null
    } catch {
        Write-Error "Go is not installed or not in PATH"
        exit 1
    }
    
    switch ($Command.ToLower()) {
        "build" {
            if ($Platform -eq "all") {
                Build-All $OutputDir
            } elseif ($Platform -match "^(\w+)/(\w+)$") {
                $OS = $Matches[1]
                $Arch = $Matches[2]
                Build-Platform $OS $Arch $OutputDir
            } else {
                Build-Current $OutputDir
            }
        }
        "build-all" {
            Build-All $OutputDir
        }
        "test" {
            Run-Tests
        }
        "clean" {
            Clean-Build
        }
        "fmt" {
            Format-Code
        }
        "vet" {
            Run-Vet
        }
        "deps" {
            Download-Dependencies
        }
        "run" {
            Run-Application
        }
        "version" {
            Show-Version
        }
        default {
            Write-Error "Unknown command: $Command"
            Show-Help
            exit 1
        }
    }
}

# 运行主函数
Main
