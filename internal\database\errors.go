package database

import "errors"

// 数据库相关错误定义
var (
	// 连接错误
	ErrDatabaseConnection = errors.New("database connection failed")
	ErrDatabaseClosed     = errors.New("database connection is closed")
	ErrTransactionFailed  = errors.New("transaction failed")

	// 数据验证错误
	ErrInvalidServerName   = errors.New("invalid server name")
	ErrInvalidServerIP     = errors.New("invalid server IP")
	ErrInvalidServerPort   = errors.New("invalid server port")
	ErrInvalidServerID     = errors.New("invalid server ID")
	ErrInvalidTestHour     = errors.New("invalid test hour")
	ErrInvalidTestStatus   = errors.New("invalid test status")
	ErrInvalidPeerIP       = errors.New("invalid peer IP")
	ErrInvalidSyncStatus   = errors.New("invalid sync status")
	ErrInvalidConfigKey    = errors.New("invalid config key")
	ErrInvalidConfigType   = errors.New("invalid config type")

	// 数据操作错误
	ErrRecordNotFound     = errors.New("record not found")
	ErrRecordExists       = errors.New("record already exists")
	ErrInvalidQuery       = errors.New("invalid query")
	ErrInvalidParameters  = errors.New("invalid parameters")

	// 迁移错误
	ErrMigrationFailed    = errors.New("migration failed")
	ErrInvalidVersion     = errors.New("invalid migration version")
	ErrVersionConflict    = errors.New("migration version conflict")
)
