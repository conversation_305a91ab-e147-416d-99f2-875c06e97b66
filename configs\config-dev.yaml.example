# Server Monitor Development Configuration Template
# 服务器监控系统开发环境配置模板

# 系统配置
system:
  name: "Server Monitor Dev"
  version: "1.0.0-dev"
  environment: "development"
  data_dir: "./data"
  mode: "both"

# 数据库配置
database:
  path: "./data/monitor-dev.db"
  max_open_conns: 10
  max_idle_conns: 2
  enable_wal: false
  enable_foreign_key: true

# 调度器配置
scheduler:
  enabled: true
  mode: "both"
  timezone: "Asia/Shanghai"
  max_concurrent: 2
  retry_attempts: 1
  retry_interval: "1m"

# 测试服务器配置（开发用）
servers:
  - name: "dev-server-01"
    ip: "127.0.0.1"
    port: 5201
    location: "Local"
    provider: "Development"
    enabled: true
    priority: 1

# 测试参数配置
test:
  duration: "10s"
  parallel: 2
  protocol: "tcp"
  timeout: "30s"
  max_retries: 1

# Web界面配置
web:
  enabled: true
  port: 8080
  host: "127.0.0.1"

# API配置
api:
  enabled: true
  port: 8443
  host: "127.0.0.1"
  prefix: "/api/v1"

# 日志配置
log:
  level: "debug"
  format: "text"
  output: "stdout"
