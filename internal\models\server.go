package models

import (
	"time"

	"gorm.io/gorm"
)

// Server 表示服务器表 (Server represents the server table)
type Server struct {
	gorm.Model
	Name        string `gorm:"unique;not null"`
	IPAddress   string `gorm:"size:45;not null"`
	Hostname    string `gorm:"size:255"`
	OSType      string `gorm:"size:50"`
	OSVersion   string `gorm:"size:100"`
	LastSeen    time.Time
	IsActive    bool   `gorm:"default:true"`
	Description string `gorm:"type:text"`
	Tags        string `gorm:"type:text"` // 存储为 JSON 字符串 (Stored as JSON string)
}
