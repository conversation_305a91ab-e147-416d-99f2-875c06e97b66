package database

import (
	"fmt"
	"log"
	"server-monitor/internal/models" // Import models package
	"server-monitor/internal/config" // Import config package
)

// InitDatabase initializes the database and returns a Repository instance
func InitDatabase(cfg *config.DatabaseConfig) (Repository, error) {
	// Initialize the global DB instance
	err := Init(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize global database: %w", err)
	}

	// Create repository
	repo := NewRepository(DB)

	// Initialize default configurations
	if err := initDefaultConfigs(repo); err != nil {
		log.Printf("Warning: failed to initialize default configs: %v", err)
	}

	return repo, nil
}

// initDefaultConfigs 初始化默认系统配置
func initDefaultConfigs(repo Repository) error {
	defaultConfigs := map[string]struct {
		value       string
		description string
		configType  string
		isSystem    bool
	}{
		"system.version": {
			value:       "1.0.0",
			description: "系统版本号",
			configType:  ConfigTypeString,
			isSystem:    true,
		},
		"system.name": {
			value:       "Server Monitor",
			description: "系统名称",
			configType:  ConfigTypeString,
			isSystem:    true,
		},
		"test.interval": {
			value:       "3600",
			description: "测试间隔时间（秒）",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"test.duration": {
			value:       "30",
			description: "单次测试持续时间（秒）",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"test.parallel": {
			value:       "4",
			description: "并行测试数量",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"sync.enabled": {
			value:       "true",
			description: "是否启用双机同步",
			configType:  ConfigTypeBool,
			isSystem:    true,
		},
		"sync.interval": {
			value:       "300",
			description: "同步间隔时间（秒）",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"web.port": {
			value:       "8080",
			description: "Web界面端口",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"api.port": {
			value:       "8443",
			description: "API服务端口",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"log.level": {
			value:       "info",
			description: "日志级别",
			configType:  ConfigTypeString,
			isSystem:    true,
		},
	}

	for key, config := range defaultConfigs {
		// 检查配置是否已存在
		existing, err := repo.GetConfig(key)
		if err == ErrRecordNotFound {
			// 配置不存在，创建默认配置
			if err := repo.SetConfig(key, config.value, config.description, config.configType, config.isSystem); err != nil {
				return fmt.Errorf("failed to set default config %s: %w", key, err)
			}
		} else if err != nil {
			return fmt.Errorf("failed to check config %s: %w", key, err)
		} else {
			// 配置已存在，更新描述和类型（但不更新值）
			if err := repo.SetConfig(key, existing.ConfigValue, config.description, config.configType, config.isSystem); err != nil {
				return fmt.Errorf("failed to update config %s: %w", key, err)
			}
		}
	}

	return nil
}

// InitTestServers 初始化测试服务器数据
func InitTestServers(repo Repository) error {
	testServers := []*models.Server{
		{
			Name:      "server-01",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-02",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-03",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-04",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-05",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-06",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-07",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-08",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-09",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-10",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-11",
			IPAddress: "*************",
			IsActive:  true,
		},
		{
			Name:      "server-12",
			IPAddress: "*************",
			IsActive:  true,
		},
	}

	for _, server := range testServers {
		// 检查服务器是否已存在
		existing, err := repo.GetServerByName(server.Name)
		if err == ErrRecordNotFound {
			// 服务器不存在，创建新服务器
			if err := repo.CreateServer(server); err != nil {
				return fmt.Errorf("failed to create server %s: %w", server.Name, err)
			}
			log.Printf("Created test server: %s (%s)", server.Name, server.IPAddress)
		} else if err != nil {
			return fmt.Errorf("failed to check server %s: %w", server.Name, err)
		} else {
			log.Printf("Test server already exists: %s (%s)", existing.Name, existing.IPAddress)
		}
	}

	return nil
}
