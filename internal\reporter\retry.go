package reporter

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"sync"
	"time"

	"server-monitor/internal/logger"
)

// RetryStrategy 重试策略
type RetryStrategy int

const (
	RetryStrategyFixed       RetryStrategy = iota // 固定间隔
	RetryStrategyLinear                           // 线性递增
	RetryStrategyExponential                      // 指数退避
	RetryStrategyJittered                         // 带抖动的指数退避
)

// String 返回策略字符串
func (s RetryStrategy) String() string {
	switch s {
	case RetryStrategyFixed:
		return "固定间隔"
	case RetryStrategyLinear:
		return "线性递增"
	case RetryStrategyExponential:
		return "指数退避"
	case RetryStrategyJittered:
		return "带抖动指数退避"
	default:
		return "未知"
	}
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts  int           `yaml:"max_attempts"`  // 最大重试次数
	InitialDelay time.Duration `yaml:"initial_delay"` // 初始延迟
	MaxDelay     time.Duration `yaml:"max_delay"`     // 最大延迟
	Multiplier   float64       `yaml:"multiplier"`    // 乘数因子
	Strategy     RetryStrategy `yaml:"strategy"`      // 重试策略
	JitterFactor float64       `yaml:"jitter_factor"` // 抖动因子
	Timeout      time.Duration `yaml:"timeout"`       // 总超时时间
}

// DefaultRetryConfig 默认重试配置
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxAttempts:  3,
		InitialDelay: 1 * time.Second,
		MaxDelay:     30 * time.Second,
		Multiplier:   2.0,
		Strategy:     RetryStrategyExponential,
		JitterFactor: 0.1,
		Timeout:      5 * time.Minute,
	}
}

// RetryableFunc 可重试的函数
type RetryableFunc func(ctx context.Context, attempt int) error

// RetryCondition 重试条件函数
type RetryCondition func(err error, attempt int) bool

// RetryResult 重试结果
type RetryResult struct {
	Success      bool            `json:"success"`
	Attempts     int             `json:"attempts"`
	TotalTime    time.Duration   `json:"total_time"`
	LastError    error           `json:"last_error"`
	AttemptTimes []time.Duration `json:"attempt_times"`
}

// Retryer 重试器
type Retryer struct {
	config    *RetryConfig
	logger    *logger.Logger
	condition RetryCondition
	mu        sync.RWMutex
	stats     *RetryStats
}

// RetryStats 重试统计
type RetryStats struct {
	TotalRetries    int64         `json:"total_retries"`
	SuccessRetries  int64         `json:"success_retries"`
	FailedRetries   int64         `json:"failed_retries"`
	AverageAttempts float64       `json:"average_attempts"`
	AverageTime     time.Duration `json:"average_time"`
	LastRetryTime   time.Time     `json:"last_retry_time"`
}

// NewRetryer 创建重试器
func NewRetryer(config *RetryConfig, logger *logger.Logger) *Retryer {
	if config == nil {
		config = DefaultRetryConfig()
	}

	return &Retryer{
		config:    config,
		logger:    logger,
		condition: DefaultRetryCondition,
		stats:     &RetryStats{},
	}
}

// SetRetryCondition 设置重试条件
func (r *Retryer) SetRetryCondition(condition RetryCondition) {
	r.condition = condition
}

// Execute 执行可重试函数
func (r *Retryer) Execute(ctx context.Context, fn RetryableFunc) *RetryResult {
	startTime := time.Now()

	result := &RetryResult{
		AttemptTimes: make([]time.Duration, 0, r.config.MaxAttempts),
	}

	// 设置总超时
	if r.config.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, r.config.Timeout)
		defer cancel()
	}

	var lastError error

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		attemptStart := time.Now()

		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			result.LastError = ctx.Err()
			r.updateFailedStats(result)
			return result
		default:
		}

		// 执行函数
		err := fn(ctx, attempt)
		attemptTime := time.Since(attemptStart)
		result.AttemptTimes = append(result.AttemptTimes, attemptTime)
		result.Attempts = attempt

		if err == nil {
			// 成功
			result.Success = true
			result.TotalTime = time.Since(startTime)
			r.updateSuccessStats(result)
			r.logger.Debug("重试成功，尝试次数: %d, 总时间: %v", attempt, result.TotalTime)
			return result
		}

		lastError = err
		result.LastError = err

		r.logger.Debug("重试失败，尝试 %d/%d: %v", attempt, r.config.MaxAttempts, err)

		// 检查是否应该重试
		if attempt >= r.config.MaxAttempts || !r.condition(err, attempt) {
			break
		}

		// 计算延迟时间
		delay := r.calculateDelay(attempt)

		r.logger.Debug("等待 %v 后进行第 %d 次重试", delay, attempt+1)

		// 等待延迟
		select {
		case <-ctx.Done():
			result.LastError = ctx.Err()
			r.updateFailedStats(result)
			return result
		case <-time.After(delay):
		}
	}

	// 所有重试都失败了
	result.Success = false
	result.TotalTime = time.Since(startTime)
	result.LastError = lastError
	r.updateFailedStats(result)

	r.logger.Error("重试最终失败，尝试次数: %d, 总时间: %v, 错误: %v",
		result.Attempts, result.TotalTime, lastError)

	return result
}

// calculateDelay 计算延迟时间
func (r *Retryer) calculateDelay(attempt int) time.Duration {
	var delay time.Duration

	switch r.config.Strategy {
	case RetryStrategyFixed:
		delay = r.config.InitialDelay

	case RetryStrategyLinear:
		delay = time.Duration(attempt) * r.config.InitialDelay

	case RetryStrategyExponential:
		delay = time.Duration(float64(r.config.InitialDelay) * math.Pow(r.config.Multiplier, float64(attempt-1)))

	case RetryStrategyJittered:
		baseDelay := time.Duration(float64(r.config.InitialDelay) * math.Pow(r.config.Multiplier, float64(attempt-1)))
		jitter := time.Duration(float64(baseDelay) * r.config.JitterFactor * (rand.Float64()*2 - 1))
		delay = baseDelay + jitter

	default:
		delay = r.config.InitialDelay
	}

	// 限制最大延迟
	if delay > r.config.MaxDelay {
		delay = r.config.MaxDelay
	}

	// 确保延迟不为负数
	if delay < 0 {
		delay = r.config.InitialDelay
	}

	return delay
}

// updateSuccessStats 更新成功统计
func (r *Retryer) updateSuccessStats(result *RetryResult) {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.stats.TotalRetries++
	r.stats.SuccessRetries++
	r.stats.LastRetryTime = time.Now()

	// 更新平均尝试次数
	r.stats.AverageAttempts = (r.stats.AverageAttempts*float64(r.stats.SuccessRetries-1) + float64(result.Attempts)) / float64(r.stats.SuccessRetries)

	// 更新平均时间
	if r.stats.SuccessRetries == 1 {
		r.stats.AverageTime = result.TotalTime
	} else {
		r.stats.AverageTime = time.Duration(
			(int64(r.stats.AverageTime)*int64(r.stats.SuccessRetries-1) + int64(result.TotalTime)) / int64(r.stats.SuccessRetries),
		)
	}
}

// updateFailedStats 更新失败统计
func (r *Retryer) updateFailedStats(result *RetryResult) {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.stats.TotalRetries++
	r.stats.FailedRetries++
	r.stats.LastRetryTime = time.Now()
}

// GetStats 获取统计信息
func (r *Retryer) GetStats() *RetryStats {
	r.mu.RLock()
	defer r.mu.RUnlock()

	stats := *r.stats
	return &stats
}

// GetConfig 获取配置
func (r *Retryer) GetConfig() *RetryConfig {
	return r.config
}

// DefaultRetryCondition 默认重试条件
func DefaultRetryCondition(err error, attempt int) bool {
	if err == nil {
		return false
	}

	// 对于测试，总是重试（除非是特定的不可重试错误）
	errStr := err.Error()

	// 不可重试的错误
	nonRetryableErrors := []string{
		"authentication failed",
		"permission denied",
		"invalid request",
		"bad request",
	}

	for _, nonRetryErr := range nonRetryableErrors {
		if contains(errStr, nonRetryErr) {
			return false
		}
	}

	// 默认情况下，大部分错误都可以重试
	return true
}

// NetworkRetryCondition 网络重试条件
func NetworkRetryCondition(err error, attempt int) bool {
	if err == nil {
		return false
	}

	// 只重试网络相关错误
	errStr := err.Error()
	networkErrors := []string{
		"connection",
		"timeout",
		"network",
		"dns",
		"resolve",
	}

	for _, netErr := range networkErrors {
		if contains(errStr, netErr) {
			return true
		}
	}

	return false
}

// HTTPRetryCondition HTTP重试条件
func HTTPRetryCondition(err error, attempt int) bool {
	if err == nil {
		return false
	}

	// 只重试特定的HTTP错误
	errStr := err.Error()
	retryableStatuses := []string{
		"429", "500", "502", "503", "504",
	}

	for _, status := range retryableStatuses {
		if contains(errStr, status) {
			return true
		}
	}

	return false
}

// RetryableHTTPClient 可重试的HTTP客户端
type RetryableHTTPClient struct {
	client  *HTTPClient
	retryer *Retryer
	logger  *logger.Logger
}

// NewRetryableHTTPClient 创建可重试的HTTP客户端
func NewRetryableHTTPClient(client *HTTPClient, config *RetryConfig, logger *logger.Logger) *RetryableHTTPClient {
	retryer := NewRetryer(config, logger)
	retryer.SetRetryCondition(HTTPRetryCondition)

	return &RetryableHTTPClient{
		client:  client,
		retryer: retryer,
		logger:  logger,
	}
}

// Request 发送可重试的HTTP请求
func (c *RetryableHTTPClient) Request(ctx context.Context, options *RequestOptions) (*Response, error) {
	var response *Response

	result := c.retryer.Execute(ctx, func(ctx context.Context, attempt int) error {
		c.logger.Debug("HTTP请求尝试 %d: %s %s", attempt, options.Method, options.URL)

		resp, err := c.client.Request(ctx, options)
		if err != nil {
			return err
		}

		response = resp
		return nil
	})

	if !result.Success {
		return nil, fmt.Errorf("HTTP请求重试失败: %w", result.LastError)
	}

	return response, nil
}

// GetRetryStats 获取重试统计
func (c *RetryableHTTPClient) GetRetryStats() *RetryStats {
	return c.retryer.GetStats()
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
				s[len(s)-len(substr):] == substr ||
				containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
