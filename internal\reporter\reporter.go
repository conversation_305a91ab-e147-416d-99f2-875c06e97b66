package reporter

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/logger"
	"server-monitor/internal/monitor"
	"server-monitor/internal/utils"
)

// ReportType 上报类型
type ReportType string

const (
	ReportTypeSystem    ReportType = "system"    // 系统信息
	ReportTypeMetrics   ReportType = "metrics"   // 指标数据
	ReportTypeEvents    ReportType = "events"    // 事件数据
	ReportTypeAlerts    ReportType = "alerts"    // 告警数据
	ReportTypeHeartbeat ReportType = "heartbeat" // 心跳数据
)

// ReportData 上报数据
type ReportData struct {
	ID        string                 `json:"id"`                 // 数据ID
	Type      ReportType             `json:"type"`               // 数据类型
	Timestamp time.Time              `json:"timestamp"`          // 时间戳
	Hostname  string                 `json:"hostname"`           // 主机名
	ClientID  string                 `json:"client_id"`          // 客户端ID
	Data      interface{}            `json:"data"`               // 数据内容
	Metadata  map[string]interface{} `json:"metadata,omitempty"` // 元数据
}

// ReportResult 上报结果
type ReportResult struct {
	Success   bool          `json:"success"`
	Message   string        `json:"message"`
	Latency   time.Duration `json:"latency"`
	Timestamp time.Time     `json:"timestamp"`
	Error     error         `json:"error,omitempty"`
}

// Reporter 数据上报器接口
type Reporter interface {
	Report(ctx context.Context, data *ReportData) (*ReportResult, error)
	BatchReport(ctx context.Context, dataList []*ReportData) ([]*ReportResult, error)
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	IsHealthy() bool
	GetStats() *ReporterStats
}

// ReporterStats 上报器统计信息
type ReporterStats struct {
	TotalReports   int64         `json:"total_reports"`
	SuccessReports int64         `json:"success_reports"`
	FailedReports  int64         `json:"failed_reports"`
	BatchReports   int64         `json:"batch_reports"`
	QueueSize      int           `json:"queue_size"`
	LastReportTime time.Time     `json:"last_report_time"`
	AverageLatency time.Duration `json:"average_latency"`
	ErrorRate      float64       `json:"error_rate"`
}

// HTTPReporter HTTP上报器
type HTTPReporter struct {
	config     *config.Config
	logger     *logger.Logger
	httpClient *HTTPClient

	// 队列管理
	queue        chan *ReportData
	batchSize    int
	batchTimeout time.Duration

	// 状态管理
	running bool
	mu      sync.RWMutex

	// 统计信息
	stats   *ReporterStats
	statsMu sync.RWMutex

	// 工作协程
	workers  int
	workerWG sync.WaitGroup
	stopCh   chan struct{}
}

// NewHTTPReporter 创建HTTP上报器
func NewHTTPReporter(cfg *config.Config, logger *logger.Logger) *HTTPReporter {
	httpClient := NewHTTPClient(cfg, logger)

	return &HTTPReporter{
		config:       cfg,
		logger:       logger,
		httpClient:   httpClient,
		queue:        make(chan *ReportData, 1000),
		batchSize:    10,
		batchTimeout: 5 * time.Second,
		workers:      4,
		stopCh:       make(chan struct{}),
		stats:        &ReporterStats{},
	}
}

// Start 启动上报器
func (r *HTTPReporter) Start(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.running {
		return fmt.Errorf("上报器已在运行")
	}

	r.running = true
	r.logger.Info("启动HTTP上报器，工作协程数: %d", r.workers)

	// 启动工作协程
	for i := 0; i < r.workers; i++ {
		r.workerWG.Add(1)
		go r.worker(ctx, i)
	}

	// 启动批量处理协程
	r.workerWG.Add(1)
	go r.batchProcessor(ctx)

	return nil
}

// Stop 停止上报器
func (r *HTTPReporter) Stop(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.running {
		return nil
	}

	r.running = false
	r.logger.Info("停止HTTP上报器...")

	// 关闭停止通道
	close(r.stopCh)

	// 等待工作协程结束
	done := make(chan struct{})
	go func() {
		r.workerWG.Wait()
		close(done)
	}()

	// 设置超时
	select {
	case <-done:
		r.logger.Info("HTTP上报器已停止")
	case <-time.After(10 * time.Second):
		r.logger.Warn("HTTP上报器停止超时")
	}

	// 关闭HTTP客户端
	return r.httpClient.Close()
}

// Report 上报单个数据
func (r *HTTPReporter) Report(ctx context.Context, data *ReportData) (*ReportResult, error) {
	if !r.IsHealthy() {
		return nil, fmt.Errorf("上报器未运行")
	}

	// 设置默认值
	if data.ID == "" {
		data.ID = utils.GenerateUUID()
	}
	if data.Timestamp.IsZero() {
		data.Timestamp = time.Now()
	}
	if data.Hostname == "" {
		data.Hostname = utils.GetHostname()
	}

	// 直接发送
	return r.sendData(ctx, data)
}

// BatchReport 批量上报数据
func (r *HTTPReporter) BatchReport(ctx context.Context, dataList []*ReportData) ([]*ReportResult, error) {
	if !r.IsHealthy() {
		return nil, fmt.Errorf("上报器未运行")
	}

	results := make([]*ReportResult, len(dataList))

	for i, data := range dataList {
		result, err := r.Report(ctx, data)
		if err != nil {
			results[i] = &ReportResult{
				Success:   false,
				Message:   err.Error(),
				Timestamp: time.Now(),
				Error:     err,
			}
		} else {
			results[i] = result
		}
	}

	r.statsMu.Lock()
	r.stats.BatchReports++
	r.statsMu.Unlock()

	return results, nil
}

// QueueReport 队列上报（异步）
func (r *HTTPReporter) QueueReport(data *ReportData) error {
	if !r.IsHealthy() {
		return fmt.Errorf("上报器未运行")
	}

	// 设置默认值
	if data.ID == "" {
		data.ID = utils.GenerateUUID()
	}
	if data.Timestamp.IsZero() {
		data.Timestamp = time.Now()
	}
	if data.Hostname == "" {
		data.Hostname = utils.GetHostname()
	}

	select {
	case r.queue <- data:
		return nil
	default:
		return fmt.Errorf("上报队列已满")
	}
}

// worker 工作协程
func (r *HTTPReporter) worker(ctx context.Context, workerID int) {
	defer r.workerWG.Done()

	r.logger.Debug("上报工作协程 %d 启动", workerID)

	for {
		select {
		case <-ctx.Done():
			r.logger.Debug("上报工作协程 %d 停止（上下文取消）", workerID)
			return
		case <-r.stopCh:
			r.logger.Debug("上报工作协程 %d 停止", workerID)
			return
		case data := <-r.queue:
			r.processData(ctx, data)
		}
	}
}

// batchProcessor 批量处理协程
func (r *HTTPReporter) batchProcessor(ctx context.Context) {
	defer r.workerWG.Done()

	r.logger.Debug("批量处理协程启动")

	ticker := time.NewTicker(r.batchTimeout)
	defer ticker.Stop()

	batch := make([]*ReportData, 0, r.batchSize)

	for {
		select {
		case <-ctx.Done():
			r.logger.Debug("批量处理协程停止（上下文取消）")
			return
		case <-r.stopCh:
			r.logger.Debug("批量处理协程停止")
			return
		case <-ticker.C:
			if len(batch) > 0 {
				r.processBatch(ctx, batch)
				batch = batch[:0]
			}
		case data := <-r.queue:
			batch = append(batch, data)
			if len(batch) >= r.batchSize {
				r.processBatch(ctx, batch)
				batch = batch[:0]
			}
		}
	}
}

// processData 处理单个数据
func (r *HTTPReporter) processData(ctx context.Context, data *ReportData) {
	result, err := r.sendData(ctx, data)
	if err != nil {
		r.logger.Error("上报数据失败: %v", err)
		r.updateFailedStats()
	} else {
		r.logger.Debug("上报数据成功: %s", data.ID)
		r.updateSuccessStats(result.Latency)
	}
}

// processBatch 处理批量数据
func (r *HTTPReporter) processBatch(ctx context.Context, batch []*ReportData) {
	r.logger.Debug("处理批量数据，数量: %d", len(batch))

	results, err := r.sendBatchData(ctx, batch)
	if err != nil {
		r.logger.Error("批量上报失败: %v", err)
		r.statsMu.Lock()
		r.stats.FailedReports += int64(len(batch))
		r.statsMu.Unlock()
		return
	}

	// 统计结果
	var successCount, failedCount int64
	var totalLatency time.Duration

	for _, result := range results {
		if result.Success {
			successCount++
			totalLatency += result.Latency
		} else {
			failedCount++
		}
	}

	r.statsMu.Lock()
	r.stats.SuccessReports += successCount
	r.stats.FailedReports += failedCount
	r.stats.BatchReports++
	r.stats.LastReportTime = time.Now()

	if successCount > 0 {
		avgLatency := totalLatency / time.Duration(successCount)
		if r.stats.SuccessReports == successCount {
			r.stats.AverageLatency = avgLatency
		} else {
			r.stats.AverageLatency = time.Duration(
				(int64(r.stats.AverageLatency)*(r.stats.SuccessReports-successCount) + int64(avgLatency)*successCount) / r.stats.SuccessReports,
			)
		}
	}
	r.statsMu.Unlock()
}

// sendData 发送单个数据
func (r *HTTPReporter) sendData(ctx context.Context, data *ReportData) (*ReportResult, error) {
	startTime := time.Now()

	// 使用默认服务器配置
	host := "127.0.0.1"
	port := 8443
	protocol := "http"
	if len(r.config.Servers) > 0 {
		host = r.config.Servers[0].IP
		port = r.config.Servers[0].Port
	}
	url := fmt.Sprintf("%s://%s:%d/api/v1/reports", protocol, host, port)

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	response, err := r.httpClient.Post(ctx, url, data, headers)
	if err != nil {
		return &ReportResult{
			Success:   false,
			Message:   err.Error(),
			Latency:   time.Since(startTime),
			Timestamp: time.Now(),
			Error:     err,
		}, err
	}

	return &ReportResult{
		Success:   true,
		Message:   "上报成功",
		Latency:   response.Latency,
		Timestamp: time.Now(),
	}, nil
}

// sendBatchData 发送批量数据
func (r *HTTPReporter) sendBatchData(ctx context.Context, batch []*ReportData) ([]*ReportResult, error) {
	// 使用默认服务器配置
	host := "127.0.0.1"
	port := 8443
	protocol := "http"
	if len(r.config.Servers) > 0 {
		host = r.config.Servers[0].IP
		port = r.config.Servers[0].Port
	}
	url := fmt.Sprintf("%s://%s:%d/api/v1/reports/batch", protocol, host, port)

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	response, err := r.httpClient.Post(ctx, url, batch, headers)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var results []*ReportResult
	if err := json.Unmarshal(response.Body, &results); err != nil {
		return nil, fmt.Errorf("解析批量上报响应失败: %w", err)
	}

	return results, nil
}

// updateSuccessStats 更新成功统计
func (r *HTTPReporter) updateSuccessStats(latency time.Duration) {
	r.statsMu.Lock()
	defer r.statsMu.Unlock()

	r.stats.TotalReports++
	r.stats.SuccessReports++
	r.stats.LastReportTime = time.Now()

	// 更新平均延迟
	if r.stats.SuccessReports == 1 {
		r.stats.AverageLatency = latency
	} else {
		r.stats.AverageLatency = time.Duration(
			(int64(r.stats.AverageLatency)*(r.stats.SuccessReports-1) + int64(latency)) / r.stats.SuccessReports,
		)
	}

	// 更新错误率
	if r.stats.TotalReports > 0 {
		r.stats.ErrorRate = float64(r.stats.FailedReports) / float64(r.stats.TotalReports) * 100
	}
}

// updateFailedStats 更新失败统计
func (r *HTTPReporter) updateFailedStats() {
	r.statsMu.Lock()
	defer r.statsMu.Unlock()

	r.stats.TotalReports++
	r.stats.FailedReports++

	// 更新错误率
	if r.stats.TotalReports > 0 {
		r.stats.ErrorRate = float64(r.stats.FailedReports) / float64(r.stats.TotalReports) * 100
	}
}

// IsHealthy 检查是否健康
func (r *HTTPReporter) IsHealthy() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.running
}

// GetStats 获取统计信息
func (r *HTTPReporter) GetStats() *ReporterStats {
	r.statsMu.RLock()
	defer r.statsMu.RUnlock()

	stats := *r.stats
	stats.QueueSize = len(r.queue)
	return &stats
}

// ReportSystemInfo 上报系统信息
func (r *HTTPReporter) ReportSystemInfo(ctx context.Context, systemInfo *monitor.SystemInfo) error {
	data := &ReportData{
		Type: ReportTypeSystem,
		Data: systemInfo,
		Metadata: map[string]interface{}{
			"version": "1.0.0",
			"source":  "system-monitor",
		},
	}

	return r.QueueReport(data)
}

// ReportHeartbeat 上报心跳
func (r *HTTPReporter) ReportHeartbeat(ctx context.Context) error {
	data := &ReportData{
		Type: ReportTypeHeartbeat,
		Data: map[string]interface{}{
			"status":    "alive",
			"timestamp": time.Now(),
			"uptime":    time.Since(time.Now()), // 这里应该是实际的运行时间
		},
	}

	return r.QueueReport(data)
}
