package websocket

import (
	"encoding/json"
	"log"
	"sync"
)

// <PERSON><PERSON> maintains the set of active clients and broadcasts messages to the clients.
type Hub struct {
	// Registered clients.
	clients map[*Client]bool

	// Inbound messages from the clients.
	broadcast chan []byte

	// Register requests from the clients.
	register chan *Client

	// Unregister requests from clients.
	unregister chan *Client

	// Mutex to protect concurrent access to clients map
	mu sync.RWMutex
}

// NewHub creates a new Hub instance.
func NewHub() *Hub {
	return &Hub{
		broadcast:  make(chan []byte),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		clients:    make(map[*Client]bool),
	}
}

// Run starts the hub's event loop.
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.mu.Lock()
			h.clients[client] = true
			h.mu.Unlock()
			log.Printf("Client registered: %s", client.conn.RemoteAddr())
		case client := <-h.unregister:
			h.mu.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
				log.Printf("Client unregistered: %s", client.conn.RemoteAddr())
			}
			h.mu.Unlock()
		case message := <-h.broadcast:
			h.mu.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
					log.Printf("Client send buffer full, unregistered: %s", client.conn.RemoteAddr())
				}
			}
			h.mu.RUnlock()
		}
	}
}

// SendMessage sends a message to all registered clients.
func (h *Hub) SendMessage(message []byte) {
	h.broadcast <- message
}

// Close closes the hub and all registered client connections.
func (h *Hub) Close() {
	h.mu.Lock()
	defer h.mu.Unlock()

	for client := range h.clients {
		close(client.send)
		client.conn.Close()
		delete(h.clients, client)
	}
	close(h.broadcast)
	close(h.register)
	close(h.unregister)
	log.Println("WebSocket Hub closed.")
}

// SendJSONMessage sends a JSON message to all registered clients.
func (h *Hub) SendJSONMessage(data interface{}) error {
	jsonMessage, err := json.Marshal(data)
	if err != nil {
		log.Printf("Error marshalling JSON message: %v", err)
		return err
	}
	h.broadcast <- jsonMessage
	return nil
}