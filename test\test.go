package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// TestCoordinator 测试协调器结构体 (Test coordinator structure)
type TestCoordinator struct {
	config      *TestConfig
	results     map[string]*TestResult
	mutex       sync.RWMutex
	startTime   time.Time
	logger      *log.Logger
	projectRoot string
}

// TestConfig 测试配置 (Test configuration)
type TestConfig struct {
	Verbose         bool          `json:"verbose"`
	Timeout         time.Duration `json:"timeout"`
	Parallel        bool          `json:"parallel"`
	SelectedModules []string      `json:"selected_modules"`
	OutputFormat    string        `json:"output_format"`
	ReportPath      string        `json:"report_path"`
}

// TestResult 测试结果 (Test result)
type TestResult struct {
	Module      string        `json:"module"`
	Status      TestStatus    `json:"status"`
	Duration    time.Duration `json:"duration"`
	Output      string        `json:"output"`
	Error       string        `json:"error"`
	StartTime   time.Time     `json:"start_time"`
	EndTime     time.Time     `json:"end_time"`
	Details     interface{}   `json:"details,omitempty"`
}

// TestStatus 测试状态 (Test status)
type TestStatus string

const (
	StatusPending TestStatus = "pending"
	StatusRunning TestStatus = "running"
	StatusPassed  TestStatus = "passed"
	StatusFailed  TestStatus = "failed"
	StatusSkipped TestStatus = "skipped"
)

// TestModule 测试模块定义 (Test module definition)
type TestModule struct {
	Name        string
	Description string
	Command     []string
	WorkDir     string
	Timeout     time.Duration
	Required    bool
}

// NewTestCoordinator 创建新的测试协调器 (Creates a new test coordinator)
func NewTestCoordinator(config *TestConfig) (*TestCoordinator, error) {
	// 获取项目根目录 (Get project root directory)
	projectRoot, err := findProjectRoot()
	if err != nil {
		return nil, fmt.Errorf("failed to find project root: %w", err)
	}

	// 创建日志器 (Create logger)
	logger := log.New(os.Stdout, "[TestCoordinator] ", log.LstdFlags|log.Lshortfile)

	return &TestCoordinator{
		config:      config,
		results:     make(map[string]*TestResult),
		projectRoot: projectRoot,
		logger:      logger,
	}, nil
}

// findProjectRoot 查找项目根目录 (Finds project root directory)
func findProjectRoot() (string, error) {
	dir, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 向上查找包含go.mod的目录 (Look up for directory containing go.mod)
	for {
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir, nil
		}
		
		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}

	return "", fmt.Errorf("go.mod not found in any parent directory")
}

// GetAvailableModules 获取可用的测试模块 (Gets available test modules)
func (tc *TestCoordinator) GetAvailableModules() []TestModule {
	return []TestModule{
		{
			Name:        "project-init",
			Description: "项目初始化和目录结构验证 (Project initialization and directory structure verification)",
			Command:     []string{"go", "mod", "verify"},
			WorkDir:     tc.projectRoot,
			Timeout:     30 * time.Second,
			Required:    true,
		},
		{
			Name:        "cli-parsing",
			Description: "基础命令行参数解析验证 (Basic CLI argument parsing verification)",
			Command:     []string{"go", "test", "./internal/cli", "-v"},
			WorkDir:     tc.projectRoot,
			Timeout:     60 * time.Second,
			Required:    true,
		},
		{
			Name:        "aes-encryption",
			Description: "AES加密模块验证 (AES encryption module verification)",
			Command:     []string{"go", "test", "./internal/crypto", "-v"},
			WorkDir:     tc.projectRoot,
			Timeout:     60 * time.Second,
			Required:    true,
		},
		{
			Name:        "communication-protocol",
			Description: "通信协议设计验证 (Communication protocol design verification)",
			Command:     []string{"go", "test", "./internal/protocol", "-v"},
			WorkDir:     tc.projectRoot,
			Timeout:     60 * time.Second,
			Required:    true,
		},
		{
			Name:        "database-model",
			Description: "数据库模型设计验证 (Database model design verification)",
			Command:     []string{"go", "test", "./internal/database", "-v"},
			WorkDir:     tc.projectRoot,
			Timeout:     60 * time.Second,
			Required:    true,
		},
		{
			Name:        "system-monitor",
			Description: "系统监控模块验证 (System monitoring module verification)",
			Command:     []string{"go", "run", "./cmd/monitor-test", "--once", "--format", "json"},
			WorkDir:     tc.projectRoot,
			Timeout:     120 * time.Second,
			Required:    true,
		},
		{
			Name:        "service-manager",
			Description: "服务管理模块验证 (Service management module verification)",
			Command:     []string{"go", "run", "./cmd/service-test", "--action", "list", "--format", "json"},
			WorkDir:     tc.projectRoot,
			Timeout:     120 * time.Second,
			Required:    true,
		},
		{
			Name:        "data-reporter",
			Description: "数据上报功能验证 (Data reporting functionality verification)",
			Command:     []string{"go", "run", "./cmd/reporter-test", "--type", "client", "--verbose"},
			WorkDir:     tc.projectRoot,
			Timeout:     120 * time.Second,
			Required:    true,
		},
		{
			Name:        "main-client",
			Description: "客户端主程序验证 (Main client program verification)",
			Command:     []string{"go", "run", "./cmd/main-client-test", "--mode", "test"},
			WorkDir:     tc.projectRoot,
			Timeout:     120 * time.Second,
			Required:    true,
		},
		{
			Name:        "comprehensive",
			Description: "综合功能测试 (Comprehensive functionality test)",
			Command:     []string{"go", "run", "./cmd/comprehensive-test", "--type", "all", "--duration", "10"},
			WorkDir:     tc.projectRoot,
			Timeout:     180 * time.Second,
			Required:    false,
		},
	}
}

// RunTests 运行测试 (Runs tests)
func (tc *TestCoordinator) RunTests(ctx context.Context) error {
	tc.startTime = time.Now()
	tc.logger.Println("开始运行测试协调器 (Starting test coordinator)")

	modules := tc.GetAvailableModules()
	
	// 过滤选定的模块 (Filter selected modules)
	if len(tc.config.SelectedModules) > 0 {
		filteredModules := make([]TestModule, 0)
		for _, module := range modules {
			for _, selected := range tc.config.SelectedModules {
				if module.Name == selected {
					filteredModules = append(filteredModules, module)
					break
				}
			}
		}
		modules = filteredModules
	}

	tc.logger.Printf("将运行 %d 个测试模块 (Will run %d test modules)", len(modules))

	// 初始化测试结果 (Initialize test results)
	for _, module := range modules {
		tc.results[module.Name] = &TestResult{
			Module: module.Name,
			Status: StatusPending,
		}
	}

	// 运行测试 (Run tests)
	if tc.config.Parallel {
		return tc.runTestsParallel(ctx, modules)
	} else {
		return tc.runTestsSequential(ctx, modules)
	}
}

// runTestsSequential 顺序运行测试 (Runs tests sequentially)
func (tc *TestCoordinator) runTestsSequential(ctx context.Context, modules []TestModule) error {
	for _, module := range modules {
		if err := tc.runSingleTest(ctx, module); err != nil {
			tc.logger.Printf("模块 %s 测试失败: %v (Module %s test failed: %v)", module.Name, err)
			if module.Required {
				return fmt.Errorf("required module %s failed: %w", module.Name, err)
			}
		}
	}
	return nil
}

// runTestsParallel 并行运行测试 (Runs tests in parallel)
func (tc *TestCoordinator) runTestsParallel(ctx context.Context, modules []TestModule) error {
	var wg sync.WaitGroup
	errChan := make(chan error, len(modules))

	for _, module := range modules {
		wg.Add(1)
		go func(m TestModule) {
			defer wg.Done()
			if err := tc.runSingleTest(ctx, m); err != nil {
				tc.logger.Printf("模块 %s 测试失败: %v (Module %s test failed: %v)", m.Name, err)
				if m.Required {
					errChan <- fmt.Errorf("required module %s failed: %w", m.Name, err)
				}
			}
		}(module)
	}

	wg.Wait()
	close(errChan)

	// 检查是否有必需模块失败 (Check if any required modules failed)
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}

// runSingleTest 运行单个测试 (Runs a single test)
func (tc *TestCoordinator) runSingleTest(ctx context.Context, module TestModule) error {
	tc.mutex.Lock()
	result := tc.results[module.Name]
	result.Status = StatusRunning
	result.StartTime = time.Now()
	tc.mutex.Unlock()

	tc.logger.Printf("开始运行模块: %s (Starting module: %s)", module.Name)

	// 创建带超时的上下文 (Create context with timeout)
	testCtx, cancel := context.WithTimeout(ctx, module.Timeout)
	defer cancel()

	// 执行命令 (Execute command)
	cmd := exec.CommandContext(testCtx, module.Command[0], module.Command[1:]...)
	cmd.Dir = module.WorkDir
	
	output, err := cmd.CombinedOutput()

	tc.mutex.Lock()
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.Output = string(output)
	
	if err != nil {
		result.Status = StatusFailed
		result.Error = err.Error()
	} else {
		result.Status = StatusPassed
	}
	tc.mutex.Unlock()

	if tc.config.Verbose {
		tc.logger.Printf("模块 %s 完成，状态: %s，耗时: %v (Module %s completed, status: %s, duration: %v)", 
			module.Name, result.Status, result.Duration)
		if result.Output != "" {
			tc.logger.Printf("输出 (Output): %s", result.Output)
		}
		if result.Error != "" {
			tc.logger.Printf("错误 (Error): %s", result.Error)
		}
	}

	return err
}

// GetResults 获取测试结果 (Gets test results)
func (tc *TestCoordinator) GetResults() map[string]*TestResult {
	tc.mutex.RLock()
	defer tc.mutex.RUnlock()
	
	results := make(map[string]*TestResult)
	for k, v := range tc.results {
		results[k] = v
	}
	return results
}

// GenerateReport 生成测试报告 (Generates test report)
func (tc *TestCoordinator) GenerateReport() (*TestReport, error) {
	results := tc.GetResults()
	
	report := &TestReport{
		StartTime:    tc.startTime,
		EndTime:      time.Now(),
		TotalModules: len(results),
		Results:      results,
	}
	
	// 统计结果 (Count results)
	for _, result := range results {
		switch result.Status {
		case StatusPassed:
			report.PassedCount++
		case StatusFailed:
			report.FailedCount++
		case StatusSkipped:
			report.SkippedCount++
		}
		report.TotalDuration += result.Duration
	}
	
	report.SuccessRate = float64(report.PassedCount) / float64(report.TotalModules) * 100
	
	return report, nil
}

// TestReport 测试报告 (Test report)
type TestReport struct {
	StartTime     time.Time                `json:"start_time"`
	EndTime       time.Time                `json:"end_time"`
	TotalDuration time.Duration            `json:"total_duration"`
	TotalModules  int                      `json:"total_modules"`
	PassedCount   int                      `json:"passed_count"`
	FailedCount   int                      `json:"failed_count"`
	SkippedCount  int                      `json:"skipped_count"`
	SuccessRate   float64                  `json:"success_rate"`
	Results       map[string]*TestResult   `json:"results"`
}

// SaveReport 保存测试报告 (Saves test report)
func (tc *TestCoordinator) SaveReport(report *TestReport) error {
	if tc.config.ReportPath == "" {
		tc.config.ReportPath = filepath.Join(tc.projectRoot, "test", "report.json")
	}

	// 确保目录存在 (Ensure directory exists)
	if err := os.MkdirAll(filepath.Dir(tc.config.ReportPath), 0755); err != nil {
		return fmt.Errorf("failed to create report directory: %w", err)
	}

	// 保存JSON报告 (Save JSON report)
	data, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal report: %w", err)
	}

	if err := os.WriteFile(tc.config.ReportPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write report file: %w", err)
	}

	tc.logger.Printf("测试报告已保存到: %s (Test report saved to: %s)", tc.config.ReportPath)
	return nil
}

// PrintSummary 打印测试摘要 (Prints test summary)
func (tc *TestCoordinator) PrintSummary(report *TestReport) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("🧪 测试协调器执行摘要 (Test Coordinator Execution Summary)")
	fmt.Println(strings.Repeat("=", 60))
	
	fmt.Printf("⏱️  执行时间 (Execution Time): %v\n", report.EndTime.Sub(report.StartTime))
	fmt.Printf("📊 总模块数 (Total Modules): %d\n", report.TotalModules)
	fmt.Printf("✅ 通过数量 (Passed): %d\n", report.PassedCount)
	fmt.Printf("❌ 失败数量 (Failed): %d\n", report.FailedCount)
	fmt.Printf("⏭️  跳过数量 (Skipped): %d\n", report.SkippedCount)
	fmt.Printf("📈 成功率 (Success Rate): %.1f%%\n", report.SuccessRate)
	
	fmt.Println("\n📋 模块详情 (Module Details):")
	fmt.Println(strings.Repeat("-", 60))
	
	for _, module := range tc.GetAvailableModules() {
		if result, exists := report.Results[module.Name]; exists {
			status := getStatusIcon(result.Status)
			fmt.Printf("%s %-20s %8v %s\n", 
				status, 
				module.Name, 
				result.Duration.Truncate(time.Millisecond),
				module.Description)
		}
	}
	
	fmt.Println(strings.Repeat("=", 60))
	
	if report.FailedCount > 0 {
		fmt.Println("\n❌ 失败模块详情 (Failed Module Details):")
		for name, result := range report.Results {
			if result.Status == StatusFailed {
				fmt.Printf("  • %s: %s\n", name, result.Error)
			}
		}
	}
}

// getStatusIcon 获取状态图标 (Gets status icon)
func getStatusIcon(status TestStatus) string {
	switch status {
	case StatusPassed:
		return "✅"
	case StatusFailed:
		return "❌"
	case StatusSkipped:
		return "⏭️ "
	case StatusRunning:
		return "🔄"
	default:
		return "⏸️ "
	}
}

func main() {
	// 解析命令行参数 (Parse command line arguments)
	var (
		verbose   = flag.Bool("verbose", false, "详细输出 (Verbose output)")
		timeout   = flag.Duration("timeout", 10*time.Minute, "总超时时间 (Total timeout)")
		parallel  = flag.Bool("parallel", false, "并行运行测试 (Run tests in parallel)")
		modules   = flag.String("modules", "", "指定运行的模块，逗号分隔 (Specify modules to run, comma separated)")
		format    = flag.String("format", "text", "输出格式: text, json (Output format: text, json)")
		reportPath = flag.String("report", "", "报告保存路径 (Report save path)")
	)
	flag.Parse()

	// 解析选定的模块 (Parse selected modules)
	var selectedModules []string
	if *modules != "" {
		selectedModules = strings.Split(*modules, ",")
		for i, module := range selectedModules {
			selectedModules[i] = strings.TrimSpace(module)
		}
	}

	// 创建测试配置 (Create test configuration)
	config := &TestConfig{
		Verbose:         *verbose,
		Timeout:         *timeout,
		Parallel:        *parallel,
		SelectedModules: selectedModules,
		OutputFormat:    *format,
		ReportPath:      *reportPath,
	}

	// 创建测试协调器 (Create test coordinator)
	coordinator, err := NewTestCoordinator(config)
	if err != nil {
		log.Fatalf("创建测试协调器失败 (Failed to create test coordinator): %v", err)
	}

	// 创建上下文 (Create context)
	ctx, cancel := context.WithTimeout(context.Background(), config.Timeout)
	defer cancel()

	// 运行测试 (Run tests)
	if err := coordinator.RunTests(ctx); err != nil {
		log.Printf("测试执行失败 (Test execution failed): %v", err)
	}

	// 生成报告 (Generate report)
	report, err := coordinator.GenerateReport()
	if err != nil {
		log.Fatalf("生成报告失败 (Failed to generate report): %v", err)
	}

	// 保存报告 (Save report)
	if err := coordinator.SaveReport(report); err != nil {
		log.Printf("保存报告失败 (Failed to save report): %v", err)
	}

	// 打印摘要 (Print summary)
	if config.OutputFormat == "text" {
		coordinator.PrintSummary(report)
	} else if config.OutputFormat == "json" {
		data, _ := json.MarshalIndent(report, "", "  ")
		fmt.Println(string(data))
	}

	// 设置退出码 (Set exit code)
	if report.FailedCount > 0 {
		os.Exit(1)
	}
}
