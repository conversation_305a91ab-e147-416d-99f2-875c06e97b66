package manager

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// SystemdManager systemd服务管理器
type SystemdManager struct{}

// NewSystemdManager 创建systemd管理器
func NewSystemdManager() *SystemdManager {
	return &SystemdManager{}
}

// ListServices 列出所有systemd服务
func (s *SystemdManager) ListServices(ctx context.Context) ([]*Service, error) {
	// 获取所有服务列表
	output, err := executeCommand(ctx, "systemctl", "list-units", "--type=service", "--all", "--no-pager", "--plain")
	if err != nil {
		return nil, fmt.Errorf("获取systemd服务列表失败: %w", err)
	}

	var services []*Service
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		
		fields := strings.Fields(line)
		if len(fields) < 4 {
			continue
		}
		
		serviceName := fields[0]
		if !strings.HasSuffix(serviceName, ".service") {
			continue
		}
		
		// 移除.service后缀
		serviceName = strings.TrimSuffix(serviceName, ".service")
		
		service := &Service{
			Name:        serviceName,
			DisplayName: serviceName,
			Status:      parseServiceStatus(fields[2]),
			Type:        "systemd",
			Description: strings.Join(fields[4:], " "),
		}
		
		// 获取详细信息
		s.enrichServiceInfo(ctx, service)
		
		services = append(services, service)
	}

	return services, nil
}

// GetService 获取指定服务的详细信息
func (s *SystemdManager) GetService(ctx context.Context, name string) (*Service, error) {
	if !s.ServiceExists(ctx, name) {
		return nil, fmt.Errorf("服务不存在: %s", name)
	}

	service := &Service{
		Name:        name,
		DisplayName: name,
		Type:        "systemd",
	}

	// 获取服务状态
	if err := s.enrichServiceInfo(ctx, service); err != nil {
		return nil, err
	}

	return service, nil
}

// enrichServiceInfo 丰富服务信息
func (s *SystemdManager) enrichServiceInfo(ctx context.Context, service *Service) error {
	serviceName := service.Name + ".service"
	
	// 获取服务状态
	statusOutput, err := executeCommand(ctx, "systemctl", "show", serviceName, "--property=ActiveState,SubState,MainPID,ExecMainStartTimestamp,Description")
	if err != nil {
		return err
	}

	properties := make(map[string]string)
	for _, line := range strings.Split(statusOutput, "\n") {
		if strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) == 2 {
				properties[parts[0]] = parts[1]
			}
		}
	}

	// 解析状态
	if activeState, ok := properties["ActiveState"]; ok {
		service.Status = parseServiceStatus(activeState)
	}

	// 解析PID
	if pidStr, ok := properties["MainPID"]; ok {
		if pid, err := strconv.Atoi(pidStr); err == nil && pid > 0 {
			service.PID = pid
		}
	}

	// 解析启动时间
	if startTimeStr, ok := properties["ExecMainStartTimestamp"]; ok && startTimeStr != "" {
		if startTime, err := time.Parse("Mon 2006-01-02 15:04:05 MST", startTimeStr); err == nil {
			service.StartTime = &startTime
		}
	}

	// 设置描述
	if desc, ok := properties["Description"]; ok {
		service.Description = desc
	}

	return nil
}

// StartService 启动服务
func (s *SystemdManager) StartService(ctx context.Context, name string) error {
	serviceName := name + ".service"
	_, err := executeCommand(ctx, "systemctl", "start", serviceName)
	if err != nil {
		return fmt.Errorf("启动服务失败: %w", err)
	}
	return nil
}

// StopService 停止服务
func (s *SystemdManager) StopService(ctx context.Context, name string) error {
	serviceName := name + ".service"
	_, err := executeCommand(ctx, "systemctl", "stop", serviceName)
	if err != nil {
		return fmt.Errorf("停止服务失败: %w", err)
	}
	return nil
}

// RestartService 重启服务
func (s *SystemdManager) RestartService(ctx context.Context, name string) error {
	serviceName := name + ".service"
	_, err := executeCommand(ctx, "systemctl", "restart", serviceName)
	if err != nil {
		return fmt.Errorf("重启服务失败: %w", err)
	}
	return nil
}

// EnableService 启用服务
func (s *SystemdManager) EnableService(ctx context.Context, name string) error {
	serviceName := name + ".service"
	_, err := executeCommand(ctx, "systemctl", "enable", serviceName)
	if err != nil {
		return fmt.Errorf("启用服务失败: %w", err)
	}
	return nil
}

// DisableService 禁用服务
func (s *SystemdManager) DisableService(ctx context.Context, name string) error {
	serviceName := name + ".service"
	_, err := executeCommand(ctx, "systemctl", "disable", serviceName)
	if err != nil {
		return fmt.Errorf("禁用服务失败: %w", err)
	}
	return nil
}

// ServiceExists 检查服务是否存在
func (s *SystemdManager) ServiceExists(ctx context.Context, name string) bool {
	serviceName := name + ".service"
	_, err := executeCommand(ctx, "systemctl", "cat", serviceName)
	return err == nil
}

// GetServiceLogs 获取服务日志
func (s *SystemdManager) GetServiceLogs(ctx context.Context, name string, lines int) ([]string, error) {
	serviceName := name + ".service"
	
	args := []string{"journalctl", "-u", serviceName, "--no-pager", "-n", strconv.Itoa(lines)}
	output, err := executeCommand(ctx, args[0], args[1:]...)
	if err != nil {
		return nil, fmt.Errorf("获取服务日志失败: %w", err)
	}

	logLines := strings.Split(output, "\n")
	
	// 过滤空行
	var result []string
	for _, line := range logLines {
		if strings.TrimSpace(line) != "" {
			result = append(result, line)
		}
	}

	return result, nil
}

// GetServiceStatus 获取服务状态详情
func (s *SystemdManager) GetServiceStatus(ctx context.Context, name string) (map[string]interface{}, error) {
	serviceName := name + ".service"
	
	output, err := executeCommand(ctx, "systemctl", "status", serviceName, "--no-pager", "-l")
	if err != nil {
		return nil, fmt.Errorf("获取服务状态失败: %w", err)
	}

	status := make(map[string]interface{})
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, ":") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])
				status[key] = value
			}
		}
	}

	return status, nil
}

// ReloadService 重新加载服务配置
func (s *SystemdManager) ReloadService(ctx context.Context, name string) error {
	serviceName := name + ".service"
	_, err := executeCommand(ctx, "systemctl", "reload", serviceName)
	if err != nil {
		return fmt.Errorf("重新加载服务失败: %w", err)
	}
	return nil
}

// ReloadDaemon 重新加载systemd配置
func (s *SystemdManager) ReloadDaemon(ctx context.Context) error {
	_, err := executeCommand(ctx, "systemctl", "daemon-reload")
	if err != nil {
		return fmt.Errorf("重新加载systemd配置失败: %w", err)
	}
	return nil
}
