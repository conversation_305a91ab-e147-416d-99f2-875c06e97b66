package crypto

import (
	"fmt"
	"sync"
	"time"
)

// KeyVersion 密钥版本类型 (Key version type)
type KeyVersion int

// Key 密钥结构 (Key structure)
type Key struct {
	Version   KeyVersion `json:"version"`    // 密钥版本 (Key version)
	Data      []byte     `json:"-"`          // 密钥数据，不序列化 (Key data, not serialized)
	Salt      []byte     `json:"salt"`       // 盐值 (Salt)
	CreatedAt time.Time  `json:"created_at"` // 创建时间 (Creation time)
	ExpiresAt time.Time  `json:"expires_at"` // 过期时间 (Expiration time)
	Active    bool       `json:"active"`     // 是否激活 (Whether active)
}

// IsExpired 检查密钥是否过期 (Checks if key is expired)
func (k *Key) IsExpired() bool {
	return time.Now().After(k.ExpiresAt)
}

// IsValid 检查密钥是否有效 (Checks if key is valid)
func (k *Key) IsValid() bool {
	return k.Active && !k.IsExpired() && len(k.Data) == keyLen
}

// KeyManagerConfig 密钥管理器配置 (Key manager configuration)
type KeyManagerConfig struct {
	RotationInterval time.Duration // 轮换间隔 (Rotation interval)
	KeyTTL           time.Duration // 密钥生存时间 (Key time to live)
	MaxKeyHistory    int           // 最大密钥历史数量 (Maximum key history count)
	AutoRotate       bool          // 是否自动轮换 (Whether to auto rotate)
	MasterPassword   []byte        // 主密码 (Master password)
}

// DefaultKeyManagerConfig 返回默认配置 (Returns default configuration)
func DefaultKeyManagerConfig() *KeyManagerConfig {
	return &KeyManagerConfig{
		RotationInterval: 24 * time.Hour, // 每24小时轮换一次 (Rotate every 24 hours)
		KeyTTL:           72 * time.Hour, // 密钥保留72小时 (Keep keys for 72 hours)
		MaxKeyHistory:    10,             // 最多保留10个历史密钥 (Keep at most 10 historical keys)
		AutoRotate:       true,           // 默认启用自动轮换 (Auto rotate enabled by default)
	}
}

// KeyManager 密钥管理器 (Key manager)
type KeyManager struct {
	config       *KeyManagerConfig
	currentKey   *Key
	previousKeys map[KeyVersion]*Key
	nextVersion  KeyVersion
	mutex        sync.RWMutex
	stopCh       chan struct{}
	stopped      bool
}

// NewKeyManager 创建新的密钥管理器 (Creates a new key manager)
func NewKeyManager(config *KeyManagerConfig) (*KeyManager, error) {
	if config == nil {
		config = DefaultKeyManagerConfig()
	}

	if len(config.MasterPassword) == 0 {
		return nil, fmt.Errorf("master password is required")
	}

	km := &KeyManager{
		config:       config,
		previousKeys: make(map[KeyVersion]*Key),
		nextVersion:  1,
		stopCh:       make(chan struct{}),
	}

	// 生成初始密钥 (Generate initial key)
	if err := km.generateInitialKey(); err != nil {
		return nil, fmt.Errorf("failed to generate initial key: %w", err)
	}

	// 启动自动轮换 (Start auto rotation)
	if config.AutoRotate {
		go km.autoRotationLoop()
	}

	return km, nil
}

// generateInitialKey 生成初始密钥 (Generates initial key)
func (km *KeyManager) generateInitialKey() error {
	key, err := km.generateNewKey()
	if err != nil {
		return err
	}

	km.currentKey = key
	return nil
}

// generateNewKey 生成新密钥 (Generates new key)
func (km *KeyManager) generateNewKey() (*Key, error) {
	// 生成新的盐值 (Generate new salt)
	salt, err := GenerateSalt()
	if err != nil {
		return nil, fmt.Errorf("failed to generate salt: %w", err)
	}

	// 使用主密码和盐值派生密钥 (Derive key using master password and salt)
	keyData := DeriveKey(km.config.MasterPassword, salt)

	now := time.Now()
	key := &Key{
		Version:   km.nextVersion,
		Data:      keyData,
		Salt:      salt,
		CreatedAt: now,
		ExpiresAt: now.Add(km.config.KeyTTL),
		Active:    true,
	}

	km.nextVersion++
	return key, nil
}

// RotateKey 手动轮换密钥 (Manually rotates key)
func (km *KeyManager) RotateKey() error {
	km.mutex.Lock()
	defer km.mutex.Unlock()

	if km.stopped {
		return fmt.Errorf("key manager has been stopped")
	}

	// 生成新密钥 (Generate new key)
	newKey, err := km.generateNewKey()
	if err != nil {
		return fmt.Errorf("failed to generate new key: %w", err)
	}

	// 将当前密钥移到历史记录 (Move current key to history)
	if km.currentKey != nil {
		km.currentKey.Active = false
		km.previousKeys[km.currentKey.Version] = km.currentKey
	}

	// 设置新密钥为当前密钥 (Set new key as current)
	km.currentKey = newKey

	// 清理过期的历史密钥 (Clean up expired historical keys)
	km.cleanupExpiredKeys()

	return nil
}

// GetCurrentKey 获取当前密钥 (Gets current key)
func (km *KeyManager) GetCurrentKey() *Key {
	km.mutex.RLock()
	defer km.mutex.RUnlock()

	if km.currentKey != nil && km.currentKey.IsValid() {
		return km.currentKey
	}
	return nil
}

// GetKeyForDecryption 获取用于解密的密钥 (Gets key for decryption)
func (km *KeyManager) GetKeyForDecryption(version KeyVersion) *Key {
	km.mutex.RLock()
	defer km.mutex.RUnlock()

	// 检查是否是当前密钥 (Check if it's the current key)
	if km.currentKey != nil && km.currentKey.Version == version {
		return km.currentKey
	}

	// 检查历史密钥 (Check historical keys)
	if key, exists := km.previousKeys[version]; exists && !key.IsExpired() {
		return key
	}

	return nil
}

// GetKeyData 获取当前密钥数据 (Gets current key data)
func (km *KeyManager) GetKeyData() []byte {
	key := km.GetCurrentKey()
	if key == nil {
		return nil
	}
	return key.Data
}

// GetKeyVersion 获取当前密钥版本 (Gets current key version)
func (km *KeyManager) GetKeyVersion() KeyVersion {
	km.mutex.RLock()
	defer km.mutex.RUnlock()

	if km.currentKey != nil {
		return km.currentKey.Version
	}
	return 0
}

// GetKeyStats 获取密钥统计信息 (Gets key statistics)
func (km *KeyManager) GetKeyStats() map[string]interface{} {
	km.mutex.RLock()
	defer km.mutex.RUnlock()

	stats := make(map[string]interface{})

	if km.currentKey != nil {
		stats["current_key_version"] = km.currentKey.Version
		stats["current_key_created_at"] = km.currentKey.CreatedAt
		stats["current_key_expires_at"] = km.currentKey.ExpiresAt
		stats["current_key_valid"] = km.currentKey.IsValid()
	}

	stats["historical_keys_count"] = len(km.previousKeys)
	stats["next_version"] = km.nextVersion
	stats["auto_rotate_enabled"] = km.config.AutoRotate
	stats["rotation_interval"] = km.config.RotationInterval.String()

	// 统计有效的历史密钥 (Count valid historical keys)
	validHistoricalKeys := 0
	for _, key := range km.previousKeys {
		if !key.IsExpired() {
			validHistoricalKeys++
		}
	}
	stats["valid_historical_keys"] = validHistoricalKeys

	return stats
}

// cleanupExpiredKeys 清理过期的密钥 (Cleans up expired keys)
func (km *KeyManager) cleanupExpiredKeys() {
	// 清理过期的历史密钥 (Clean up expired historical keys)
	for version, key := range km.previousKeys {
		if key.IsExpired() {
			delete(km.previousKeys, version)
		}
	}

	// 如果历史密钥数量超过限制，删除最旧的 (If historical keys exceed limit, delete oldest)
	if len(km.previousKeys) > km.config.MaxKeyHistory {
		// 找到最旧的密钥 (Find oldest keys)
		oldestVersions := make([]KeyVersion, 0, len(km.previousKeys))
		for version := range km.previousKeys {
			oldestVersions = append(oldestVersions, version)
		}

		// 按版本号排序（版本号越小越旧）(Sort by version number, smaller is older)
		for i := 0; i < len(oldestVersions)-1; i++ {
			for j := i + 1; j < len(oldestVersions); j++ {
				if oldestVersions[i] > oldestVersions[j] {
					oldestVersions[i], oldestVersions[j] = oldestVersions[j], oldestVersions[i]
				}
			}
		}

		// 删除超出限制的最旧密钥 (Delete oldest keys that exceed limit)
		for i := 0; i < len(oldestVersions)-km.config.MaxKeyHistory; i++ {
			delete(km.previousKeys, oldestVersions[i])
		}
	}
}

// autoRotationLoop 自动轮换循环 (Auto rotation loop)
func (km *KeyManager) autoRotationLoop() {
	ticker := time.NewTicker(km.config.RotationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := km.RotateKey(); err != nil {
				// 在生产环境中，这里应该记录到日志系统 (In production, this should be logged)
				fmt.Printf("Auto key rotation failed: %v\n", err)
			}
		case <-km.stopCh:
			return
		}
	}
}

// Stop 停止密钥管理器 (Stops the key manager)
func (km *KeyManager) Stop() {
	km.mutex.Lock()
	defer km.mutex.Unlock()

	if !km.stopped {
		km.stopped = true
		close(km.stopCh)
	}
}

// UpdateConfig 更新配置 (Updates configuration)
func (km *KeyManager) UpdateConfig(config *KeyManagerConfig) error {
	km.mutex.Lock()
	defer km.mutex.Unlock()

	if km.stopped {
		return fmt.Errorf("key manager has been stopped")
	}

	km.config = config
	return nil
}
