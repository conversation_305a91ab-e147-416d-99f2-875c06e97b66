package aes

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"

	"golang.org/x/crypto/pbkdf2"
)

const (
	saltSize    = 16
	nonceSize   = 12 // GCM nonce size
	keySize     = 32 // AES-256
	iter        = 10000
	messageType = "aes-gcm-v1"
)

// EncryptedMessage represents the structure of an encrypted message.
type EncryptedMessage struct {
	Type    string `json:"type"`
	Salt    []byte `json:"salt"`
	Nonce   []byte `json:"nonce"`
	Payload []byte `json:"payload"` // Ciphertext + GCM tag
}

// NewKey derives a cryptographic key from a password and salt using PBKDF2.
func NewKey(password string, salt []byte) []byte {
	return pbkdf2.Key([]byte(password), salt, iter, keySize, sha256.New)
}

// Encrypt encrypts plaintext using AES-256-GCM.
// It derives a key from the password, generates a random salt and nonce,
// and returns the encrypted message in a structured format.
func Encrypt(plaintext []byte, password string) ([]byte, error) {
	salt := make([]byte, saltSize)
	if _, err := io.ReadFull(rand.Reader, salt); err != nil {
		return nil, fmt.Errorf("failed to generate salt: %w", err)
	}

	key := NewKey(password, salt)

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	nonce := make([]byte, nonceSize)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

	msg := EncryptedMessage{
		Type:    messageType,
		Salt:    salt,
		Nonce:   nonce,
		Payload: ciphertext,
	}

	jsonMsg, err := json.Marshal(msg)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal encrypted message: %w", err)
	}

	return jsonMsg, nil
}

// Decrypt decrypts an AES-256-GCM encrypted message.
// It uses the provided password to derive the key and decrypt the payload.
func Decrypt(encryptedMessage []byte, password string) ([]byte, error) {
	var msg EncryptedMessage
	if err := json.Unmarshal(encryptedMessage, &msg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal encrypted message: %w", err)
	}

	if msg.Type != messageType {
		return nil, fmt.Errorf("unsupported message type: %s", msg.Type)
	}

	key := NewKey(password, msg.Salt)

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	plaintext, err := gcm.Open(nil, msg.Nonce, msg.Payload, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %w", err)
	}

	return plaintext, nil
}