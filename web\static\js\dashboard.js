document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard script loaded.');

    // 示例：模拟数据加载和更新
    function updateDashboardData() {
        // 可以在这里发起API请求获取实时数据
        console.log('Updating dashboard data...');
        // 假设这里更新了一些DOM元素
        const serverStatusElement = document.querySelector('.server-status');
        if (serverStatusElement) {
            serverStatusElement.textContent = '在线'; // 模拟状态更新
        }
    }

    // 每隔一段时间更新数据
    setInterval(updateDashboardData, 5000); // 每5秒更新一次

    // 初始加载
    updateDashboardData();
});