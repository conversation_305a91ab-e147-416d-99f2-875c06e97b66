package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"time"

	"server-monitor/internal/cache"
	"server-monitor/internal/logger"
	"server-monitor/internal/manager"
	"server-monitor/internal/pool"
	"server-monitor/internal/utils"
)

func main() {
	var (
		testType = flag.String("type", "all", "测试类型: all, cache, pool, service, logger, utils")
		duration = flag.Int("duration", 10, "测试持续时间(秒)")
	)
	flag.Parse()

	fmt.Printf("综合功能测试程序\n")
	fmt.Printf("测试类型: %s\n", *testType)
	fmt.Printf("测试时长: %d秒\n", *duration)
	fmt.Println("---")

	switch *testType {
	case "cache":
		testCache()
	case "pool":
		testConnectionPool()
	case "service":
		testServiceManager()
	case "logger":
		testLogger()
	case "utils":
		testUtils()
	case "all":
		testAll()
	default:
		log.Fatalf("未知测试类型: %s", *testType)
	}
}

// testAll 测试所有功能
func testAll() {
	fmt.Println("=== 综合功能测试 ===")
	
	testLogger()
	fmt.Println()
	
	testUtils()
	fmt.Println()
	
	testCache()
	fmt.Println()
	
	testConnectionPool()
	fmt.Println()
	
	testServiceManager()
	
	fmt.Println("\n✅ 所有功能测试完成！")
}

// testLogger 测试日志功能
func testLogger() {
	fmt.Println("=== 日志系统测试 ===")
	
	// 初始化日志器
	config := &logger.Config{
		Level:      logger.LevelDebug,
		Format:     "text",
		Output:     "stdout",
		File:       "./logs/test.log",
		MaxSize:    10,
		MaxBackups: 3,
		MaxAge:     7,
		Compress:   true,
	}
	
	if err := logger.InitLogger(config); err != nil {
		log.Printf("初始化日志器失败: %v", err)
		return
	}
	
	// 测试各种日志级别
	logger.Debug("这是一条调试日志")
	logger.Info("这是一条信息日志")
	logger.Warn("这是一条警告日志")
	logger.Error("这是一条错误日志")
	
	fmt.Println("✅ 日志系统测试完成")
}

// testUtils 测试工具函数
func testUtils() {
	fmt.Println("=== 工具函数测试 ===")
	
	// 字符串工具
	fmt.Printf("字符串截断: %s\n", utils.TruncateString("这是一个很长的字符串", 10))
	fmt.Printf("驼峰转下划线: %s\n", utils.CamelToSnake("CamelCaseString"))
	fmt.Printf("下划线转驼峰: %s\n", utils.SnakeToCamel("snake_case_string"))
	
	// 时间工具
	duration, _ := utils.ParseDuration("1小时30分钟")
	fmt.Printf("解析时间间隔: %v\n", duration)
	fmt.Printf("格式化时间间隔: %s\n", utils.FormatDuration(duration))
	fmt.Printf("相对时间: %s\n", utils.TimeAgo(time.Now().Add(-2*time.Hour)))
	
	// 文件工具
	fmt.Printf("格式化文件大小: %s\n", utils.FormatFileSize(1024*1024*1024))
	fmt.Printf("主机名: %s\n", utils.GetHostname())
	fmt.Printf("操作系统: %s\n", utils.GetOS())
	
	// 网络工具
	localIP, _ := utils.GetLocalIP()
	fmt.Printf("本地IP: %s\n", localIP)
	fmt.Printf("端口检查: %v\n", utils.IsPortOpen("127.0.0.1", 80))
	
	// 验证工具
	fmt.Printf("邮箱验证: %v\n", utils.IsValidEmail("<EMAIL>"))
	fmt.Printf("数字验证: %v\n", utils.IsNumeric("123.45"))
	
	fmt.Println("✅ 工具函数测试完成")
}

// testCache 测试缓存功能
func testCache() {
	fmt.Println("=== 缓存系统测试 ===")
	
	// 创建缓存管理器
	cacheManager := cache.NewManager(time.Minute)
	defer cacheManager.Stop()
	
	// 测试基本操作
	cacheManager.Set("key1", "value1", 5*time.Minute)
	cacheManager.Set("key2", 42, 5*time.Minute)
	cacheManager.Set("key3", true, 5*time.Minute)
	
	// 测试获取
	if value, found := cacheManager.Get("key1"); found {
		fmt.Printf("获取字符串: %v\n", value)
	}
	
	if value, found := cacheManager.GetInt("key2"); found {
		fmt.Printf("获取整数: %d\n", value)
	}
	
	// 测试JSON
	testData := map[string]interface{}{
		"name": "测试",
		"age":  25,
	}
	cacheManager.SetJSON("json_key", testData, 5*time.Minute)
	
	var retrievedData map[string]interface{}
	if cacheManager.GetJSON("json_key", &retrievedData) {
		fmt.Printf("获取JSON: %+v\n", retrievedData)
	}
	
	// 测试计数器
	count, _ := cacheManager.Increment("counter", 1)
	fmt.Printf("计数器: %d\n", count)
	
	// 获取统计信息
	stats := cacheManager.GetStats()
	fmt.Printf("缓存统计: %+v\n", stats)
	
	fmt.Println("✅ 缓存系统测试完成")
}

// testConnectionPool 测试连接池
func testConnectionPool() {
	fmt.Println("=== 连接池测试 ===")
	
	// 创建模拟连接工厂
	factory := func() (pool.Connection, error) {
		return &MockConnection{
			id:      time.Now().UnixNano(),
			created: time.Now(),
		}, nil
	}
	
	// 创建连接池
	config := &pool.Config{
		MaxIdle:     5,
		MaxActive:   10,
		IdleTimeout: time.Minute,
		TestOnGet:   true,
	}
	
	connPool := pool.NewPool(factory, config)
	defer connPool.Close()
	
	ctx := context.Background()
	
	// 测试获取和归还连接
	conn1, err := connPool.Get(ctx)
	if err != nil {
		log.Printf("获取连接失败: %v", err)
		return
	}
	fmt.Printf("获取连接1: %v\n", conn1.(*MockConnection).id)
	
	conn2, err := connPool.Get(ctx)
	if err != nil {
		log.Printf("获取连接失败: %v", err)
		return
	}
	fmt.Printf("获取连接2: %v\n", conn2.(*MockConnection).id)
	
	// 归还连接
	connPool.Put(conn1)
	connPool.Put(conn2)
	
	// 获取统计信息
	stats := connPool.Stats()
	fmt.Printf("连接池统计: %+v\n", stats)
	
	fmt.Println("✅ 连接池测试完成")
}

// testServiceManager 测试服务管理
func testServiceManager() {
	fmt.Println("=== 服务管理测试 ===")
	
	// 创建服务管理器
	serviceManager := manager.NewManager()
	
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	// 列出服务（限制数量避免输出过多）
	services, err := serviceManager.ListServices(ctx)
	if err != nil {
		log.Printf("获取服务列表失败: %v", err)
		return
	}
	
	fmt.Printf("发现 %d 个服务\n", len(services))
	
	// 显示前5个服务
	count := 5
	if len(services) < count {
		count = len(services)
	}
	
	for i := 0; i < count; i++ {
		service := services[i]
		fmt.Printf("服务 %d: %s (%s) - %s\n", 
			i+1, service.Name, service.Type, service.Status)
	}
	
	fmt.Println("✅ 服务管理测试完成")
}

// MockConnection 模拟连接
type MockConnection struct {
	id      int64
	created time.Time
	closed  bool
}

func (c *MockConnection) Close() error {
	c.closed = true
	return nil
}

func (c *MockConnection) IsAlive() bool {
	return !c.closed
}

func (c *MockConnection) LastUsed() time.Time {
	return c.created
}

func (c *MockConnection) SetLastUsed(t time.Time) {
	// Mock implementation
}
