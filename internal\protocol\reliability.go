package protocol

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"server-monitor/internal/reporter"
)

// SimpleLogger 简单的logger实现 (Simple logger implementation)
type SimpleLogger struct{}

// Debug 调试日志 (Debug log)
func (l *SimpleLogger) Debug(format string, args ...interface{}) {
	log.Printf("[DEBUG] "+format, args...)
}

// Info 信息日志 (Info log)
func (l *SimpleLogger) Info(format string, args ...interface{}) {
	log.Printf("[INFO] "+format, args...)
}

// Error 错误日志 (Error log)
func (l *SimpleLogger) Error(format string, args ...interface{}) {
	log.Printf("[ERROR] "+format, args...)
}

// SimpleRetryer 简单的重试器实现 (Simple retryer implementation)
type SimpleRetryer struct {
	config *reporter.RetryConfig
}

// Execute 执行重试逻辑 (Executes retry logic)
func (r *SimpleRetryer) Execute(ctx context.Context, fn func(context.Context, int) error) *reporter.RetryResult {
	startTime := time.Now()
	result := &reporter.RetryResult{}

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		result.Attempts = attempt

		err := fn(ctx, attempt)
		if err == nil {
			result.Success = true
			result.TotalTime = time.Since(startTime)
			return result
		}

		result.LastError = err

		if attempt >= r.config.MaxAttempts {
			break
		}

		// 计算延迟 (Calculate delay)
		delay := r.config.InitialDelay
		for i := 1; i < attempt; i++ {
			delay = time.Duration(float64(delay) * r.config.Multiplier)
			if delay > r.config.MaxDelay {
				delay = r.config.MaxDelay
				break
			}
		}

		// 等待重试 (Wait for retry)
		select {
		case <-time.After(delay):
		case <-ctx.Done():
			result.LastError = ctx.Err()
			result.TotalTime = time.Since(startTime)
			return result
		}
	}

	result.TotalTime = time.Since(startTime)
	return result
}

// GetStats 获取统计信息 (Gets statistics)
func (r *SimpleRetryer) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"max_attempts":  r.config.MaxAttempts,
		"initial_delay": r.config.InitialDelay.String(),
		"max_delay":     r.config.MaxDelay.String(),
		"multiplier":    r.config.Multiplier,
	}
}

// createRetryer 创建重试器，处理logger兼容性问题 (Creates retryer, handles logger compatibility issues)
func createRetryer(config *reporter.RetryConfig) Retryer {
	return &SimpleRetryer{config: config}
}

// MessageStatus 消息状态 (Message status)
type MessageStatus int

const (
	MessageStatusPending MessageStatus = iota // 等待发送 (Pending to send)
	MessageStatusSent                         // 已发送 (Sent)
	MessageStatusAcked                        // 已确认 (Acknowledged)
	MessageStatusFailed                       // 发送失败 (Failed)
	MessageStatusExpired                      // 已过期 (Expired)
)

// String 返回消息状态的字符串表示 (Returns string representation of message status)
func (s MessageStatus) String() string {
	switch s {
	case MessageStatusPending:
		return "pending"
	case MessageStatusSent:
		return "sent"
	case MessageStatusAcked:
		return "acknowledged"
	case MessageStatusFailed:
		return "failed"
	case MessageStatusExpired:
		return "expired"
	default:
		return "unknown"
	}
}

// PendingMessage 待处理消息 (Pending message)
type PendingMessage struct {
	Message     *ApplicationMessage   `json:"message"`      // 消息内容 (Message content)
	Status      MessageStatus         `json:"status"`       // 消息状态 (Message status)
	SentAt      time.Time             `json:"sent_at"`      // 发送时间 (Sent time)
	LastAttempt time.Time             `json:"last_attempt"` // 最后尝试时间 (Last attempt time)
	Attempts    int                   `json:"attempts"`     // 尝试次数 (Attempt count)
	ExpiresAt   time.Time             `json:"expires_at"`   // 过期时间 (Expiration time)
	RetryResult *reporter.RetryResult `json:"retry_result"` // 重试结果 (Retry result)
}

// IsExpired 检查消息是否已过期 (Checks if message is expired)
func (pm *PendingMessage) IsExpired() bool {
	return time.Now().After(pm.ExpiresAt)
}

// MessageReliabilityConfig 消息可靠性配置 (Message reliability configuration)
type MessageReliabilityConfig struct {
	AckTimeout      time.Duration         // 确认超时时间 (Acknowledgment timeout)
	MessageTTL      time.Duration         // 消息生存时间 (Message time to live)
	MaxPendingMsgs  int                   // 最大待处理消息数 (Maximum pending messages)
	CleanupInterval time.Duration         // 清理间隔 (Cleanup interval)
	RetryConfig     *reporter.RetryConfig // 重试配置 (Retry configuration)
	EnableDedup     bool                  // 启用去重 (Enable deduplication)
	DedupCacheSize  int                   // 去重缓存大小 (Deduplication cache size)
	DedupCacheTTL   time.Duration         // 去重缓存TTL (Deduplication cache TTL)
}

// DefaultMessageReliabilityConfig 返回默认配置 (Returns default configuration)
func DefaultMessageReliabilityConfig() *MessageReliabilityConfig {
	return &MessageReliabilityConfig{
		AckTimeout:      30 * time.Second,
		MessageTTL:      5 * time.Minute,
		MaxPendingMsgs:  1000,
		CleanupInterval: time.Minute,
		RetryConfig:     reporter.DefaultRetryConfig(),
		EnableDedup:     true,
		DedupCacheSize:  10000,
		DedupCacheTTL:   10 * time.Minute,
	}
}

// MessageSender 消息发送器接口 (Message sender interface)
type MessageSender interface {
	SendMessage(ctx context.Context, msg *ApplicationMessage) error
}

// AckHandler 确认处理器接口 (Acknowledgment handler interface)
type AckHandler interface {
	HandleAck(messageID string) error
}

// Retryer 重试器接口 (Retryer interface)
type Retryer interface {
	Execute(ctx context.Context, fn func(context.Context, int) error) *reporter.RetryResult
	GetStats() map[string]interface{}
}

// MessageReliabilityManager 消息可靠性管理器 (Message reliability manager)
type MessageReliabilityManager struct {
	config          *MessageReliabilityConfig
	pendingMessages map[string]*PendingMessage
	dedupCache      map[string]time.Time // 去重缓存 (Deduplication cache)
	retryer         Retryer
	sender          MessageSender
	mutex           sync.RWMutex
	stopCh          chan struct{}
	stopped         bool
}

// NewMessageReliabilityManager 创建新的消息可靠性管理器 (Creates a new message reliability manager)
func NewMessageReliabilityManager(config *MessageReliabilityConfig, sender MessageSender) *MessageReliabilityManager {
	if config == nil {
		config = DefaultMessageReliabilityConfig()
	}

	mrm := &MessageReliabilityManager{
		config:          config,
		pendingMessages: make(map[string]*PendingMessage),
		dedupCache:      make(map[string]time.Time),
		retryer:         createRetryer(config.RetryConfig),
		sender:          sender,
		stopCh:          make(chan struct{}),
	}

	// 启动清理协程 (Start cleanup goroutine)
	go mrm.cleanupLoop()

	return mrm
}

// SendReliableMessage 发送可靠消息 (Sends a reliable message)
func (mrm *MessageReliabilityManager) SendReliableMessage(ctx context.Context, msg *ApplicationMessage) error {
	mrm.mutex.Lock()
	defer mrm.mutex.Unlock()

	if mrm.stopped {
		return fmt.Errorf("message reliability manager has been stopped")
	}

	// 检查去重 (Check deduplication)
	if mrm.config.EnableDedup && mrm.isDuplicate(msg.ID) {
		return fmt.Errorf("duplicate message detected: %s", msg.ID)
	}

	// 检查待处理消息数量限制 (Check pending message limit)
	if len(mrm.pendingMessages) >= mrm.config.MaxPendingMsgs {
		return fmt.Errorf("too many pending messages: %d", len(mrm.pendingMessages))
	}

	// 创建待处理消息 (Create pending message)
	now := time.Now()
	pendingMsg := &PendingMessage{
		Message:     msg,
		Status:      MessageStatusPending,
		SentAt:      now,
		LastAttempt: now,
		Attempts:    0,
		ExpiresAt:   now.Add(mrm.config.MessageTTL),
	}

	// 添加到待处理消息列表 (Add to pending messages)
	mrm.pendingMessages[msg.ID] = pendingMsg

	// 添加到去重缓存 (Add to deduplication cache)
	if mrm.config.EnableDedup {
		mrm.dedupCache[msg.ID] = now.Add(mrm.config.DedupCacheTTL)
	}

	// 异步发送消息 (Send message asynchronously)
	go mrm.sendMessageWithRetry(ctx, pendingMsg)

	return nil
}

// sendMessageWithRetry 使用重试机制发送消息 (Sends message with retry mechanism)
func (mrm *MessageReliabilityManager) sendMessageWithRetry(ctx context.Context, pendingMsg *PendingMessage) {
	result := mrm.retryer.Execute(ctx, func(ctx context.Context, attempt int) error {
		mrm.mutex.Lock()
		pendingMsg.Attempts = attempt
		pendingMsg.LastAttempt = time.Now()
		pendingMsg.Status = MessageStatusSent
		mrm.mutex.Unlock()

		return mrm.sender.SendMessage(ctx, pendingMsg.Message)
	})

	mrm.mutex.Lock()
	defer mrm.mutex.Unlock()

	pendingMsg.RetryResult = result

	if result.Success {
		pendingMsg.Status = MessageStatusSent
		// 等待确认或超时 (Wait for acknowledgment or timeout)
		go mrm.waitForAck(pendingMsg)
	} else {
		pendingMsg.Status = MessageStatusFailed
	}
}

// waitForAck 等待消息确认 (Waits for message acknowledgment)
func (mrm *MessageReliabilityManager) waitForAck(pendingMsg *PendingMessage) {
	timer := time.NewTimer(mrm.config.AckTimeout)
	defer timer.Stop()

	select {
	case <-timer.C:
		// 确认超时 (Acknowledgment timeout)
		mrm.mutex.Lock()
		if pendingMsg.Status == MessageStatusSent {
			pendingMsg.Status = MessageStatusExpired
		}
		mrm.mutex.Unlock()
	case <-mrm.stopCh:
		return
	}
}

// HandleAck 处理消息确认 (Handles message acknowledgment)
func (mrm *MessageReliabilityManager) HandleAck(messageID string) error {
	mrm.mutex.Lock()
	defer mrm.mutex.Unlock()

	pendingMsg, exists := mrm.pendingMessages[messageID]
	if !exists {
		return fmt.Errorf("message not found: %s", messageID)
	}

	if pendingMsg.Status == MessageStatusSent {
		pendingMsg.Status = MessageStatusAcked
		return nil
	}

	return fmt.Errorf("message %s is not in sent status, current status: %s", messageID, pendingMsg.Status)
}

// isDuplicate 检查是否为重复消息 (Checks if message is duplicate)
func (mrm *MessageReliabilityManager) isDuplicate(messageID string) bool {
	expireTime, exists := mrm.dedupCache[messageID]
	if !exists {
		return false
	}

	// 检查是否过期 (Check if expired)
	if time.Now().After(expireTime) {
		delete(mrm.dedupCache, messageID)
		return false
	}

	return true
}

// GetPendingMessage 获取待处理消息 (Gets pending message)
func (mrm *MessageReliabilityManager) GetPendingMessage(messageID string) (*PendingMessage, bool) {
	mrm.mutex.RLock()
	defer mrm.mutex.RUnlock()

	msg, exists := mrm.pendingMessages[messageID]
	return msg, exists
}

// GetPendingMessages 获取所有待处理消息 (Gets all pending messages)
func (mrm *MessageReliabilityManager) GetPendingMessages() map[string]*PendingMessage {
	mrm.mutex.RLock()
	defer mrm.mutex.RUnlock()

	result := make(map[string]*PendingMessage)
	for id, msg := range mrm.pendingMessages {
		result[id] = msg
	}
	return result
}

// GetStats 获取统计信息 (Gets statistics)
func (mrm *MessageReliabilityManager) GetStats() map[string]interface{} {
	mrm.mutex.RLock()
	defer mrm.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["pending_count"] = len(mrm.pendingMessages)
	stats["dedup_cache_size"] = len(mrm.dedupCache)

	// 按状态统计 (Statistics by status)
	statusCounts := make(map[string]int)
	for _, msg := range mrm.pendingMessages {
		statusCounts[msg.Status.String()]++
	}
	stats["status_counts"] = statusCounts

	// 重试统计 (Retry statistics)
	stats["retry_stats"] = mrm.retryer.GetStats()

	return stats
}

// cleanupLoop 清理循环 (Cleanup loop)
func (mrm *MessageReliabilityManager) cleanupLoop() {
	ticker := time.NewTicker(mrm.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			mrm.cleanup()
		case <-mrm.stopCh:
			return
		}
	}
}

// cleanup 清理过期消息 (Cleans up expired messages)
func (mrm *MessageReliabilityManager) cleanup() {
	mrm.mutex.Lock()
	defer mrm.mutex.Unlock()

	now := time.Now()

	// 清理过期的待处理消息 (Clean up expired pending messages)
	for id, msg := range mrm.pendingMessages {
		if msg.IsExpired() || msg.Status == MessageStatusAcked || msg.Status == MessageStatusFailed {
			delete(mrm.pendingMessages, id)
		}
	}

	// 清理过期的去重缓存 (Clean up expired deduplication cache)
	for id, expireTime := range mrm.dedupCache {
		if now.After(expireTime) {
			delete(mrm.dedupCache, id)
		}
	}
}

// Stop 停止消息可靠性管理器 (Stops the message reliability manager)
func (mrm *MessageReliabilityManager) Stop() {
	mrm.mutex.Lock()
	defer mrm.mutex.Unlock()

	if !mrm.stopped {
		mrm.stopped = true
		close(mrm.stopCh)
	}
}
