package client

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/logger"
	"server-monitor/internal/manager"
	"server-monitor/internal/monitor"
	"server-monitor/internal/utils"
)

// ClientState 客户端状态
type ClientState int

const (
	StateIdle ClientState = iota
	StateRunning
	StateStopping
	StateStopped
	StateError
)

// String 返回状态字符串
func (s ClientState) String() string {
	switch s {
	case StateIdle:
		return "空闲"
	case StateRunning:
		return "运行中"
	case StateStopping:
		return "停止中"
	case StateStopped:
		return "已停止"
	case StateError:
		return "错误"
	default:
		return "未知"
	}
}

// MainClient 主客户端
type MainClient struct {
	// 配置
	config *config.Config

	// 组件
	logger         *logger.Logger
	monitor        *monitor.SystemMonitor
	serviceManager *manager.Manager
	httpClient     *Client

	// 状态管理
	state   ClientState
	stateMu sync.RWMutex

	// 生命周期管理
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// 错误处理
	errorCh   chan error
	lastError error
	errorMu   sync.RWMutex

	// 信号处理
	signalCh chan os.Signal

	// 统计信息
	stats   *ClientStats
	statsMu sync.RWMutex
}

// ClientStats 客户端统计信息
type ClientStats struct {
	StartTime      time.Time     `json:"start_time"`
	Uptime         time.Duration `json:"uptime"`
	MonitorCycles  int64         `json:"monitor_cycles"`
	ReportsSent    int64         `json:"reports_sent"`
	ReportsFailed  int64         `json:"reports_failed"`
	LastReportTime time.Time     `json:"last_report_time"`
	ErrorCount     int64         `json:"error_count"`
	LastErrorTime  time.Time     `json:"last_error_time"`
	LastErrorMsg   string        `json:"last_error_msg"`
}

// NewMainClient 创建主客户端
func NewMainClient(configPath string) (*MainClient, error) {
	// 加载配置
	configManager := config.NewManager(configPath)
	if err := configManager.Load(); err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	cfg := configManager.Get()

	// 初始化日志器
	logConfig := &logger.Config{
		Level:      logger.LevelInfo,
		Format:     "text",
		Output:     "both",
		File:       "./logs/client.log",
		MaxSize:    50,
		MaxBackups: 5,
		MaxAge:     30,
		Compress:   true,
	}

	clientLogger, err := logger.NewLogger(logConfig)
	if err != nil {
		return nil, fmt.Errorf("初始化日志器失败: %w", err)
	}

	// 创建系统监控器
	systemMonitor := monitor.NewSystemMonitor(30 * time.Second)

	// 创建服务管理器
	serviceManager := manager.NewManager()

	// 创建HTTP客户端
	httpClient := NewClient(cfg)

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())

	client := &MainClient{
		config:         cfg,
		logger:         clientLogger,
		monitor:        systemMonitor,
		serviceManager: serviceManager,
		httpClient:     httpClient,
		state:          StateIdle,
		ctx:            ctx,
		cancel:         cancel,
		errorCh:        make(chan error, 10),
		signalCh:       make(chan os.Signal, 1),
		stats: &ClientStats{
			StartTime: time.Now(),
		},
	}

	// 设置信号处理
	signal.Notify(client.signalCh, syscall.SIGINT, syscall.SIGTERM)

	return client, nil
}

// Start 启动客户端
func (c *MainClient) Start() error {
	c.stateMu.Lock()
	if c.state != StateIdle {
		c.stateMu.Unlock()
		return fmt.Errorf("客户端已在运行或处于错误状态")
	}
	c.state = StateRunning
	c.stateMu.Unlock()

	c.logger.Info("客户端启动中...")

	// 启动错误处理协程
	c.wg.Add(1)
	go c.errorHandler()

	// 启动监控协程
	c.wg.Add(1)
	go c.monitorLoop()

	// 启动数据上报协程
	c.wg.Add(1)
	go c.reportLoop()

	// 启动信号处理协程
	c.wg.Add(1)
	go c.signalHandler()

	c.logger.Info("客户端启动完成")
	return nil
}

// Stop 停止客户端
func (c *MainClient) Stop() error {
	c.stateMu.Lock()
	if c.state != StateRunning {
		c.stateMu.Unlock()
		return fmt.Errorf("客户端未在运行")
	}
	c.state = StateStopping
	c.stateMu.Unlock()

	c.logger.Info("客户端停止中...")

	// 取消上下文
	c.cancel()

	// 等待所有协程结束
	done := make(chan struct{})
	go func() {
		c.wg.Wait()
		close(done)
	}()

	// 设置超时
	select {
	case <-done:
		c.logger.Info("客户端优雅停止完成")
	case <-time.After(10 * time.Second):
		c.logger.Warn("客户端停止超时，强制退出")
	}

	c.stateMu.Lock()
	c.state = StateStopped
	c.stateMu.Unlock()

	// 关闭日志器
	if err := c.logger.Close(); err != nil {
		log.Printf("关闭日志器失败: %v", err)
	}

	return nil
}

// Wait 等待客户端结束
func (c *MainClient) Wait() {
	c.wg.Wait()
}

// GetState 获取客户端状态
func (c *MainClient) GetState() ClientState {
	c.stateMu.RLock()
	defer c.stateMu.RUnlock()
	return c.state
}

// GetStats 获取统计信息
func (c *MainClient) GetStats() *ClientStats {
	c.statsMu.RLock()
	defer c.statsMu.RUnlock()

	stats := *c.stats
	stats.Uptime = time.Since(c.stats.StartTime)
	return &stats
}

// GetLastError 获取最后一个错误
func (c *MainClient) GetLastError() error {
	c.errorMu.RLock()
	defer c.errorMu.RUnlock()
	return c.lastError
}

// monitorLoop 监控循环
func (c *MainClient) monitorLoop() {
	defer c.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	c.logger.Info("监控循环启动")

	for {
		select {
		case <-c.ctx.Done():
			c.logger.Info("监控循环停止")
			return
		case <-ticker.C:
			c.performMonitoring()
		}
	}
}

// performMonitoring 执行监控
func (c *MainClient) performMonitoring() {
	c.statsMu.Lock()
	c.stats.MonitorCycles++
	c.statsMu.Unlock()

	// 获取系统信息
	systemInfo, err := c.monitor.GetSystemInfo()
	if err != nil {
		c.handleError(fmt.Errorf("获取系统信息失败: %w", err))
		return
	}

	c.logger.Debug("系统监控 - CPU: %.1f%%, 内存: %.1f%%, 磁盘: %.1f%%",
		systemInfo.CPUUsage, systemInfo.MemoryUsage, systemInfo.DiskUsage)
}

// reportLoop 数据上报循环
func (c *MainClient) reportLoop() {
	defer c.wg.Done()

	ticker := time.NewTicker(60 * time.Second)
	defer ticker.Stop()

	c.logger.Info("数据上报循环启动")

	for {
		select {
		case <-c.ctx.Done():
			c.logger.Info("数据上报循环停止")
			return
		case <-ticker.C:
			c.performReport()
		}
	}
}

// performReport 执行数据上报
func (c *MainClient) performReport() {
	// 获取系统信息
	systemInfo, err := c.monitor.GetSystemInfo()
	if err != nil {
		c.handleError(fmt.Errorf("获取系统信息失败: %w", err))
		c.statsMu.Lock()
		c.stats.ReportsFailed++
		c.statsMu.Unlock()
		return
	}

	// 上报数据（这里可以调用HTTP客户端发送数据）
	c.logger.Debug("上报系统数据: %+v", systemInfo)

	c.statsMu.Lock()
	c.stats.ReportsSent++
	c.stats.LastReportTime = time.Now()
	c.statsMu.Unlock()

	c.logger.Debug("数据上报成功")
}

// errorHandler 错误处理协程
func (c *MainClient) errorHandler() {
	defer c.wg.Done()

	c.logger.Info("错误处理器启动")

	for {
		select {
		case <-c.ctx.Done():
			c.logger.Info("错误处理器停止")
			return
		case err := <-c.errorCh:
			c.processError(err)
		}
	}
}

// processError 处理错误
func (c *MainClient) processError(err error) {
	c.errorMu.Lock()
	c.lastError = err
	c.errorMu.Unlock()

	c.statsMu.Lock()
	c.stats.ErrorCount++
	c.stats.LastErrorTime = time.Now()
	c.stats.LastErrorMsg = err.Error()
	c.statsMu.Unlock()

	c.logger.Error("客户端错误: %v", err)

	// 根据错误类型决定是否需要重启或其他处理
	// 这里可以添加更复杂的错误恢复逻辑
}

// handleError 处理错误
func (c *MainClient) handleError(err error) {
	select {
	case c.errorCh <- err:
	default:
		// 错误通道满了，直接记录日志
		c.logger.Error("错误通道满，直接记录: %v", err)
	}
}

// signalHandler 信号处理协程
func (c *MainClient) signalHandler() {
	defer c.wg.Done()

	c.logger.Info("信号处理器启动")

	for {
		select {
		case <-c.ctx.Done():
			c.logger.Info("信号处理器停止")
			return
		case sig := <-c.signalCh:
			c.logger.Info("收到信号: %v", sig)

			switch sig {
			case syscall.SIGINT, syscall.SIGTERM:
				c.logger.Info("收到停止信号，开始优雅停止...")
				go func() {
					if err := c.Stop(); err != nil {
						c.logger.Error("停止客户端失败: %v", err)
					}
				}()
				return
			}
		}
	}
}

// HealthCheck 健康检查
func (c *MainClient) HealthCheck() map[string]interface{} {
	stats := c.GetStats()

	return map[string]interface{}{
		"status":         c.GetState().String(),
		"uptime":         stats.Uptime.String(),
		"monitor_cycles": stats.MonitorCycles,
		"reports_sent":   stats.ReportsSent,
		"reports_failed": stats.ReportsFailed,
		"error_count":    stats.ErrorCount,
		"last_error":     c.GetLastError(),
		"hostname":       utils.GetHostname(),
		"os":             utils.GetOS(),
		"arch":           utils.GetArch(),
		"go_version":     utils.GetGoVersion(),
	}
}
