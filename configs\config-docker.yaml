# Server Monitor Docker Configuration Template
# 服务器监控系统Docker环境配置模板

# 系统配置
system:
  name: "Server Monitor Docker"
  version: "1.0.0"
  environment: "production"
  data_dir: "/app/data"
  pid_file: "/app/data/monitor.pid"
  mode: "both"

# 数据库配置
database:
  path: "/app/data/monitor.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "1h"
  conn_max_idle_time: "10m"
  enable_wal: true
  enable_foreign_key: true
  busy_timeout: "30s"

# 调度器配置
scheduler:
  enabled: true
  mode: "both"
  timezone: "Asia/Shanghai"
  max_concurrent: 4
  retry_attempts: 3
  retry_interval: "5m"

# 对端配置（通过环境变量配置）
peer:
  ip: "" # 设置为空，通过环境变量MONITOR_PEER_IP配置
  port: 8443
  username: "admin" # 通过环境变量MONITOR_PEER_USERNAME配置
  password: "" # 通过环境变量MONITOR_PEER_PASSWORD配置
  timeout: "30s"
  enabled: false # 通过环境变量MONITOR_PEER_ENABLED配置

# 测试参数配置
test:
  duration: "30s"
  parallel: 4
  window_size: "64K"
  buffer_length: "128K"
  protocol: "tcp"
  timeout: "60s"
  connect_timeout: "10s"
  max_retries: 3
  retry_delay: "5s"

# Web界面配置
web:
  enabled: true
  port: 8080
  host: "0.0.0.0"
  static_dir: "/app/web/static"
  template_dir: "/app/web/templates"

# API配置
api:
  enabled: true
  port: 8443
  host: "0.0.0.0"
  prefix: "/api/v1"
  timeout: "30s"
  rate_limit: 100

# 服务器配置（示例）
servers:
  - name: "docker-server-01"
    ip: "*************"
    port: 5201
    location: "Docker Environment"
    provider: "Local"
    enabled: true
    priority: 1

# 数据同步配置
sync:
  enabled: false # 通过环境变量MONITOR_SYNC_ENABLED配置
  interval: "5m"
  batch_size: 100
  max_retries: 3
  retry_interval: "30s"
  timeout: "30s"

# 日志配置
log:
  level: "info" # 通过环境变量MONITOR_LOG_LEVEL配置
  format: "json"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true
# Docker环境变量说明:
# PEER_IP - 对端IP地址
# PEER_USERNAME - 对端用户名 (默认: admin)
# PEER_PASSWORD - 对端密码
# PEER_ENABLED - 是否启用对端同步 (默认: false)
# SYNC_ENABLED - 是否启用数据同步 (默认: false)
# LOG_LEVEL - 日志级别 (默认: info)

# Docker Compose 示例:
# version: '3.8'
# services:
#   monitor:
#     image: server-monitor:latest
#     ports:
#       - "8080:8080"
#       - "8443:8443"
#     volumes:
#       - ./data:/app/data
#       - ./config.yaml:/app/config.yaml
#     environment:
#       - PEER_IP=*************
#       - PEER_PASSWORD=your_password
#       - PEER_ENABLED=true
#       - LOG_LEVEL=debug
