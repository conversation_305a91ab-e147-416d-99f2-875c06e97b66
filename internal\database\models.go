package database

import (
	"time"
)

// Server 服务器信息表
type Server struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`                 // 服务器名称
	IP          string    `json:"ip" db:"ip"`                     // IP地址
	Port        int       `json:"port" db:"port"`                 // 端口
	Location    string    `json:"location" db:"location"`         // 地理位置
	Provider    string    `json:"provider" db:"provider"`         // 服务商
	IsActive    bool      `json:"is_active" db:"is_active"`       // 是否激活
	LastSeen    time.Time `json:"last_seen" db:"last_seen"`       // 最后在线时间
	CreatedAt   time.Time `json:"created_at" db:"created_at"`     // 创建时间
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`     // 更新时间
}

// HourlyResult 每小时测速结果表
type HourlyResult struct {
	ID              int       `json:"id" db:"id"`
	ServerID        uint       `json:"server_id" db:"server_id"`           // 服务器ID
	TestHour        time.Time `json:"test_hour" db:"test_hour"`           // 测试小时（精确到小时）
	UploadSpeed     float64   `json:"upload_speed" db:"upload_speed"`     // 上传速度 Mbps
	DownloadSpeed   float64   `json:"download_speed" db:"download_speed"` // 下载速度 Mbps
	Latency         float64   `json:"latency" db:"latency"`               // 延迟 ms
	Jitter          float64   `json:"jitter" db:"jitter"`                 // 抖动 ms
	PacketLoss      float64   `json:"packet_loss" db:"packet_loss"`       // 丢包率 %
	TestDuration    int       `json:"test_duration" db:"test_duration"`   // 测试持续时间 秒
	TestType        string    `json:"test_type" db:"test_type"`           // 测试类型 (upload/download/both)
	Status          string    `json:"status" db:"status"`                 // 测试状态 (success/failed/timeout)
	ErrorMessage    string    `json:"error_message" db:"error_message"`   // 错误信息
	CreatedAt       time.Time `json:"created_at" db:"created_at"`         // 创建时间
}

// SyncStatus 双机同步状态表
type SyncStatus struct {
	ID              int       `json:"id" db:"id"`
	PeerIP          string    `json:"peer_ip" db:"peer_ip"`               // 对端IP
	LastSyncTime    time.Time `json:"last_sync_time" db:"last_sync_time"` // 最后同步时间
	SyncStatus      string    `json:"sync_status" db:"sync_status"`       // 同步状态 (synced/syncing/failed)
	RecordsSynced   int       `json:"records_synced" db:"records_synced"` // 已同步记录数
	LastSyncError   string    `json:"last_sync_error" db:"last_sync_error"` // 最后同步错误
	CreatedAt       time.Time `json:"created_at" db:"created_at"`         // 创建时间
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`         // 更新时间
}

// SystemConfig 系统配置表
type SystemConfig struct {
	ID          int       `json:"id" db:"id"`
	ConfigKey   string    `json:"config_key" db:"config_key"`     // 配置键
	ConfigValue string    `json:"config_value" db:"config_value"` // 配置值
	Description string    `json:"description" db:"description"`   // 配置描述
	ConfigType  string    `json:"config_type" db:"config_type"`   // 配置类型 (string/int/bool/json)
	IsSystem    bool      `json:"is_system" db:"is_system"`       // 是否系统配置
	CreatedAt   time.Time `json:"created_at" db:"created_at"`     // 创建时间
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`     // 更新时间
}

// TableNames 表名常量
const (
	TableServers       = "servers"
	TableHourlyResults = "hourly_results"
	TableSyncStatus    = "sync_status"
	TableSystemConfig  = "system_config"
)

// TestStatus 测试状态常量
const (
	TestStatusSuccess = "success"
	TestStatusFailed  = "failed"
	TestStatusTimeout = "timeout"
	TestStatusRunning = "running"
)

// TestType 测试类型常量
const (
	TestTypeUpload   = "upload"
	TestTypeDownload = "download"
	TestTypeBoth     = "both"
)

// SyncStatusType 同步状态常量
const (
	SyncStatusSynced  = "synced"
	SyncStatusSyncing = "syncing"
	SyncStatusFailed  = "failed"
)

// ConfigType 配置类型常量
const (
	ConfigTypeString = "string"
	ConfigTypeInt    = "int"
	ConfigTypeBool   = "bool"
	ConfigTypeJSON   = "json"
)

// GetTableName 获取模型对应的表名
func (s *Server) GetTableName() string {
	return TableServers
}

func (h *HourlyResult) GetTableName() string {
	return TableHourlyResults
}

func (ss *SyncStatus) GetTableName() string {
	return TableSyncStatus
}

func (sc *SystemConfig) GetTableName() string {
	return TableSystemConfig
}

// Validate 验证数据有效性
func (s *Server) Validate() error {
	if s.Name == "" {
		return ErrInvalidServerName
	}
	if s.IP == "" {
		return ErrInvalidServerIP
	}
	if s.Port <= 0 || s.Port > 65535 {
		return ErrInvalidServerPort
	}
	return nil
}

func (h *HourlyResult) Validate() error {
	if h.ServerID <= 0 {
		return ErrInvalidServerID
	}
	if h.TestHour.IsZero() {
		return ErrInvalidTestHour
	}
	if h.Status == "" {
		return ErrInvalidTestStatus
	}
	return nil
}

func (ss *SyncStatus) Validate() error {
	if ss.PeerIP == "" {
		return ErrInvalidPeerIP
	}
	if ss.SyncStatus == "" {
		return ErrInvalidSyncStatus
	}
	return nil
}

func (sc *SystemConfig) Validate() error {
	if sc.ConfigKey == "" {
		return ErrInvalidConfigKey
	}
	if sc.ConfigType == "" {
		return ErrInvalidConfigType
	}
	return nil
}
