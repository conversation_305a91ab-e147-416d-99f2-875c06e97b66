package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"fmt"
	"io"
	"time"

	"golang.org/x/crypto/pbkdf2"
	"golang.org/x/crypto/sha3" // Using SHA3 for PBKDF2 as it's more robust than SHA1
)

const (
	keyLen     = 32    // AES-256 requires a 32-byte key
	nonceLen   = 12    // GCM recommended Nonce size
	saltLen    = 16    // Salt size for PBKDF2
	pbkdf2Iter = 10000 // PBKDF2 iterations
)

// GenerateSalt generates a random salt for PBKDF2
func GenerateSalt() ([]byte, error) {
	salt := make([]byte, saltLen)
	if _, err := io.ReadFull(rand.Reader, salt); err != nil {
		return nil, fmt.Errorf("failed to generate salt: %w", err)
	}
	return salt, nil
}

// DeriveKey derives a key from a password and salt using PBKDF2
func DeriveKey(password, salt []byte) []byte {
	return pbkdf2.Key(password, salt, pbkdf2Iter, keyLen, sha3.New256) // Using SHA3-256
}

// Encrypt encrypts data using AES-256-GCM
func Encrypt(key, plaintext []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	nonce := make([]byte, nonceLen)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)
	return append(nonce, ciphertext...), nil // Prepend nonce to ciphertext
}

// Decrypt decrypts data using AES-256-GCM
func Decrypt(key, ciphertextWithNonce []byte) ([]byte, error) {
	if len(ciphertextWithNonce) < nonceLen {
		return nil, fmt.Errorf("ciphertext too short")
	}

	nonce := ciphertextWithNonce[:nonceLen]
	ciphertext := ciphertextWithNonce[nonceLen:]

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %w", err)
	}

	return plaintext, nil
}

// EncryptionService 加密服务 (Encryption service)
type EncryptionService struct {
	keyManager *KeyManager
}

// NewEncryptionService 创建新的加密服务 (Creates a new encryption service)
func NewEncryptionService(keyManager *KeyManager) *EncryptionService {
	return &EncryptionService{
		keyManager: keyManager,
	}
}

// EncryptWithVersion 使用当前密钥加密并返回密钥版本 (Encrypts with current key and returns key version)
func (es *EncryptionService) EncryptWithVersion(plaintext []byte) ([]byte, KeyVersion, error) {
	currentKey := es.keyManager.GetCurrentKey()
	if currentKey == nil {
		return nil, 0, fmt.Errorf("no current key available")
	}

	ciphertext, err := Encrypt(currentKey.Data, plaintext)
	if err != nil {
		return nil, 0, fmt.Errorf("encryption failed: %w", err)
	}

	return ciphertext, currentKey.Version, nil
}

// DecryptWithVersion 使用指定版本的密钥解密 (Decrypts with specified key version)
func (es *EncryptionService) DecryptWithVersion(ciphertext []byte, version KeyVersion) ([]byte, error) {
	key := es.keyManager.GetKeyForDecryption(version)
	if key == nil {
		return nil, fmt.Errorf("key version %d not available for decryption", version)
	}

	plaintext, err := Decrypt(key.Data, ciphertext)
	if err != nil {
		return nil, fmt.Errorf("decryption failed: %w", err)
	}

	return plaintext, nil
}

// EncryptedData 加密数据结构 (Encrypted data structure)
type EncryptedData struct {
	Data       []byte     `json:"data"`        // 加密数据 (Encrypted data)
	KeyVersion KeyVersion `json:"key_version"` // 密钥版本 (Key version)
	Timestamp  int64      `json:"timestamp"`   // 加密时间戳 (Encryption timestamp)
}

// EncryptData 加密数据并返回完整的加密数据结构 (Encrypts data and returns complete encrypted data structure)
func (es *EncryptionService) EncryptData(plaintext []byte) (*EncryptedData, error) {
	ciphertext, version, err := es.EncryptWithVersion(plaintext)
	if err != nil {
		return nil, err
	}

	return &EncryptedData{
		Data:       ciphertext,
		KeyVersion: version,
		Timestamp:  time.Now().UnixNano(),
	}, nil
}

// DecryptData 解密加密数据结构 (Decrypts encrypted data structure)
func (es *EncryptionService) DecryptData(encData *EncryptedData) ([]byte, error) {
	return es.DecryptWithVersion(encData.Data, encData.KeyVersion)
}
