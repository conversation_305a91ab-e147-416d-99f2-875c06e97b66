package api

import (
	"net/http"

	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/logger"

	"github.com/gin-gonic/gin"
)

// SetupRouter 设置 Gin 路由器，包含所有 API 路由。(Sets up the Gin router with all API routes.)
func SetupRouter(cfg *config.Manager, repo database.Repository) *gin.Engine {
	router := gin.Default()

	// 获取配置 (Get configurations)
	apiConfig := cfg.Get().API
	securityConfig := cfg.Get().Security

	// 应用中间件 (Apply middleware)
	router.Use(CORSMiddleware(apiConfig.CORS))
	router.Use(CompressionMiddleware())
	router.Use(LoggerMiddleware())
	router.Use(RateLimitMiddleware(apiConfig.RateLimit))

	// 公共路由 (Public routes)
	public := router.Group(apiConfig.Prefix)
	{
		public.GET("/health", HealthCheckHandler)
		public.POST("/example", ExampleHandler) // 用于数据验证示例的新路由 (New route for data validation example)
	}

	// 认证路由 (Authenticated routes)
	authenticated := router.Group(apiConfig.Prefix)
	authenticated.Use(AuthMiddleware(repo, securityConfig.Auth)) // 假设存在 AuthMiddleware (Assuming AuthMiddleware exists)
	authenticated.Use(RoleMiddleware(securityConfig.Auth, "admin"))                          // 假设存在 RoleMiddleware (Assuming RoleMiddleware exists)
	{
		// Server CRUD
		authenticated.POST("/servers", CreateServerHandler(repo))
		authenticated.GET("/servers", ListServersHandler(repo))
		authenticated.GET("/servers/:id", GetServerHandler(repo))
		authenticated.PUT("/servers/:id", UpdateServerHandler(repo))
		authenticated.DELETE("/servers/:id", DeleteServerHandler(repo))

		// SystemInfo
		authenticated.GET("/servers/:id/systeminfo", GetSystemInfoHandler(repo))
	}

	// NoRoute 处理未匹配的路由 (Handle unmatched routes)
	router.NoRoute(func(c *gin.Context) {
		logger.Warn("404 Not Found: %s %s", c.Request.Method, c.Request.URL.Path)
		SendErrorResponse(c, http.StatusNotFound, http.StatusNotFound, "Route not found")
	})

	return router
}
