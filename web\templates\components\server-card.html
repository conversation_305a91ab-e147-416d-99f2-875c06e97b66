{{define "server-card"}}
<div class="server-card bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-all duration-300" 
     data-server-id="{{.ID}}" 
     onclick="navigateToDetail({{.ID}})">
    
    <!-- 服务器标题和状态 -->
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <span class="status-indicator {{if eq .Status "online"}}status-online{{else if eq .Status "offline"}}status-offline{{else}}status-warning{{end}}"></span>
            <h3 class="text-lg font-semibold text-gray-900">{{.Name}}</h3>
        </div>
        <div class="text-sm text-gray-500">
            {{.IPAddress}}
        </div>
    </div>

    <!-- 服务器信息 -->
    <div class="mb-4">
        <div class="flex justify-between text-sm text-gray-600 mb-1">
            <span>{{.Hostname}}</span>
            <span class="capitalize {{if eq .Status "online"}}text-green-600{{else if eq .Status "offline"}}text-red-600{{else}}text-yellow-600{{end}}">
                {{if eq .Status "online"}}在线{{else if eq .Status "offline"}}离线{{else}}警告{{end}}
            </span>
        </div>
        <div class="text-xs text-gray-500">
            {{.OSType}} {{.OSVersion}}
        </div>
    </div>

    <!-- CPU 使用率 -->
    <div class="mb-4">
        <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-700">CPU 使用率</span>
            <span class="text-sm font-bold text-gray-900 cpu-percentage">0%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div class="progress-bar bg-gradient-to-r from-green-400 to-green-600 h-2.5 rounded-full" 
                 data-type="cpu" 
                 data-server-id="{{.ID}}" 
                 style="width: 0%"></div>
        </div>
    </div>

    <!-- 内存使用率 -->
    <div class="mb-4">
        <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-700">内存使用率</span>
            <span class="text-sm font-bold text-gray-900 memory-percentage">0%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div class="progress-bar bg-gradient-to-r from-blue-400 to-blue-600 h-2.5 rounded-full" 
                 data-type="memory" 
                 data-server-id="{{.ID}}" 
                 style="width: 0%"></div>
        </div>
    </div>

    <!-- 最后更新时间 -->
    <div class="flex justify-between items-center text-xs text-gray-500">
        <span>最后更新: <span class="last-update">{{.LastSeen.Format "15:04:05"}}</span></span>
        <span class="uptime">运行时间: --</span>
    </div>

    <!-- 悬停效果提示 -->
    <div class="mt-3 text-center">
        <span class="text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity">
            点击查看详情
        </span>
    </div>
</div>
{{end}}

<!-- 静态版本的服务器卡片模板（用于JavaScript动态生成） -->
<template id="server-card-template">
    <div class="server-card bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-all duration-300" 
         data-server-id="">
        
        <!-- 服务器标题和状态 -->
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <span class="status-indicator status-online"></span>
                <h3 class="text-lg font-semibold text-gray-900 server-name">Server Name</h3>
            </div>
            <div class="text-sm text-gray-500 server-ip">
                ***********
            </div>
        </div>

        <!-- 服务器信息 -->
        <div class="mb-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
                <span class="server-hostname">hostname</span>
                <span class="server-status-text text-green-600">在线</span>
            </div>
            <div class="text-xs text-gray-500 server-os">
                Linux Ubuntu 20.04
            </div>
        </div>

        <!-- CPU 使用率 -->
        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700">CPU 使用率</span>
                <span class="text-sm font-bold text-gray-900 cpu-percentage">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="progress-bar bg-gradient-to-r from-green-400 to-green-600 h-2.5 rounded-full" 
                     data-type="cpu" 
                     style="width: 0%"></div>
            </div>
        </div>

        <!-- 内存使用率 -->
        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700">内存使用率</span>
                <span class="text-sm font-bold text-gray-900 memory-percentage">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="progress-bar bg-gradient-to-r from-blue-400 to-blue-600 h-2.5 rounded-full" 
                     data-type="memory" 
                     style="width: 0%"></div>
            </div>
        </div>

        <!-- 最后更新时间 -->
        <div class="flex justify-between items-center text-xs text-gray-500">
            <span>最后更新: <span class="last-update">--:--:--</span></span>
            <span class="uptime">运行时间: --</span>
        </div>

        <!-- 悬停效果提示 -->
        <div class="mt-3 text-center">
            <span class="text-xs text-gray-400 opacity-0 hover:opacity-100 transition-opacity">
                点击查看详情
            </span>
        </div>
    </div>
</template>
