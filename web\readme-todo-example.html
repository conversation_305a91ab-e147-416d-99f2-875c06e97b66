<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>README & TODO - 服务器监控系统</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- 自定义样式 -->
    <style>
        .tab-content {
            transition: all 0.3s ease;
        }
        
        .todo-item {
            transition: all 0.3s ease;
        }
        
        .todo-item:hover {
            background-color: #f9fafb;
        }
        
        .completed {
            opacity: 0.6;
            text-decoration: line-through;
        }

        .markdown-content h1 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #1f2937;
        }
        
        .markdown-content h2 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.75rem;
            margin-top: 1.5rem;
            color: #374151;
        }
        
        .markdown-content h3 {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            margin-top: 1rem;
            color: #4b5563;
        }
        
        .markdown-content p {
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        
        .markdown-content ul {
            margin-bottom: 1rem;
            padding-left: 1.5rem;
        }
        
        .markdown-content li {
            margin-bottom: 0.5rem;
        }
        
        .markdown-content code {
            background-color: #f3f4f6;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
        
        .markdown-content pre {
            background-color: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin-bottom: 1rem;
        }
        
        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
            color: inherit;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-white via-gray-50 to-gray-100 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">服务器监控系统</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="example.html" class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium">仪表板</a>
                        <a href="services-example.html" class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium">服务管理</a>
                        <a href="system-example.html" class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium">系统监控</a>
                        <a href="#" class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">文档管理</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <span class="text-sm text-gray-500" id="current-time"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <!-- 页面标题 -->
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900">文档管理</h2>
                <p class="mt-2 text-gray-600">查看项目文档和管理待办事项</p>
            </div>

            <!-- 标签页导航 -->
            <div class="bg-white rounded-lg shadow-md">
                <!-- 标签页头部 -->
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button onclick="switchTab('readme')" class="tab-button border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="readme">
                            📖 README
                        </button>
                        <button onclick="switchTab('todo')" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="todo">
                            📝 TODO
                        </button>
                        <button onclick="switchTab('config')" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="config">
                            ⚙️ 配置文件
                        </button>
                    </nav>
                </div>

                <!-- 标签页内容 -->
                <div class="p-6">
                    <!-- README 标签页 -->
                    <div id="readme-tab" class="tab-content">
                        <div class="markdown-content">
                            <h1>服务器监控系统</h1>
                            
                            <p>这是一个基于 Go + Gin + SQLite + Tailwind CSS 的现代化服务器监控系统，提供实时的服务器状态监控、服务管理和系统资源监控功能。</p>
                            
                            <h2>🚀 功能特性</h2>
                            
                            <h3>📊 服务器监控</h3>
                            <ul>
                                <li>实时监控12台服务器的CPU和内存使用情况</li>
                                <li>美观的进度条显示，支持颜色变化</li>
                                <li>状态指示器：在线（绿色）、警告（黄色）、离线（红色）</li>
                                <li>响应式设计，支持手机、平板、桌面设备</li>
                            </ul>
                            
                            <h3>🔧 服务管理</h3>
                            <ul>
                                <li>Supervisor 服务管理</li>
                                <li>Systemd 服务管理</li>
                                <li>Docker 容器管理</li>
                                <li>服务启动、停止、重启功能</li>
                                <li>实时日志查看</li>
                            </ul>
                            
                            <h3>📈 系统监控</h3>
                            <ul>
                                <li>CPU、内存、磁盘、网络实时监控</li>
                                <li>历史数据图表显示</li>
                                <li>系统信息详情</li>
                                <li>磁盘分区使用情况</li>
                            </ul>
                            
                            <h2>🛠️ 技术栈</h2>
                            
                            <ul>
                                <li><strong>后端</strong>: Go + Gin + GORM + SQLite</li>
                                <li><strong>前端</strong>: HTML + Tailwind CSS + JavaScript</li>
                                <li><strong>图表</strong>: Chart.js</li>
                                <li><strong>实时通信</strong>: WebSocket</li>
                            </ul>
                            
                            <h2>📦 安装和运行</h2>
                            
                            <h3>1. 克隆项目</h3>
                            <pre><code>git clone https://github.com/your-repo/server-monitor.git
cd server-monitor</code></pre>
                            
                            <h3>2. 安装依赖</h3>
                            <pre><code>go mod download</code></pre>
                            
                            <h3>3. 编译前端资源</h3>
                            <pre><code>cd web
npm install
npm run build</code></pre>
                            
                            <h3>4. 运行程序</h3>
                            <pre><code>go run main.go</code></pre>
                            
                            <h3>5. 访问系统</h3>
                            <p>打开浏览器访问 <code>http://localhost:8080</code></p>
                            
                            <h2>📝 API 文档</h2>
                            
                            <h3>服务器数据</h3>
                            <ul>
                                <li><code>GET /data/servers</code> - 获取所有服务器状态</li>
                                <li><code>GET /data/servers/:id</code> - 获取特定服务器详情</li>
                                <li><code>GET /data/system</code> - 获取系统监控数据</li>
                                <li><code>GET /data/stats</code> - 获取统计信息</li>
                            </ul>
                            
                            <h3>服务管理</h3>
                            <ul>
                                <li><code>POST /api/services/start</code> - 启动服务</li>
                                <li><code>POST /api/services/stop</code> - 停止服务</li>
                                <li><code>POST /api/services/restart</code> - 重启服务</li>
                                <li><code>GET /api/services/logs/:name</code> - 获取服务日志</li>
                            </ul>
                            
                            <h2>🤝 贡献</h2>
                            
                            <p>欢迎提交 Issue 和 Pull Request！</p>
                            
                            <h2>📄 许可证</h2>
                            
                            <p>MIT License</p>
                        </div>
                    </div>

                    <!-- TODO 标签页 -->
                    <div id="todo-tab" class="tab-content hidden">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">待办事项</h3>
                            <button onclick="addTodo()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                添加任务
                            </button>
                        </div>
                        
                        <!-- 添加任务表单 -->
                        <div id="add-todo-form" class="hidden mb-6 p-4 bg-gray-50 rounded-lg">
                            <div class="flex space-x-3">
                                <input type="text" id="new-todo-input" placeholder="输入新任务..." 
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <select id="todo-priority" class="px-3 py-2 border border-gray-300 rounded-md">
                                    <option value="low">低优先级</option>
                                    <option value="medium" selected>中优先级</option>
                                    <option value="high">高优先级</option>
                                </select>
                                <button onclick="saveTodo()" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
                                    保存
                                </button>
                                <button onclick="cancelTodo()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                                    取消
                                </button>
                            </div>
                        </div>
                        
                        <!-- 任务过滤器 -->
                        <div class="flex space-x-4 mb-6">
                            <button onclick="filterTodos('all')" class="filter-btn px-3 py-1 bg-blue-500 text-white rounded text-sm" data-filter="all">
                                全部 (<span id="count-all">0</span>)
                            </button>
                            <button onclick="filterTodos('pending')" class="filter-btn px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm" data-filter="pending">
                                待完成 (<span id="count-pending">0</span>)
                            </button>
                            <button onclick="filterTodos('completed')" class="filter-btn px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm" data-filter="completed">
                                已完成 (<span id="count-completed">0</span>)
                            </button>
                        </div>
                        
                        <!-- 任务列表 -->
                        <div id="todo-list" class="space-y-3">
                            <!-- 任务项将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 配置文件标签页 -->
                    <div id="config-tab" class="tab-content hidden">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">配置文件</h3>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 服务器配置 -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-700 mb-3">服务器配置 (config.yaml)</h4>
                                <pre class="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto"><code>server:
  host: "0.0.0.0"
  port: 8080
  debug: false

database:
  type: "sqlite"
  path: "./data/monitor.db"

monitoring:
  interval: 5s
  retention: 7d
  
websocket:
  enabled: true
  path: "/ws"

logging:
  level: "info"
  file: "./logs/app.log"</code></pre>
                            </div>
                            
                            <!-- 监控配置 -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-700 mb-3">监控配置 (monitor.json)</h4>
                                <pre class="text-sm bg-gray-800 text-blue-400 p-3 rounded overflow-x-auto"><code>{
  "servers": [
    {
      "id": 1,
      "name": "Web-Server-01",
      "host": "*************",
      "enabled": true
    },
    {
      "id": 2,
      "name": "DB-Server-01",
      "host": "*************",
      "enabled": true
    }
  ],
  "alerts": {
    "cpu_threshold": 80,
    "memory_threshold": 85,
    "disk_threshold": 90
  }
}</code></pre>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex space-x-3">
                            <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                编辑配置
                            </button>
                            <button class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                                重载配置
                            </button>
                            <button class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors">
                                备份配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // TODO 数据
        let todos = [
            { id: 1, text: '完善服务器监控API接口', completed: true, priority: 'high', createdAt: '2024-01-15' },
            { id: 2, text: '添加邮件告警功能', completed: false, priority: 'high', createdAt: '2024-01-16' },
            { id: 3, text: '优化前端响应式设计', completed: false, priority: 'medium', createdAt: '2024-01-17' },
            { id: 4, text: '编写API文档', completed: true, priority: 'medium', createdAt: '2024-01-18' },
            { id: 5, text: '添加用户权限管理', completed: false, priority: 'low', createdAt: '2024-01-19' },
            { id: 6, text: '集成Prometheus监控', completed: false, priority: 'high', createdAt: '2024-01-20' }
        ];

        let currentFilter = 'all';
        let currentTab = 'readme';

        // 切换标签页
        function switchTab(tab) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 重置所有标签按钮样式
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的标签页
            document.getElementById(`${tab}-tab`).classList.remove('hidden');
            
            // 激活选中的标签按钮
            const activeButton = document.querySelector(`[data-tab="${tab}"]`);
            activeButton.classList.remove('border-transparent', 'text-gray-500');
            activeButton.classList.add('border-blue-500', 'text-blue-600');
            
            currentTab = tab;
            
            if (tab === 'todo') {
                renderTodos();
                updateCounts();
            }
        }

        // 渲染TODO列表
        function renderTodos() {
            const container = document.getElementById('todo-list');
            const filteredTodos = todos.filter(todo => {
                if (currentFilter === 'all') return true;
                if (currentFilter === 'pending') return !todo.completed;
                if (currentFilter === 'completed') return todo.completed;
                return true;
            });

            if (filteredTodos.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <p class="mt-2">暂无任务</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredTodos.map(todo => createTodoItem(todo)).join('');
        }

        // 创建TODO项HTML
        function createTodoItem(todo) {
            const priorityColors = {
                high: 'bg-red-100 text-red-800',
                medium: 'bg-yellow-100 text-yellow-800',
                low: 'bg-green-100 text-green-800'
            };

            const priorityText = {
                high: '高',
                medium: '中',
                low: '低'
            };

            return `
                <div class="todo-item flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 ${todo.completed ? 'completed' : ''}">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox" ${todo.completed ? 'checked' : ''} 
                               onchange="toggleTodo(${todo.id})"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <div>
                            <p class="text-gray-900 ${todo.completed ? 'line-through' : ''}">${todo.text}</p>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="px-2 py-1 text-xs rounded-full ${priorityColors[todo.priority]}">
                                    ${priorityText[todo.priority]}优先级
                                </span>
                                <span class="text-xs text-gray-500">${todo.createdAt}</span>
                            </div>
                        </div>
                    </div>
                    <button onclick="deleteTodo(${todo.id})" 
                            class="text-red-500 hover:text-red-700 p-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            `;
        }

        // 更新计数
        function updateCounts() {
            const all = todos.length;
            const pending = todos.filter(t => !t.completed).length;
            const completed = todos.filter(t => t.completed).length;

            document.getElementById('count-all').textContent = all;
            document.getElementById('count-pending').textContent = pending;
            document.getElementById('count-completed').textContent = completed;
        }

        // 过滤TODO
        function filterTodos(filter) {
            currentFilter = filter;
            
            // 更新按钮样式
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('bg-blue-500', 'text-white');
                btn.classList.add('bg-gray-200', 'text-gray-700');
            });
            
            document.querySelector(`[data-filter="${filter}"]`).classList.remove('bg-gray-200', 'text-gray-700');
            document.querySelector(`[data-filter="${filter}"]`).classList.add('bg-blue-500', 'text-white');
            
            renderTodos();
        }

        // 切换TODO状态
        function toggleTodo(id) {
            const todo = todos.find(t => t.id === id);
            if (todo) {
                todo.completed = !todo.completed;
                renderTodos();
                updateCounts();
            }
        }

        // 删除TODO
        function deleteTodo(id) {
            if (confirm('确定要删除这个任务吗？')) {
                todos = todos.filter(t => t.id !== id);
                renderTodos();
                updateCounts();
            }
        }

        // 显示添加表单
        function addTodo() {
            document.getElementById('add-todo-form').classList.remove('hidden');
            document.getElementById('new-todo-input').focus();
        }

        // 保存新TODO
        function saveTodo() {
            const input = document.getElementById('new-todo-input');
            const priority = document.getElementById('todo-priority').value;
            const text = input.value.trim();
            
            if (text) {
                const newTodo = {
                    id: Math.max(...todos.map(t => t.id)) + 1,
                    text: text,
                    completed: false,
                    priority: priority,
                    createdAt: new Date().toISOString().split('T')[0]
                };
                
                todos.unshift(newTodo);
                input.value = '';
                document.getElementById('add-todo-form').classList.add('hidden');
                renderTodos();
                updateCounts();
            }
        }

        // 取消添加
        function cancelTodo() {
            document.getElementById('new-todo-input').value = '';
            document.getElementById('add-todo-form').classList.add('hidden');
        }

        // 更新时间
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('README & TODO page loaded');
            
            updateTime();
            setInterval(updateTime, 1000);
            
            // 监听回车键保存TODO
            document.getElementById('new-todo-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveTodo();
                }
            });
        });
    </script>
</body>
</html>
