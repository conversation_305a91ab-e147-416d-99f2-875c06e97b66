package crypto

import (
	"errors"
	"sync"
	"testing"
	"time"
)

// isReplayAttackError 检查错误是否为重放攻击错误 (Checks if error is a replay attack error)
func isReplayAttackError(err error) bool {
	return errors.Is(err, ErrReplayAttack)
}

func TestNewReplayProtector(t *testing.T) {
	// 测试默认配置 (Test default configuration)
	rp, err := NewReplayProtector(nil)
	if err != nil {
		t.Fatalf("Failed to create replay protector: %v", err)
	}
	defer rp.Stop()

	if rp.config.TimeWindow != DefaultTimeWindow {
		t.Errorf("Expected time window %v, got %v", DefaultTimeWindow, rp.config.TimeWindow)
	}

	if rp.config.CacheSize != DefaultCacheSize {
		t.Errorf("Expected cache size %d, got %d", DefaultCacheSize, rp.config.CacheSize)
	}
}

func TestReplayProtectorValidateMessage(t *testing.T) {
	config := &ReplayProtectorConfig{
		TimeWindow:         time.Minute,
		CacheSize:          100,
		ClockSkewTolerance: 10 * time.Second,
		EnableCleanup:      false,
	}

	rp, err := NewReplayProtector(config)
	if err != nil {
		t.Fatalf("Failed to create replay protector: %v", err)
	}
	defer rp.Stop()

	// 测试有效消息 (Test valid message)
	messageID := "test-message-1"
	timestamp := time.Now().UnixNano()

	err = rp.ValidateMessage(messageID, timestamp)
	if err != nil {
		t.Errorf("Valid message should pass validation: %v", err)
	}

	// 测试重放攻击 (Test replay attack)
	err = rp.ValidateMessage(messageID, timestamp)
	if err == nil || !isReplayAttackError(err) {
		t.Errorf("Expected replay attack error, got: %v", err)
	}

	// 测试过期消息 (Test expired message)
	oldTimestamp := time.Now().Add(-2 * time.Minute).UnixNano()
	err = rp.ValidateMessage("old-message", oldTimestamp)
	if err != ErrMessageExpired {
		t.Errorf("Expected message expired error, got: %v", err)
	}

	// 测试未来消息（超出时钟偏差容忍度）(Test future message beyond clock skew tolerance)
	futureTimestamp := time.Now().Add(time.Minute).UnixNano()
	err = rp.ValidateMessage("future-message", futureTimestamp)
	if err != ErrInvalidTimestamp {
		t.Errorf("Expected invalid timestamp error, got: %v", err)
	}
}

func TestReplayProtectorValidateMessageWithID(t *testing.T) {
	rp, err := NewReplayProtector(nil)
	if err != nil {
		t.Fatalf("Failed to create replay protector: %v", err)
	}
	defer rp.Stop()

	// 生成安全消息ID (Generate secure message ID)
	messageID, err := GenerateSecureMessageID()
	if err != nil {
		t.Fatalf("Failed to generate secure message ID: %v", err)
	}

	// 第一次验证应该成功 (First validation should succeed)
	err = rp.ValidateMessageWithID(messageID)
	if err != nil {
		t.Errorf("First validation should succeed: %v", err)
	}

	// 第二次验证应该检测到重放攻击 (Second validation should detect replay attack)
	err = rp.ValidateMessageWithID(messageID)
	if err == nil || !isReplayAttackError(err) {
		t.Errorf("Expected replay attack error, got: %v", err)
	}
}

func TestReplayProtectorConcurrency(t *testing.T) {
	config := &ReplayProtectorConfig{
		TimeWindow:         time.Minute,
		CacheSize:          1000,
		ClockSkewTolerance: 10 * time.Second,
		EnableCleanup:      false,
	}

	rp, err := NewReplayProtector(config)
	if err != nil {
		t.Fatalf("Failed to create replay protector: %v", err)
	}
	defer rp.Stop()

	const numGoroutines = 100
	const numMessagesPerGoroutine = 10

	var wg sync.WaitGroup
	errors := make(chan error, numGoroutines*numMessagesPerGoroutine)

	// 启动多个goroutine并发验证消息 (Start multiple goroutines to validate messages concurrently)
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < numMessagesPerGoroutine; j++ {
				messageID, err := GenerateSecureMessageID()
				if err != nil {
					errors <- err
					return
				}

				err = rp.ValidateMessageWithID(messageID)
				if err != nil {
					errors <- err
					return
				}
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	// 检查是否有错误 (Check for errors)
	for err := range errors {
		t.Errorf("Concurrent validation error: %v", err)
	}

	// 验证缓存大小 (Verify cache size)
	size, capacity := rp.GetCacheStats()
	expectedSize := numGoroutines * numMessagesPerGoroutine
	if size != expectedSize {
		t.Errorf("Expected cache size %d, got %d", expectedSize, size)
	}

	t.Logf("Cache stats: size=%d, capacity=%d", size, capacity)
}

func TestReplayProtectorIsMessageSeen(t *testing.T) {
	rp, err := NewReplayProtector(nil)
	if err != nil {
		t.Fatalf("Failed to create replay protector: %v", err)
	}
	defer rp.Stop()

	messageID := "test-message"
	timestamp := time.Now().UnixNano()

	// 消息应该还没有被见过 (Message should not be seen yet)
	if rp.IsMessageSeen(messageID) {
		t.Error("Message should not be seen yet")
	}

	// 验证消息 (Validate message)
	err = rp.ValidateMessage(messageID, timestamp)
	if err != nil {
		t.Fatalf("Failed to validate message: %v", err)
	}

	// 现在消息应该被标记为已见过 (Now message should be marked as seen)
	if !rp.IsMessageSeen(messageID) {
		t.Error("Message should be marked as seen")
	}
}

func TestReplayProtectorClearCache(t *testing.T) {
	rp, err := NewReplayProtector(nil)
	if err != nil {
		t.Fatalf("Failed to create replay protector: %v", err)
	}
	defer rp.Stop()

	// 添加一些消息 (Add some messages)
	for i := 0; i < 5; i++ {
		messageID, err := GenerateSecureMessageID()
		if err != nil {
			t.Fatalf("Failed to generate message ID: %v", err)
		}

		err = rp.ValidateMessageWithID(messageID)
		if err != nil {
			t.Fatalf("Failed to validate message: %v", err)
		}
	}

	// 验证缓存不为空 (Verify cache is not empty)
	size, _ := rp.GetCacheStats()
	if size == 0 {
		t.Error("Cache should not be empty")
	}

	// 清空缓存 (Clear cache)
	rp.ClearCache()

	// 验证缓存已清空 (Verify cache is cleared)
	size, _ = rp.GetCacheStats()
	if size != 0 {
		t.Errorf("Cache should be empty, got size %d", size)
	}
}

func TestReplayProtectorCleanup(t *testing.T) {
	config := &ReplayProtectorConfig{
		TimeWindow:         100 * time.Millisecond, // 很短的时间窗口用于测试 (Very short time window for testing)
		CacheSize:          100,
		ClockSkewTolerance: 10 * time.Second,
		EnableCleanup:      true,
		CleanupInterval:    50 * time.Millisecond, // 很短的清理间隔 (Very short cleanup interval)
	}

	rp, err := NewReplayProtector(config)
	if err != nil {
		t.Fatalf("Failed to create replay protector: %v", err)
	}
	defer rp.Stop()

	// 添加一个消息 (Add a message)
	messageID, err := GenerateSecureMessageID()
	if err != nil {
		t.Fatalf("Failed to generate message ID: %v", err)
	}

	err = rp.ValidateMessageWithID(messageID)
	if err != nil {
		t.Fatalf("Failed to validate message: %v", err)
	}

	// 验证消息在缓存中 (Verify message is in cache)
	if !rp.IsMessageSeen(messageID) {
		t.Error("Message should be in cache")
	}

	// 等待清理发生 (Wait for cleanup to happen)
	time.Sleep(200 * time.Millisecond)

	// 验证消息已被清理 (Verify message has been cleaned up)
	if rp.IsMessageSeen(messageID) {
		t.Error("Message should have been cleaned up")
	}
}

func TestReplayProtectorUpdateConfig(t *testing.T) {
	rp, err := NewReplayProtector(nil)
	if err != nil {
		t.Fatalf("Failed to create replay protector: %v", err)
	}
	defer rp.Stop()

	// 添加一些消息 (Add some messages)
	for i := 0; i < 3; i++ {
		messageID, err := GenerateSecureMessageID()
		if err != nil {
			t.Fatalf("Failed to generate message ID: %v", err)
		}

		err = rp.ValidateMessageWithID(messageID)
		if err != nil {
			t.Fatalf("Failed to validate message: %v", err)
		}
	}

	// 更新配置 (Update configuration)
	newConfig := &ReplayProtectorConfig{
		TimeWindow:         2 * time.Minute,
		CacheSize:          200, // 增加缓存大小 (Increase cache size)
		ClockSkewTolerance: 20 * time.Second,
		EnableCleanup:      false,
	}

	err = rp.UpdateConfig(newConfig)
	if err != nil {
		t.Fatalf("Failed to update config: %v", err)
	}

	// 验证配置已更新 (Verify configuration is updated)
	if rp.config.CacheSize != 200 {
		t.Errorf("Expected cache size 200, got %d", rp.config.CacheSize)
	}

	// 验证现有数据仍然存在 (Verify existing data still exists)
	size, capacity := rp.GetCacheStats()
	if size != 3 {
		t.Errorf("Expected 3 messages in cache, got %d", size)
	}
	if capacity != 200 {
		t.Errorf("Expected capacity 200, got %d", capacity)
	}
}

func BenchmarkReplayProtectorValidateMessage(b *testing.B) {
	rp, err := NewReplayProtector(nil)
	if err != nil {
		b.Fatalf("Failed to create replay protector: %v", err)
	}
	defer rp.Stop()

	// 预生成消息ID (Pre-generate message IDs)
	messageIDs := make([]string, b.N)
	for i := 0; i < b.N; i++ {
		id, err := GenerateSecureMessageID()
		if err != nil {
			b.Fatalf("Failed to generate message ID: %v", err)
		}
		messageIDs[i] = id
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := rp.ValidateMessageWithID(messageIDs[i])
		if err != nil {
			b.Fatalf("Failed to validate message: %v", err)
		}
	}
}
