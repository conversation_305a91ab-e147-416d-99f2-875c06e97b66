package protocol

import (
	"fmt"
	"sync"
	"time"
)

// MessageValidator 高级消息验证器 (Advanced message validator)
type MessageValidator struct {
	// sequenceTracker 跟踪每个发送方的序列号 (Tracks sequence numbers for each sender)
	sequenceTracker map[string]uint64
	
	// mutex 保护序列号跟踪器 (Protects sequence number tracker)
	mutex sync.RWMutex
	
	// enableSequenceValidation 是否启用序列号验证 (Whether to enable sequence number validation)
	enableSequenceValidation bool
	
	// enableChecksumValidation 是否启用校验和验证 (Whether to enable checksum validation)
	enableChecksumValidation bool
	
	// enableReplayProtection 是否启用重放攻击防护 (Whether to enable replay protection)
	enableReplayProtection bool
	
	// maxSequenceGap 允许的最大序列号间隔 (Maximum allowed sequence number gap)
	maxSequenceGap uint64
}

// ValidatorConfig 验证器配置 (Validator configuration)
type ValidatorConfig struct {
	EnableSequenceValidation bool   // 启用序列号验证 (Enable sequence number validation)
	EnableChecksumValidation bool   // 启用校验和验证 (Enable checksum validation)
	EnableReplayProtection   bool   // 启用重放攻击防护 (Enable replay protection)
	MaxSequenceGap          uint64  // 最大序列号间隔 (Maximum sequence number gap)
}

// DefaultValidatorConfig 返回默认验证器配置 (Returns default validator configuration)
func DefaultValidatorConfig() *ValidatorConfig {
	return &ValidatorConfig{
		EnableSequenceValidation: true,
		EnableChecksumValidation: true,
		EnableReplayProtection:   true,
		MaxSequenceGap:          1000, // 允许最多1000的序列号间隔 (Allow up to 1000 sequence number gap)
	}
}

// NewMessageValidator 创建新的消息验证器 (Creates a new message validator)
func NewMessageValidator(config *ValidatorConfig) *MessageValidator {
	if config == nil {
		config = DefaultValidatorConfig()
	}
	
	return &MessageValidator{
		sequenceTracker:           make(map[string]uint64),
		enableSequenceValidation:  config.EnableSequenceValidation,
		enableChecksumValidation:  config.EnableChecksumValidation,
		enableReplayProtection:    config.EnableReplayProtection,
		maxSequenceGap:           config.MaxSequenceGap,
	}
}

// ValidateMessage 验证消息的所有方面 (Validates all aspects of the message)
func (v *MessageValidator) ValidateMessage(msg *ApplicationMessage) error {
	// 基本验证 (Basic validation)
	if err := msg.Validate(); err != nil {
		return fmt.Errorf("basic validation failed: %w", err)
	}
	
	// 校验和验证 (Checksum validation)
	if v.enableChecksumValidation {
		if err := msg.ValidateChecksum(); err != nil {
			return fmt.Errorf("checksum validation failed: %w", err)
		}
	}
	
	// 序列号验证 (Sequence number validation)
	if v.enableSequenceValidation {
		if err := v.validateSequenceNumber(msg); err != nil {
			return fmt.Errorf("sequence number validation failed: %w", err)
		}
	}
	
	// 重放攻击防护 (Replay protection)
	if v.enableReplayProtection {
		if err := msg.ValidateWithReplayProtection(); err != nil {
			return fmt.Errorf("replay protection failed: %w", err)
		}
	}
	
	return nil
}

// validateSequenceNumber 验证消息的序列号 (Validates the sequence number of the message)
func (v *MessageValidator) validateSequenceNumber(msg *ApplicationMessage) error {
	seqNum, hasSeqNum := msg.GetSequenceNumber()
	if !hasSeqNum {
		// 如果消息没有序列号，跳过验证 (If message has no sequence number, skip validation)
		return nil
	}
	
	v.mutex.Lock()
	defer v.mutex.Unlock()
	
	lastSeqNum, exists := v.sequenceTracker[msg.From]
	if !exists {
		// 第一次收到来自此发送方的消息 (First message from this sender)
		v.sequenceTracker[msg.From] = seqNum
		return nil
	}
	
	// 检查序列号是否递增 (Check if sequence number is incrementing)
	if seqNum <= lastSeqNum {
		return fmt.Errorf("sequence number %d is not greater than last sequence number %d for sender %s", 
			seqNum, lastSeqNum, msg.From)
	}
	
	// 检查序列号间隔是否过大 (Check if sequence number gap is too large)
	if seqNum-lastSeqNum > v.maxSequenceGap {
		return fmt.Errorf("sequence number gap %d exceeds maximum allowed gap %d for sender %s", 
			seqNum-lastSeqNum, v.maxSequenceGap, msg.From)
	}
	
	// 更新序列号跟踪器 (Update sequence number tracker)
	v.sequenceTracker[msg.From] = seqNum
	return nil
}

// GetLastSequenceNumber 获取指定发送方的最后序列号 (Gets the last sequence number for the specified sender)
func (v *MessageValidator) GetLastSequenceNumber(sender string) (uint64, bool) {
	v.mutex.RLock()
	defer v.mutex.RUnlock()
	
	seqNum, exists := v.sequenceTracker[sender]
	return seqNum, exists
}

// ResetSequenceTracker 重置指定发送方的序列号跟踪 (Resets sequence number tracking for the specified sender)
func (v *MessageValidator) ResetSequenceTracker(sender string) {
	v.mutex.Lock()
	defer v.mutex.Unlock()
	
	delete(v.sequenceTracker, sender)
}

// ClearSequenceTracker 清空所有序列号跟踪 (Clears all sequence number tracking)
func (v *MessageValidator) ClearSequenceTracker() {
	v.mutex.Lock()
	defer v.mutex.Unlock()
	
	v.sequenceTracker = make(map[string]uint64)
}

// GetSequenceTrackerStats 获取序列号跟踪器统计信息 (Gets sequence number tracker statistics)
func (v *MessageValidator) GetSequenceTrackerStats() map[string]uint64 {
	v.mutex.RLock()
	defer v.mutex.RUnlock()
	
	stats := make(map[string]uint64)
	for sender, seqNum := range v.sequenceTracker {
		stats[sender] = seqNum
	}
	return stats
}

// UpdateConfig 更新验证器配置 (Updates validator configuration)
func (v *MessageValidator) UpdateConfig(config *ValidatorConfig) {
	v.mutex.Lock()
	defer v.mutex.Unlock()
	
	v.enableSequenceValidation = config.EnableSequenceValidation
	v.enableChecksumValidation = config.EnableChecksumValidation
	v.enableReplayProtection = config.EnableReplayProtection
	v.maxSequenceGap = config.MaxSequenceGap
}

// ValidateMessageBatch 批量验证消息 (Validates messages in batch)
func (v *MessageValidator) ValidateMessageBatch(messages []*ApplicationMessage) []error {
	errors := make([]error, len(messages))
	
	for i, msg := range messages {
		errors[i] = v.ValidateMessage(msg)
	}
	
	return errors
}

// MessageIntegrityChecker 消息完整性检查器 (Message integrity checker)
type MessageIntegrityChecker struct {
	// 可以添加更多配置选项 (Can add more configuration options)
}

// NewMessageIntegrityChecker 创建新的消息完整性检查器 (Creates a new message integrity checker)
func NewMessageIntegrityChecker() *MessageIntegrityChecker {
	return &MessageIntegrityChecker{}
}

// CheckMessageIntegrity 检查消息完整性 (Checks message integrity)
func (mic *MessageIntegrityChecker) CheckMessageIntegrity(msg *ApplicationMessage) error {
	// 检查必填字段 (Check required fields)
	if err := msg.Validate(); err != nil {
		return err
	}
	
	// 检查时间戳合理性 (Check timestamp reasonableness)
	now := time.Now().Unix()
	msgTime := msg.Timestamp / 1e9 // 转换纳秒到秒 (Convert nanoseconds to seconds)
	
	// 检查消息是否来自未来 (Check if message is from future)
	if msgTime > now+300 { // 允许5分钟的时钟偏差 (Allow 5 minutes clock skew)
		return fmt.Errorf("message timestamp %d is too far in the future (current: %d)", msgTime, now)
	}
	
	// 检查消息是否过于陈旧 (Check if message is too old)
	if now-msgTime > 3600 { // 1小时 (1 hour)
		return fmt.Errorf("message timestamp %d is too old (current: %d)", msgTime, now)
	}
	
	// 检查校验和 (Check checksum)
	if err := msg.ValidateChecksum(); err != nil {
		return err
	}
	
	return nil
}

// SequenceNumberManager 序列号管理器 (Sequence number manager)
type SequenceNumberManager struct {
	// 可以添加更多功能 (Can add more features)
}

// NewSequenceNumberManager 创建新的序列号管理器 (Creates a new sequence number manager)
func NewSequenceNumberManager() *SequenceNumberManager {
	return &SequenceNumberManager{}
}

// PrepareMessage 为消息准备序列号和校验和 (Prepares sequence number and checksum for message)
func (snm *SequenceNumberManager) PrepareMessage(msg *ApplicationMessage) {
	// 生成并设置序列号 (Generate and set sequence number)
	msg.GenerateAndSetSequenceNumber()
	
	// 生成并设置校验和 (Generate and set checksum)
	msg.GenerateAndSetChecksum()
}
