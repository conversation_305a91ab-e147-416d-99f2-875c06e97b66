package config

import (
	"os"
	"testing"
	"time"
)

func TestSecurityConfigDefaults(t *testing.T) {
	// 创建配置管理器，不指定配置文件以使用默认值 (Create config manager without config file to use defaults)
	manager := NewManager("")

	// 加载默认配置 (Load default configuration)
	err := manager.Load()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}
	config := manager.Get()

	// 验证加密配置默认值 (Verify encryption config defaults)
	if !config.Security.Encryption.Enabled {
		t.Error("Encryption should be enabled by default")
	}
	if config.Security.Encryption.Algorithm != "AES-256-GCM" {
		t.E<PERSON>rf("Expected algorithm AES-256-GCM, got %s", config.Security.Encryption.Algorithm)
	}
	if config.Security.Encryption.KeySize != 256 {
		t.<PERSON><PERSON>("Expected key size 256, got %d", config.Security.Encryption.KeySize)
	}

	// 验证密钥管理配置默认值 (Verify key manager config defaults)
	if !config.Security.KeyManager.Enabled {
		t.Error("Key manager should be enabled by default")
	}
	if config.Security.KeyManager.RotationInterval != 24*time.Hour {
		t.Errorf("Expected rotation interval 24h, got %v", config.Security.KeyManager.RotationInterval)
	}
	if config.Security.KeyManager.KeyTTL != 72*time.Hour {
		t.Errorf("Expected key TTL 72h, got %v", config.Security.KeyManager.KeyTTL)
	}
	if config.Security.KeyManager.MaxKeyHistory != 10 {
		t.Errorf("Expected max key history 10, got %d", config.Security.KeyManager.MaxKeyHistory)
	}
	if !config.Security.KeyManager.AutoRotate {
		t.Error("Auto rotate should be enabled by default")
	}

	// 验证认证配置默认值 (Verify auth config defaults)
	if !config.Security.Auth.Enabled {
		t.Error("Auth should be enabled by default")
	}
	if config.Security.Auth.TokenTTL != time.Hour {
		t.Errorf("Expected token TTL 1h, got %v", config.Security.Auth.TokenTTL)
	}
	if config.Security.Auth.RefreshTokenTTL != 24*time.Hour {
		t.Errorf("Expected refresh token TTL 24h, got %v", config.Security.Auth.RefreshTokenTTL)
	}
	if config.Security.Auth.MaxLoginAttempts != 5 {
		t.Errorf("Expected max login attempts 5, got %d", config.Security.Auth.MaxLoginAttempts)
	}
	if config.Security.Auth.LockoutDuration != 15*time.Minute {
		t.Errorf("Expected lockout duration 15m, got %v", config.Security.Auth.LockoutDuration)
	}

	// 验证重放攻击防护配置默认值 (Verify replay protection config defaults)
	if !config.Security.ReplayProtection.Enabled {
		t.Error("Replay protection should be enabled by default")
	}
	if config.Security.ReplayProtection.TimeWindow != 5*time.Minute {
		t.Errorf("Expected time window 5m, got %v", config.Security.ReplayProtection.TimeWindow)
	}
	if config.Security.ReplayProtection.NonceCache != 10000 {
		t.Errorf("Expected nonce cache 10000, got %d", config.Security.ReplayProtection.NonceCache)
	}

	// 验证消息完整性配置默认值 (Verify message integrity config defaults)
	if !config.Security.MessageIntegrity.Enabled {
		t.Error("Message integrity should be enabled by default")
	}
	if config.Security.MessageIntegrity.ChecksumAlgorithm != "SHA256" {
		t.Errorf("Expected checksum algorithm SHA256, got %s", config.Security.MessageIntegrity.ChecksumAlgorithm)
	}
	if !config.Security.MessageIntegrity.EnableSequenceValidation {
		t.Error("Sequence validation should be enabled by default")
	}
	if config.Security.MessageIntegrity.MaxSequenceGap != 100 {
		t.Errorf("Expected max sequence gap 100, got %d", config.Security.MessageIntegrity.MaxSequenceGap)
	}

	// 验证速率限制配置默认值 (Verify rate limiting config defaults)
	if !config.Security.RateLimiting.Enabled {
		t.Error("Rate limiting should be enabled by default")
	}
	if config.Security.RateLimiting.RequestsPerSec != 100 {
		t.Errorf("Expected requests per sec 100, got %d", config.Security.RateLimiting.RequestsPerSec)
	}
	if config.Security.RateLimiting.BurstSize != 200 {
		t.Errorf("Expected burst size 200, got %d", config.Security.RateLimiting.BurstSize)
	}
}

func TestSecurityConfigEnvironmentOverrides(t *testing.T) {
	// 设置环境变量 (Set environment variables)
	os.Setenv("MONITOR_SECURITY_ENCRYPTION_ALGORITHM", "AES-192-GCM")
	os.Setenv("MONITOR_SECURITY_KEY_MANAGER_ROTATION_INTERVAL", "12h")
	os.Setenv("MONITOR_SECURITY_AUTH_TOKEN_TTL", "30m")
	os.Setenv("MONITOR_SECURITY_REPLAY_PROTECTION_TIME_WINDOW", "3m")
	os.Setenv("MONITOR_SECURITY_MESSAGE_INTEGRITY_CHECKSUM_ALGORITHM", "SHA512")
	os.Setenv("MONITOR_SECURITY_RATE_LIMITING_REQUESTS_PER_SEC", "50")
	// 设置必需的配置以通过验证 (Set required configs to pass validation)
	os.Setenv("MONITOR_SECURITY_KEY_MANAGER_MASTER_PASSWORD", "test-password")

	defer func() {
		// 清理环境变量 (Clean up environment variables)
		os.Unsetenv("MONITOR_SECURITY_ENCRYPTION_ALGORITHM")
		os.Unsetenv("MONITOR_SECURITY_KEY_MANAGER_ROTATION_INTERVAL")
		os.Unsetenv("MONITOR_SECURITY_AUTH_TOKEN_TTL")
		os.Unsetenv("MONITOR_SECURITY_REPLAY_PROTECTION_TIME_WINDOW")
		os.Unsetenv("MONITOR_SECURITY_MESSAGE_INTEGRITY_CHECKSUM_ALGORITHM")
		os.Unsetenv("MONITOR_SECURITY_RATE_LIMITING_REQUESTS_PER_SEC")
		os.Unsetenv("MONITOR_SECURITY_KEY_MANAGER_MASTER_PASSWORD")
	}()

	// 创建配置管理器 (Create config manager)
	manager := NewManager("")

	// 加载配置 (Load configuration)
	err := manager.Load()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}
	config := manager.Get()

	// 验证环境变量覆盖 (Verify environment variable overrides)
	if config.Security.Encryption.Algorithm != "AES-192-GCM" {
		t.Errorf("Expected algorithm AES-192-GCM, got %s", config.Security.Encryption.Algorithm)
	}
	if config.Security.KeyManager.RotationInterval != 12*time.Hour {
		t.Errorf("Expected rotation interval 12h, got %v", config.Security.KeyManager.RotationInterval)
	}
	if config.Security.Auth.TokenTTL != 30*time.Minute {
		t.Errorf("Expected token TTL 30m, got %v", config.Security.Auth.TokenTTL)
	}
	if config.Security.ReplayProtection.TimeWindow != 3*time.Minute {
		t.Errorf("Expected time window 3m, got %v", config.Security.ReplayProtection.TimeWindow)
	}
	if config.Security.MessageIntegrity.ChecksumAlgorithm != "SHA512" {
		t.Errorf("Expected checksum algorithm SHA512, got %s", config.Security.MessageIntegrity.ChecksumAlgorithm)
	}
	if config.Security.RateLimiting.RequestsPerSec != 50 {
		t.Errorf("Expected requests per sec 50, got %d", config.Security.RateLimiting.RequestsPerSec)
	}
}

func TestSecurityConfigValidation(t *testing.T) {
	testCases := []struct {
		name         string
		modifyConfig func(*Config)
		expectError  bool
		errorField   string
	}{
		{
			name: "Valid configuration",
			modifyConfig: func(c *Config) {
				// 设置必需的配置字段 (Set required config fields)
				c.Security.KeyManager.MasterPassword = "test-password-123"
				c.Servers = []ServerConfig{
					{Name: "test-server", IP: "127.0.0.1", Port: 8080, Priority: 1},
				}
			},
			expectError: false,
		},
		{
			name: "Invalid encryption algorithm",
			modifyConfig: func(c *Config) {
				c.Security.Encryption.Enabled = true
				c.Security.Encryption.Algorithm = "INVALID"
			},
			expectError: true,
			errorField:  "security.encryption.algorithm",
		},
		{
			name: "Invalid key size",
			modifyConfig: func(c *Config) {
				c.Security.Encryption.Enabled = true
				c.Security.Encryption.KeySize = 64 // 无效的密钥大小 (Invalid key size)
			},
			expectError: true,
			errorField:  "security.encryption.key_size",
		},
		{
			name: "Key TTL less than rotation interval",
			modifyConfig: func(c *Config) {
				c.Security.KeyManager.Enabled = true
				c.Security.KeyManager.RotationInterval = 24 * time.Hour
				c.Security.KeyManager.KeyTTL = 12 * time.Hour // 小于轮换间隔 (Less than rotation interval)
			},
			expectError: true,
			errorField:  "security.key_manager.key_ttl",
		},
		{
			name: "Missing master password",
			modifyConfig: func(c *Config) {
				c.Security.KeyManager.Enabled = true
				c.Security.KeyManager.MasterPassword = "" // 缺少主密码 (Missing master password)
			},
			expectError: true,
			errorField:  "security.key_manager.master_password",
		},
		{
			name: "Token TTL too long",
			modifyConfig: func(c *Config) {
				c.Security.Auth.Enabled = true
				c.Security.Auth.TokenTTL = 25 * time.Hour // 超过24小时 (Exceeds 24 hours)
			},
			expectError: true,
			errorField:  "security.auth.token_ttl",
		},
		{
			name: "Invalid checksum algorithm",
			modifyConfig: func(c *Config) {
				c.Security.MessageIntegrity.Enabled = true
				c.Security.MessageIntegrity.ChecksumAlgorithm = "MD5" // 不安全的算法 (Insecure algorithm)
			},
			expectError: true,
			errorField:  "security.message_integrity.checksum_algorithm",
		},
		{
			name: "Burst size less than requests per sec",
			modifyConfig: func(c *Config) {
				c.Security.RateLimiting.Enabled = true
				c.Security.RateLimiting.RequestsPerSec = 100
				c.Security.RateLimiting.BurstSize = 50 // 小于每秒请求数 (Less than requests per sec)
			},
			expectError: true,
			errorField:  "security.rate_limiting.burst_size",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建配置管理器 (Create config manager)
			manager := NewManager("")

			// 加载默认配置 (Load default configuration)
			err := manager.Load()
			if err != nil {
				t.Fatalf("Failed to load config: %v", err)
			}
			config := manager.Get()

			// 修改配置 (Modify configuration)
			tc.modifyConfig(config)

			// 验证配置 (Validate configuration)
			err = config.Validate()

			if tc.expectError {
				if err == nil {
					t.Errorf("Expected validation error for %s, but got none", tc.name)
				} else if tc.errorField != "" {
					if ve, ok := err.(ValidationErrors); ok {
						found := false
						for _, e := range ve {
							if e.Field == tc.errorField {
								found = true
								break
							}
						}
						if !found {
							t.Errorf("Expected validation error for field %s, but got: %v", tc.errorField, err)
						}
					} else {
						t.Errorf("Expected ValidationErrors, but got: %T", err)
					}
				}
			} else {
				if err != nil {
					t.Errorf("Expected no validation error for %s, but got: %v", tc.name, err)
				}
			}
		})
	}
}
