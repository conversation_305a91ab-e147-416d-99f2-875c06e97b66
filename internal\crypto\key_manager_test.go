package crypto

import (
	"bytes"
	"testing"
	"time"
)

func TestNewKeyManager(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    5,
		AutoRotate:       false, // 禁用自动轮换以便测试 (Disable auto rotation for testing)
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 验证初始密钥已生成 (Verify initial key is generated)
	currentKey := km.GetCurrentKey()
	if currentKey == nil {
		t.Error("Current key should not be nil")
	}

	if currentKey.Version != 1 {
		t.<PERSON>rrorf("Expected initial key version 1, got %d", currentKey.Version)
	}

	if !currentKey.IsValid() {
		t.<PERSON>r("Initial key should be valid")
	}
}

func TestNewKeyManagerWithoutMasterPassword(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    5,
		AutoRotate:       false,
		MasterPassword:   nil, // 没有主密码 (No master password)
	}

	_, err := NewKeyManager(config)
	if err == nil {
		t.Error("Expected error when creating key manager without master password")
	}
}

func TestKeyManagerRotateKey(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    5,
		AutoRotate:       false,
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 获取初始密钥 (Get initial key)
	initialKey := km.GetCurrentKey()
	initialVersion := initialKey.Version

	// 轮换密钥 (Rotate key)
	err = km.RotateKey()
	if err != nil {
		t.Fatalf("Failed to rotate key: %v", err)
	}

	// 验证新密钥 (Verify new key)
	newKey := km.GetCurrentKey()
	if newKey == nil {
		t.Error("New key should not be nil")
	}

	if newKey.Version <= initialVersion {
		t.Errorf("New key version %d should be greater than initial version %d", newKey.Version, initialVersion)
	}

	// 验证旧密钥仍可用于解密 (Verify old key is still available for decryption)
	oldKey := km.GetKeyForDecryption(initialVersion)
	if oldKey == nil {
		t.Error("Old key should still be available for decryption")
	}

	if oldKey.Active {
		t.Error("Old key should not be active")
	}
}

func TestKeyManagerGetKeyForDecryption(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    5,
		AutoRotate:       false,
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 获取当前密钥版本 (Get current key version)
	currentVersion := km.GetKeyVersion()

	// 测试获取当前密钥 (Test getting current key)
	key := km.GetKeyForDecryption(currentVersion)
	if key == nil {
		t.Error("Should be able to get current key for decryption")
	}

	// 测试获取不存在的密钥 (Test getting non-existent key)
	nonExistentKey := km.GetKeyForDecryption(KeyVersion(999))
	if nonExistentKey != nil {
		t.Error("Should not be able to get non-existent key")
	}
}

func TestKeyManagerKeyExpiration(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           50 * time.Millisecond, // 很短的TTL用于测试 (Very short TTL for testing)
		MaxKeyHistory:    5,
		AutoRotate:       false,
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 获取初始密钥版本 (Get initial key version)
	initialVersion := km.GetKeyVersion()

	// 轮换密钥 (Rotate key)
	err = km.RotateKey()
	if err != nil {
		t.Fatalf("Failed to rotate key: %v", err)
	}

	// 等待密钥过期 (Wait for key to expire)
	time.Sleep(100 * time.Millisecond)

	// 尝试获取过期的密钥 (Try to get expired key)
	expiredKey := km.GetKeyForDecryption(initialVersion)
	if expiredKey != nil {
		t.Error("Should not be able to get expired key")
	}
}

func TestKeyManagerMaxKeyHistory(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    2, // 限制为2个历史密钥 (Limit to 2 historical keys)
		AutoRotate:       false,
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 记录初始版本 (Record initial version)
	initialVersion := km.GetKeyVersion()

	// 进行多次密钥轮换 (Perform multiple key rotations)
	for i := 0; i < 4; i++ {
		err = km.RotateKey()
		if err != nil {
			t.Fatalf("Failed to rotate key at iteration %d: %v", i, err)
		}
	}

	// 验证历史密钥数量不超过限制 (Verify historical keys don't exceed limit)
	stats := km.GetKeyStats()
	historicalCount, ok := stats["historical_keys_count"].(int)
	if !ok {
		t.Error("Failed to get historical keys count from stats")
	}

	if historicalCount > config.MaxKeyHistory {
		t.Errorf("Historical keys count %d exceeds limit %d", historicalCount, config.MaxKeyHistory)
	}

	// 验证最旧的密钥已被清理 (Verify oldest key has been cleaned up)
	oldestKey := km.GetKeyForDecryption(initialVersion)
	if oldestKey != nil {
		t.Error("Oldest key should have been cleaned up")
	}
}

func TestKeyManagerGetKeyStats(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    5,
		AutoRotate:       false,
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 轮换几次密钥 (Rotate keys a few times)
	for i := 0; i < 3; i++ {
		err = km.RotateKey()
		if err != nil {
			t.Fatalf("Failed to rotate key: %v", err)
		}
	}

	// 获取统计信息 (Get statistics)
	stats := km.GetKeyStats()

	// 验证统计信息 (Verify statistics)
	requiredFields := []string{
		"current_key_version",
		"current_key_created_at",
		"current_key_expires_at",
		"current_key_valid",
		"historical_keys_count",
		"next_version",
		"auto_rotate_enabled",
		"rotation_interval",
		"valid_historical_keys",
	}

	for _, field := range requiredFields {
		if _, exists := stats[field]; !exists {
			t.Errorf("Missing required field in stats: %s", field)
		}
	}

	// 验证历史密钥数量 (Verify historical keys count)
	historicalCount, ok := stats["historical_keys_count"].(int)
	if !ok || historicalCount != 3 {
		t.Errorf("Expected 3 historical keys, got %v", stats["historical_keys_count"])
	}
}

func TestKeyManagerAutoRotation(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: 50 * time.Millisecond, // 很短的间隔用于测试 (Very short interval for testing)
		KeyTTL:           time.Hour,
		MaxKeyHistory:    5,
		AutoRotate:       true, // 启用自动轮换 (Enable auto rotation)
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 记录初始版本 (Record initial version)
	initialVersion := km.GetKeyVersion()

	// 等待自动轮换发生 (Wait for auto rotation to occur)
	time.Sleep(150 * time.Millisecond)

	// 验证密钥已自动轮换 (Verify key has been auto-rotated)
	newVersion := km.GetKeyVersion()
	if newVersion <= initialVersion {
		t.Errorf("Key should have been auto-rotated, initial: %d, current: %d", initialVersion, newVersion)
	}
}

func TestKeyManagerUpdateConfig(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    5,
		AutoRotate:       false,
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 更新配置 (Update configuration)
	newConfig := &KeyManagerConfig{
		RotationInterval: 2 * time.Hour,
		KeyTTL:           48 * time.Hour,
		MaxKeyHistory:    10,
		AutoRotate:       true,
		MasterPassword:   []byte("new-master-password"),
	}

	err = km.UpdateConfig(newConfig)
	if err != nil {
		t.Fatalf("Failed to update config: %v", err)
	}

	// 验证配置已更新 (Verify configuration is updated)
	stats := km.GetKeyStats()
	rotationInterval, ok := stats["rotation_interval"].(string)
	if !ok || rotationInterval != "2h0m0s" {
		t.Errorf("Expected rotation interval '2h0m0s', got %v", stats["rotation_interval"])
	}
}

func TestKeyManagerEncryptDecryptIntegration(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    5,
		AutoRotate:       false,
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 测试数据 (Test data)
	plaintext := []byte("Hello, World! This is a test message for encryption.")

	// 使用当前密钥加密 (Encrypt with current key)
	currentKey := km.GetCurrentKey()
	if currentKey == nil {
		t.Fatal("Current key should not be nil")
	}

	ciphertext, err := Encrypt(currentKey.Data, plaintext)
	if err != nil {
		t.Fatalf("Failed to encrypt: %v", err)
	}

	// 轮换密钥 (Rotate key)
	err = km.RotateKey()
	if err != nil {
		t.Fatalf("Failed to rotate key: %v", err)
	}

	// 使用旧密钥解密 (Decrypt with old key)
	oldKey := km.GetKeyForDecryption(currentKey.Version)
	if oldKey == nil {
		t.Fatal("Old key should still be available")
	}

	decrypted, err := Decrypt(oldKey.Data, ciphertext)
	if err != nil {
		t.Fatalf("Failed to decrypt with old key: %v", err)
	}

	// 验证解密结果 (Verify decryption result)
	if !bytes.Equal(plaintext, decrypted) {
		t.Errorf("Decrypted text doesn't match original. Expected: %s, Got: %s", plaintext, decrypted)
	}
}

func TestEncryptionService(t *testing.T) {
	config := &KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    5,
		AutoRotate:       false,
		MasterPassword:   []byte("test-master-password"),
	}

	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	es := NewEncryptionService(km)

	// 测试数据 (Test data)
	plaintext := []byte("Hello, World! This is a test message for encryption service.")

	// 加密数据 (Encrypt data)
	encData, err := es.EncryptData(plaintext)
	if err != nil {
		t.Fatalf("Failed to encrypt data: %v", err)
	}

	// 验证加密数据结构 (Verify encrypted data structure)
	if encData.KeyVersion == 0 {
		t.Error("Key version should not be zero")
	}
	if encData.Timestamp == 0 {
		t.Error("Timestamp should not be zero")
	}
	if len(encData.Data) == 0 {
		t.Error("Encrypted data should not be empty")
	}

	// 解密数据 (Decrypt data)
	decrypted, err := es.DecryptData(encData)
	if err != nil {
		t.Fatalf("Failed to decrypt data: %v", err)
	}

	// 验证解密结果 (Verify decryption result)
	if !bytes.Equal(plaintext, decrypted) {
		t.Errorf("Decrypted text doesn't match original. Expected: %s, Got: %s", plaintext, decrypted)
	}

	// 轮换密钥后仍能解密 (Can still decrypt after key rotation)
	err = km.RotateKey()
	if err != nil {
		t.Fatalf("Failed to rotate key: %v", err)
	}

	// 使用旧密钥版本解密 (Decrypt with old key version)
	decrypted2, err := es.DecryptData(encData)
	if err != nil {
		t.Fatalf("Failed to decrypt with old key: %v", err)
	}

	if !bytes.Equal(plaintext, decrypted2) {
		t.Errorf("Decrypted text with old key doesn't match original. Expected: %s, Got: %s", plaintext, decrypted2)
	}
}
