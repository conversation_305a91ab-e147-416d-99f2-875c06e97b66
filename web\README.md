# 服务器监控系统 Web 界面

这是一个基于 Tailwind CSS 的现代化服务器监控仪表板，用于显示12台服务器的CPU和内存使用情况。

## 文件结构

```
web/
├── example.html              # 完整的示例页面（独立运行）
├── templates/                # Go 模板文件
│   ├── layouts/
│   │   └── base.html        # 基础布局模板
│   ├── pages/
│   │   └── dashboard.html   # 仪表板页面模板
│   ├── components/
│   │   └── server-card.html # 服务器卡片组件
│   └── index.html           # 主页面模板
├── static/                  # 静态资源
│   ├── css/
│   │   └── output.css       # Tailwind 编译后的 CSS
│   └── js/
│       ├── dashboard.js     # 仪表板 JavaScript
│       ├── charts.js        # 图表功能
│       ├── websocket.js     # WebSocket 客户端
│       └── services.js      # 服务管理
└── README.md               # 说明文档
```

## 快速预览

### 方法1：直接打开示例文件
```bash
# 在浏览器中打开
open web/example.html
# 或者
firefox web/example.html
# 或者
chrome web/example.html
```

### 方法2：使用简单的HTTP服务器
```bash
# 使用 Python
cd web
python -m http.server 8000

# 使用 Node.js
cd web
npx http-server

# 然后在浏览器中访问
# http://localhost:8000/example.html
```

## 功能特性

### 🎨 界面设计
- **白色渐变背景**：使用 Tailwind 的 `bg-gradient-to-br from-white via-gray-50 to-gray-100`
- **响应式布局**：支持手机、平板、桌面设备
- **现代化卡片设计**：圆角、阴影、悬停效果

### 📊 数据展示
- **12台服务器监控**：每台服务器独立的监控卡片
- **CPU/内存进度条**：实时显示使用率，支持颜色变化
- **状态指示器**：在线（绿色）、警告（黄色）、离线（红色）
- **统计概览**：总服务器数、在线数量、警告数量、平均CPU使用率

### 🔄 实时更新
- **自动数据刷新**：每5秒自动更新服务器数据
- **平滑动画**：进度条变化使用 CSS 过渡效果
- **时间显示**：实时更新当前时间

### 🔍 交互功能
- **搜索功能**：按服务器名称、主机名、IP地址搜索
- **状态过滤**：按在线/警告/离线状态过滤
- **详情模态框**：点击服务器卡片查看详细信息
- **控制面板**：重启、日志、系统信息、服务管理按钮

### 📱 用户体验
- **加载动画**：页面加载时的动画效果
- **错误处理**：连接失败时的错误提示
- **键盘支持**：ESC键关闭模态框
- **点击外部关闭**：点击模态框外部区域关闭

## 技术实现

### CSS 框架
- **Tailwind CSS**：使用 CDN 版本，包含所有 utility classes
- **自定义动画**：进度条动画、卡片悬停效果、加载动画

### JavaScript 功能
- **数据模拟**：12台服务器的随机数据生成
- **DOM 操作**：动态生成服务器卡片
- **事件处理**：搜索、过滤、模态框交互
- **定时器**：自动数据更新和时间显示

### 数据结构
```javascript
const serverData = {
    id: 1,
    name: 'Web-Server-01',
    ip: '*************',
    hostname: 'web01.example.com',
    os: 'Ubuntu 20.04',
    status: 'online',  // online, warning, offline
    cpu: 35,           // 0-100
    memory: 68         // 0-100
};
```

## 集成到 Go 项目

### 1. 模板集成
将 `templates/` 目录下的文件复制到项目的 `web/templates/` 目录。

### 2. 静态资源
确保 Tailwind CSS 已编译到 `web/static/css/output.css`。

### 3. 路由配置
在 Go 代码中配置相应的路由：
```go
router.GET("/", indexPage)
router.GET("/servers/:id", serverDetailPage)
router.Static("/static", "./web/static")
```

### 4. 数据传递
在 Go 处理函数中传递数据：
```go
data := gin.H{
    "title":         "服务器监控系统",
    "totalServers":  12,
    "activeServers": 10,
    "timestamp":     time.Now().Format("2006-01-02 15:04:05"),
}
c.HTML(http.StatusOK, "index.html", data)
```

## 自定义配置

### 修改服务器数量
在 `example.html` 中修改 `serversData` 数组，添加或删除服务器数据。

### 调整更新频率
修改定时器间隔：
```javascript
// 每5秒更新数据
setInterval(simulateDataUpdate, 5000);
```

### 自定义样式
在 `<style>` 标签中添加自定义 CSS，或修改 Tailwind classes。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

MIT License
