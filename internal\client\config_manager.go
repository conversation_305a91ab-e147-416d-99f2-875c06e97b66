package client

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"gopkg.in/yaml.v3"
	"server-monitor/internal/logger"
	"server-monitor/internal/utils"
)

// ClientConfig 客户端配置
type ClientConfig struct {
	// 基本配置
	Name        string `yaml:"name"`         // 客户端名称
	Description string `yaml:"description"`  // 客户端描述
	Version     string `yaml:"version"`      // 客户端版本
	
	// 服务器配置
	Server ServerConfig `yaml:"server"`
	
	// 监控配置
	Monitor MonitorConfig `yaml:"monitor"`
	
	// 日志配置
	Log LogConfig `yaml:"log"`
	
	// 网络配置
	Network NetworkConfig `yaml:"network"`
	
	// 安全配置
	Security SecurityConfig `yaml:"security"`
	
	// 高级配置
	Advanced AdvancedConfig `yaml:"advanced"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host     string `yaml:"host"`      // 服务器地址
	Port     int    `yaml:"port"`      // 服务器端口
	Protocol string `yaml:"protocol"`  // 协议 (http/https)
	APIKey   string `yaml:"api_key"`   // API密钥
	Timeout  string `yaml:"timeout"`   // 请求超时
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	Enabled         bool   `yaml:"enabled"`          // 是否启用监控
	Interval        string `yaml:"interval"`         // 监控间隔
	ReportInterval  string `yaml:"report_interval"`  // 上报间隔
	MetricsEnabled  bool   `yaml:"metrics_enabled"`  // 是否启用指标收集
	ServicesEnabled bool   `yaml:"services_enabled"` // 是否启用服务监控
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `yaml:"level"`       // 日志级别
	Format     string `yaml:"format"`      // 日志格式
	Output     string `yaml:"output"`      // 输出方式
	File       string `yaml:"file"`        // 日志文件
	MaxSize    int    `yaml:"max_size"`    // 最大文件大小(MB)
	MaxBackups int    `yaml:"max_backups"` // 最大备份数量
	MaxAge     int    `yaml:"max_age"`     // 最大保存天数
	Compress   bool   `yaml:"compress"`    // 是否压缩
}

// NetworkConfig 网络配置
type NetworkConfig struct {
	ConnectTimeout string `yaml:"connect_timeout"` // 连接超时
	ReadTimeout    string `yaml:"read_timeout"`    // 读取超时
	WriteTimeout   string `yaml:"write_timeout"`   // 写入超时
	MaxRetries     int    `yaml:"max_retries"`     // 最大重试次数
	RetryInterval  string `yaml:"retry_interval"`  // 重试间隔
	KeepAlive      bool   `yaml:"keep_alive"`      // 是否保持连接
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	TLSEnabled     bool   `yaml:"tls_enabled"`      // 是否启用TLS
	TLSSkipVerify  bool   `yaml:"tls_skip_verify"`  // 是否跳过TLS验证
	CertFile       string `yaml:"cert_file"`        // 证书文件
	KeyFile        string `yaml:"key_file"`         // 密钥文件
	CAFile         string `yaml:"ca_file"`          // CA文件
	EncryptionKey  string `yaml:"encryption_key"`   // 加密密钥
}

// AdvancedConfig 高级配置
type AdvancedConfig struct {
	WorkerCount    int    `yaml:"worker_count"`     // 工作协程数量
	BufferSize     int    `yaml:"buffer_size"`      // 缓冲区大小
	CacheEnabled   bool   `yaml:"cache_enabled"`    // 是否启用缓存
	CacheTTL       string `yaml:"cache_ttl"`        // 缓存TTL
	DebugMode      bool   `yaml:"debug_mode"`       // 调试模式
	ProfileEnabled bool   `yaml:"profile_enabled"`  // 是否启用性能分析
}

// ConfigManager 配置管理器
type ConfigManager struct {
	configPath string
	config     *ClientConfig
	mu         sync.RWMutex
	logger     *logger.Logger
	watchers   []ConfigWatcher
}

// ConfigWatcher 配置监听器
type ConfigWatcher interface {
	OnConfigChanged(config *ClientConfig)
}

// NewConfigManager 创建配置管理器
func NewConfigManager(configPath string) *ConfigManager {
	return &ConfigManager{
		configPath: configPath,
		watchers:   make([]ConfigWatcher, 0),
	}
}

// Load 加载配置
func (cm *ConfigManager) Load() error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	// 检查配置文件是否存在
	if !utils.FileExists(cm.configPath) {
		// 创建默认配置
		if err := cm.createDefaultConfig(); err != nil {
			return fmt.Errorf("创建默认配置失败: %w", err)
		}
	}
	
	// 读取配置文件
	data, err := os.ReadFile(cm.configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}
	
	// 解析配置
	config := &ClientConfig{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}
	
	// 验证配置
	if err := cm.validateConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	// 应用默认值
	cm.applyDefaults(config)
	
	cm.config = config
	
	// 通知监听器
	cm.notifyWatchers()
	
	return nil
}

// Save 保存配置
func (cm *ConfigManager) Save() error {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	if cm.config == nil {
		return fmt.Errorf("配置未加载")
	}
	
	// 序列化配置
	data, err := yaml.Marshal(cm.config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}
	
	// 创建目录
	dir := filepath.Dir(cm.configPath)
	if err := utils.CreateDirIfNotExists(dir); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}
	
	// 写入文件
	if err := os.WriteFile(cm.configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}
	
	return nil
}

// Get 获取配置
func (cm *ConfigManager) Get() *ClientConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	if cm.config == nil {
		return nil
	}
	
	// 返回配置副本
	config := *cm.config
	return &config
}

// Update 更新配置
func (cm *ConfigManager) Update(config *ClientConfig) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	// 验证配置
	if err := cm.validateConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	// 应用默认值
	cm.applyDefaults(config)
	
	cm.config = config
	
	// 保存配置
	if err := cm.Save(); err != nil {
		return fmt.Errorf("保存配置失败: %w", err)
	}
	
	// 通知监听器
	cm.notifyWatchers()
	
	return nil
}

// AddWatcher 添加配置监听器
func (cm *ConfigManager) AddWatcher(watcher ConfigWatcher) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.watchers = append(cm.watchers, watcher)
}

// createDefaultConfig 创建默认配置
func (cm *ConfigManager) createDefaultConfig() error {
	config := &ClientConfig{
		Name:        "server-monitor-client",
		Description: "服务器监控客户端",
		Version:     "1.0.0",
		
		Server: ServerConfig{
			Host:     "127.0.0.1",
			Port:     8443,
			Protocol: "http",
			Timeout:  "30s",
		},
		
		Monitor: MonitorConfig{
			Enabled:         true,
			Interval:        "30s",
			ReportInterval:  "60s",
			MetricsEnabled:  true,
			ServicesEnabled: true,
		},
		
		Log: LogConfig{
			Level:      "info",
			Format:     "text",
			Output:     "both",
			File:       "./logs/client.log",
			MaxSize:    50,
			MaxBackups: 5,
			MaxAge:     30,
			Compress:   true,
		},
		
		Network: NetworkConfig{
			ConnectTimeout: "10s",
			ReadTimeout:    "30s",
			WriteTimeout:   "30s",
			MaxRetries:     3,
			RetryInterval:  "5s",
			KeepAlive:      true,
		},
		
		Security: SecurityConfig{
			TLSEnabled:    false,
			TLSSkipVerify: false,
		},
		
		Advanced: AdvancedConfig{
			WorkerCount:    4,
			BufferSize:     1000,
			CacheEnabled:   true,
			CacheTTL:       "5m",
			DebugMode:      false,
			ProfileEnabled: false,
		},
	}
	
	// 序列化配置
	data, err := yaml.Marshal(config)
	if err != nil {
		return err
	}
	
	// 创建目录
	dir := filepath.Dir(cm.configPath)
	if err := utils.CreateDirIfNotExists(dir); err != nil {
		return err
	}
	
	// 写入文件
	return os.WriteFile(cm.configPath, data, 0644)
}

// validateConfig 验证配置
func (cm *ConfigManager) validateConfig(config *ClientConfig) error {
	if config.Name == "" {
		return fmt.Errorf("客户端名称不能为空")
	}
	
	if config.Server.Host == "" {
		return fmt.Errorf("服务器地址不能为空")
	}
	
	if !utils.IsValidPort(config.Server.Port) {
		return fmt.Errorf("服务器端口无效: %d", config.Server.Port)
	}
	
	if config.Server.Protocol != "http" && config.Server.Protocol != "https" {
		return fmt.Errorf("协议必须是 http 或 https")
	}
	
	// 验证时间间隔
	if _, err := utils.ParseDuration(config.Monitor.Interval); err != nil {
		return fmt.Errorf("监控间隔格式错误: %w", err)
	}
	
	if _, err := utils.ParseDuration(config.Monitor.ReportInterval); err != nil {
		return fmt.Errorf("上报间隔格式错误: %w", err)
	}
	
	return nil
}

// applyDefaults 应用默认值
func (cm *ConfigManager) applyDefaults(config *ClientConfig) {
	if config.Version == "" {
		config.Version = "1.0.0"
	}
	
	if config.Server.Protocol == "" {
		config.Server.Protocol = "http"
	}
	
	if config.Server.Timeout == "" {
		config.Server.Timeout = "30s"
	}
	
	if config.Monitor.Interval == "" {
		config.Monitor.Interval = "30s"
	}
	
	if config.Monitor.ReportInterval == "" {
		config.Monitor.ReportInterval = "60s"
	}
	
	if config.Log.Level == "" {
		config.Log.Level = "info"
	}
	
	if config.Log.Format == "" {
		config.Log.Format = "text"
	}
	
	if config.Log.Output == "" {
		config.Log.Output = "both"
	}
	
	if config.Advanced.WorkerCount <= 0 {
		config.Advanced.WorkerCount = 4
	}
	
	if config.Advanced.BufferSize <= 0 {
		config.Advanced.BufferSize = 1000
	}
}

// notifyWatchers 通知监听器
func (cm *ConfigManager) notifyWatchers() {
	for _, watcher := range cm.watchers {
		go watcher.OnConfigChanged(cm.config)
	}
}

// GetServerURL 获取服务器URL
func (cm *ConfigManager) GetServerURL() string {
	config := cm.Get()
	if config == nil {
		return ""
	}
	
	return fmt.Sprintf("%s://%s:%d", 
		config.Server.Protocol, 
		config.Server.Host, 
		config.Server.Port)
}

// GetMonitorInterval 获取监控间隔
func (cm *ConfigManager) GetMonitorInterval() time.Duration {
	config := cm.Get()
	if config == nil {
		return 30 * time.Second
	}
	
	duration, err := utils.ParseDuration(config.Monitor.Interval)
	if err != nil {
		return 30 * time.Second
	}
	
	return duration
}

// GetReportInterval 获取上报间隔
func (cm *ConfigManager) GetReportInterval() time.Duration {
	config := cm.Get()
	if config == nil {
		return 60 * time.Second
	}
	
	duration, err := utils.ParseDuration(config.Monitor.ReportInterval)
	if err != nil {
		return 60 * time.Second
	}
	
	return duration
}
