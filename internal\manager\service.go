package manager

import (
	"context"
	"fmt"
	"os/exec"
	"runtime"
	"strings"
	"time"
)

// ServiceStatus 服务状态
type ServiceStatus string

const (
	StatusRunning  ServiceStatus = "running"  // 运行中
	StatusStopped  ServiceStatus = "stopped"  // 已停止
	StatusFailed   ServiceStatus = "failed"   // 失败
	StatusUnknown  ServiceStatus = "unknown"  // 未知
	StatusDisabled ServiceStatus = "disabled" // 禁用
)

// Service 服务信息
type Service struct {
	Name        string        `json:"name"`         // 服务名称
	DisplayName string        `json:"display_name"` // 显示名称
	Status      ServiceStatus `json:"status"`       // 服务状态
	PID         int           `json:"pid"`          // 进程ID
	StartTime   *time.Time    `json:"start_time"`   // 启动时间
	Description string        `json:"description"`  // 服务描述
	Type        string        `json:"type"`         // 服务类型 (systemd, supervisord, docker, windows)
}

// ServiceManager 服务管理器接口
type ServiceManager interface {
	// 列出所有服务
	ListServices(ctx context.Context) ([]*Service, error)

	// 获取服务详情
	GetService(ctx context.Context, name string) (*Service, error)

	// 启动服务
	StartService(ctx context.Context, name string) error

	// 停止服务
	StopService(ctx context.Context, name string) error

	// 重启服务
	RestartService(ctx context.Context, name string) error

	// 启用服务
	EnableService(ctx context.Context, name string) error

	// 禁用服务
	DisableService(ctx context.Context, name string) error

	// 检查服务是否存在
	ServiceExists(ctx context.Context, name string) bool

	// 获取服务日志
	GetServiceLogs(ctx context.Context, name string, lines int) ([]string, error)
}

// Manager 统一服务管理器
type Manager struct {
	managers []ServiceManager
}

// NewManager 创建服务管理器
func NewManager() *Manager {
	m := &Manager{
		managers: make([]ServiceManager, 0),
	}

	// 根据操作系统添加相应的管理器
	switch runtime.GOOS {
	case "linux":
		// 添加systemd管理器
		if isSystemdAvailable() {
			m.managers = append(m.managers, NewSystemdManager())
		}
		// 添加supervisord管理器
		if isSupervisordAvailable() {
			m.managers = append(m.managers, NewSupervisordManager())
		}
		// 添加docker管理器
		if isDockerAvailable() {
			m.managers = append(m.managers, NewDockerManager())
		}
	case "windows":
		// 添加Windows服务管理器
		m.managers = append(m.managers, NewWindowsManager())
	case "darwin":
		// 添加launchd管理器（仅在macOS上可用）
		// m.managers = append(m.managers, NewLaunchdManager())
	}

	return m
}

// ListServices 列出所有服务
func (m *Manager) ListServices(ctx context.Context) ([]*Service, error) {
	var allServices []*Service

	for _, manager := range m.managers {
		services, err := manager.ListServices(ctx)
		if err != nil {
			continue // 忽略错误，继续下一个管理器
		}
		allServices = append(allServices, services...)
	}

	return allServices, nil
}

// GetService 获取服务详情
func (m *Manager) GetService(ctx context.Context, name string) (*Service, error) {
	for _, manager := range m.managers {
		if manager.ServiceExists(ctx, name) {
			return manager.GetService(ctx, name)
		}
	}
	return nil, fmt.Errorf("服务不存在: %s", name)
}

// StartService 启动服务
func (m *Manager) StartService(ctx context.Context, name string) error {
	for _, manager := range m.managers {
		if manager.ServiceExists(ctx, name) {
			return manager.StartService(ctx, name)
		}
	}
	return fmt.Errorf("服务不存在: %s", name)
}

// StopService 停止服务
func (m *Manager) StopService(ctx context.Context, name string) error {
	for _, manager := range m.managers {
		if manager.ServiceExists(ctx, name) {
			return manager.StopService(ctx, name)
		}
	}
	return fmt.Errorf("服务不存在: %s", name)
}

// RestartService 重启服务
func (m *Manager) RestartService(ctx context.Context, name string) error {
	for _, manager := range m.managers {
		if manager.ServiceExists(ctx, name) {
			return manager.RestartService(ctx, name)
		}
	}
	return fmt.Errorf("服务不存在: %s", name)
}

// EnableService 启用服务
func (m *Manager) EnableService(ctx context.Context, name string) error {
	for _, manager := range m.managers {
		if manager.ServiceExists(ctx, name) {
			return manager.EnableService(ctx, name)
		}
	}
	return fmt.Errorf("服务不存在: %s", name)
}

// DisableService 禁用服务
func (m *Manager) DisableService(ctx context.Context, name string) error {
	for _, manager := range m.managers {
		if manager.ServiceExists(ctx, name) {
			return manager.DisableService(ctx, name)
		}
	}
	return fmt.Errorf("服务不存在: %s", name)
}

// ServiceExists 检查服务是否存在
func (m *Manager) ServiceExists(ctx context.Context, name string) bool {
	for _, manager := range m.managers {
		if manager.ServiceExists(ctx, name) {
			return true
		}
	}
	return false
}

// GetServiceLogs 获取服务日志
func (m *Manager) GetServiceLogs(ctx context.Context, name string, lines int) ([]string, error) {
	for _, manager := range m.managers {
		if manager.ServiceExists(ctx, name) {
			return manager.GetServiceLogs(ctx, name, lines)
		}
	}
	return nil, fmt.Errorf("服务不存在: %s", name)
}

// 检查systemd是否可用
func isSystemdAvailable() bool {
	cmd := exec.Command("systemctl", "--version")
	return cmd.Run() == nil
}

// 检查supervisord是否可用
func isSupervisordAvailable() bool {
	cmd := exec.Command("supervisorctl", "version")
	return cmd.Run() == nil
}

// 检查docker是否可用
func isDockerAvailable() bool {
	cmd := exec.Command("docker", "version")
	return cmd.Run() == nil
}

// parseServiceStatus 解析服务状态
func parseServiceStatus(status string) ServiceStatus {
	status = strings.ToLower(strings.TrimSpace(status))

	switch {
	case strings.Contains(status, "active") || strings.Contains(status, "running"):
		return StatusRunning
	case strings.Contains(status, "inactive") || strings.Contains(status, "stopped"):
		return StatusStopped
	case strings.Contains(status, "failed") || strings.Contains(status, "error"):
		return StatusFailed
	case strings.Contains(status, "disabled"):
		return StatusDisabled
	default:
		return StatusUnknown
	}
}

// executeCommand 执行命令并返回输出
func executeCommand(ctx context.Context, name string, args ...string) (string, error) {
	cmd := exec.CommandContext(ctx, name, args...)
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(output)), nil
}

// executeCommandWithTimeout 执行命令并设置超时
func executeCommandWithTimeout(timeout time.Duration, name string, args ...string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	return executeCommand(ctx, name, args...)
}
