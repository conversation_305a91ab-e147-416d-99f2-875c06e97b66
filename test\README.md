# 测试协调器 (Test Coordinator)

## 概述 (Overview)

测试协调器是一个统一的测试管理工具，用于系统性地验证项目中各个模块的功能实现状态。它能够调度现有的测试程序，收集测试结果，并生成详细的验证报告。

## 功能特性 (Features)

- 🎯 **统一调度**: 统一管理和调度所有测试模块
- 📊 **结果聚合**: 收集和聚合各个测试程序的执行结果
- 🔄 **并行执行**: 支持并行和顺序两种执行模式
- 📝 **详细报告**: 生成JSON和文本格式的详细测试报告
- ⚙️ **灵活配置**: 支持选择性运行特定模块
- 🕐 **超时控制**: 为每个测试模块设置合理的超时时间

## 支持的测试模块 (Supported Test Modules)

### 阶段一：核心架构 (Stage 1: Core Architecture)
- **project-init**: 项目初始化和目录结构验证
- **cli-parsing**: 基础命令行参数解析验证
- **aes-encryption**: AES加密模块验证
- **communication-protocol**: 通信协议设计验证
- **database-model**: 数据库模型设计验证

### 阶段二：客户端开发 (Stage 2: Client Development)
- **system-monitor**: 系统监控模块验证
- **service-manager**: 服务管理模块验证
- **data-reporter**: 数据上报功能验证
- **main-client**: 客户端主程序验证

### 综合测试 (Comprehensive Tests)
- **comprehensive**: 综合功能测试

## 使用方法 (Usage)

### 基本用法 (Basic Usage)

```bash
# 运行所有测试模块
go run ./test/test.go

# 详细输出模式
go run ./test/test.go --verbose

# 并行执行测试
go run ./test/test.go --parallel

# 指定超时时间
go run ./test/test.go --timeout 15m
```

### 选择性运行 (Selective Execution)

```bash
# 只运行阶段一的测试
go run ./test/test.go --modules "project-init,cli-parsing,aes-encryption,communication-protocol,database-model"

# 只运行特定模块
go run ./test/test.go --modules "system-monitor,service-manager"

# 运行单个模块
go run ./test/test.go --modules "aes-encryption" --verbose
```

### 报告生成 (Report Generation)

```bash
# 生成JSON格式报告
go run ./test/test.go --format json

# 指定报告保存路径
go run ./test/test.go --report ./reports/test-results.json

# 生成详细的文本报告
go run ./test/test.go --verbose --format text
```

## 命令行参数 (Command Line Arguments)

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `--verbose` | bool | false | 启用详细输出模式 |
| `--timeout` | duration | 10m | 总超时时间 |
| `--parallel` | bool | false | 并行运行测试 |
| `--modules` | string | "" | 指定运行的模块，逗号分隔 |
| `--format` | string | text | 输出格式：text, json |
| `--report` | string | "" | 报告保存路径 |

## 输出示例 (Output Examples)

### 文本格式输出 (Text Format Output)

```
============================================================
🧪 测试协调器执行摘要 (Test Coordinator Execution Summary)
============================================================
⏱️  执行时间 (Execution Time): 2m15s
📊 总模块数 (Total Modules): 10
✅ 通过数量 (Passed): 9
❌ 失败数量 (Failed): 1
⏭️  跳过数量 (Skipped): 0
📈 成功率 (Success Rate): 90.0%

📋 模块详情 (Module Details):
------------------------------------------------------------
✅ project-init         1.2s    项目初始化和目录结构验证
✅ cli-parsing          3.4s    基础命令行参数解析验证
✅ aes-encryption       5.6s    AES加密模块验证
❌ communication-protocol 2.1s   通信协议设计验证
✅ database-model       4.3s    数据库模型设计验证
============================================================
```

### JSON格式输出 (JSON Format Output)

```json
{
  "start_time": "2025-01-30T10:00:00Z",
  "end_time": "2025-01-30T10:02:15Z",
  "total_duration": "2m15s",
  "total_modules": 10,
  "passed_count": 9,
  "failed_count": 1,
  "skipped_count": 0,
  "success_rate": 90.0,
  "results": {
    "project-init": {
      "module": "project-init",
      "status": "passed",
      "duration": "1.2s",
      "output": "...",
      "start_time": "2025-01-30T10:00:00Z",
      "end_time": "2025-01-30T10:00:01Z"
    }
  }
}
```

## 集成现有测试程序 (Integration with Existing Test Programs)

测试协调器充分利用了项目中现有的测试基础设施：

- **cmd/monitor-test**: 系统监控功能测试
- **cmd/service-test**: 服务管理功能测试
- **cmd/reporter-test**: 数据上报功能测试
- **cmd/client-test**: 客户端API功能测试
- **cmd/main-client-test**: 主客户端功能测试
- **cmd/comprehensive-test**: 综合功能测试
- **internal/*/\*_test.go**: 各模块单元测试

## 错误处理 (Error Handling)

- **超时处理**: 每个测试模块都有独立的超时设置
- **必需模块**: 标记为必需的模块失败时会终止整个测试流程
- **错误收集**: 收集并报告所有测试错误和输出信息
- **优雅退出**: 根据测试结果设置适当的退出码

## 扩展性 (Extensibility)

测试协调器设计为易于扩展：

1. **添加新模块**: 在`GetAvailableModules()`方法中添加新的测试模块定义
2. **自定义命令**: 每个模块可以定义自己的执行命令和参数
3. **结果处理**: 支持自定义结果处理和报告格式
4. **配置扩展**: 可以轻松添加新的配置选项

## 最佳实践 (Best Practices)

1. **模块化测试**: 保持每个测试模块的独立性和原子性
2. **合理超时**: 为不同类型的测试设置合适的超时时间
3. **详细日志**: 在开发和调试时使用`--verbose`选项
4. **并行执行**: 在CI/CD环境中使用`--parallel`提高执行效率
5. **选择性运行**: 在开发过程中使用`--modules`参数只运行相关测试

## 故障排除 (Troubleshooting)

### 常见问题 (Common Issues)

1. **找不到项目根目录**
   - 确保在包含`go.mod`文件的目录或其子目录中运行

2. **测试超时**
   - 使用`--timeout`参数增加总超时时间
   - 检查网络连接和系统资源

3. **模块执行失败**
   - 使用`--verbose`查看详细错误信息
   - 确保所有依赖项已正确安装

4. **权限问题**
   - 确保有足够的权限执行测试程序
   - 检查文件和目录的访问权限

### 调试技巧 (Debugging Tips)

```bash
# 运行单个模块并查看详细输出
go run ./test/test.go --modules "aes-encryption" --verbose

# 生成详细的JSON报告用于分析
go run ./test/test.go --format json --report debug-report.json

# 使用较短的超时时间快速测试
go run ./test/test.go --timeout 2m --modules "project-init"
```

## 贡献指南 (Contributing)

欢迎贡献新的测试模块和功能改进：

1. 在`GetAvailableModules()`中添加新的测试模块
2. 确保新模块有适当的描述和超时设置
3. 测试新功能并更新文档
4. 提交Pull Request

## 许可证 (License)

本项目遵循与主项目相同的许可证。
