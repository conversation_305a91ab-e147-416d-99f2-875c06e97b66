package manager

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// SupervisordManager supervisord服务管理器
type SupervisordManager struct{}

// NewSupervisordManager 创建supervisord管理器
func NewSupervisordManager() *SupervisordManager {
	return &SupervisordManager{}
}

// ListServices 列出所有supervisord服务
func (s *SupervisordManager) ListServices(ctx context.Context) ([]*Service, error) {
	output, err := executeCommand(ctx, "supervisorctl", "status")
	if err != nil {
		return nil, fmt.Errorf("获取supervisord服务列表失败: %w", err)
	}

	var services []*Service
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		
		fields := strings.Fields(line)
		if len(fields) < 2 {
			continue
		}
		
		serviceName := fields[0]
		status := fields[1]
		
		service := &Service{
			Name:        serviceName,
			DisplayName: serviceName,
			Status:      parseSupervisordStatus(status),
			Type:        "supervisord",
			Description: "Supervisord管理的进程",
		}
		
		// 解析PID和启动时间
		if len(fields) > 2 {
			for i, field := range fields[2:] {
				if strings.HasPrefix(field, "pid") {
					pidStr := strings.TrimPrefix(field, "pid")
					pidStr = strings.Trim(pidStr, ",")
					if pid, err := strconv.Atoi(pidStr); err == nil {
						service.PID = pid
					}
				}
				if strings.Contains(field, ":") && i > 0 {
					// 可能是时间信息
					timeStr := strings.Join(fields[2+i:], " ")
					if startTime, err := time.Parse("Jan 02 15:04 PM", timeStr); err == nil {
						service.StartTime = &startTime
					}
					break
				}
			}
		}
		
		services = append(services, service)
	}

	return services, nil
}

// GetService 获取指定supervisord服务的详细信息
func (s *SupervisordManager) GetService(ctx context.Context, name string) (*Service, error) {
	if !s.ServiceExists(ctx, name) {
		return nil, fmt.Errorf("服务不存在: %s", name)
	}

	output, err := executeCommand(ctx, "supervisorctl", "status", name)
	if err != nil {
		return nil, fmt.Errorf("获取supervisord服务信息失败: %w", err)
	}

	lines := strings.Split(output, "\n")
	if len(lines) == 0 {
		return nil, fmt.Errorf("无法解析服务信息")
	}

	line := strings.TrimSpace(lines[0])
	fields := strings.Fields(line)
	if len(fields) < 2 {
		return nil, fmt.Errorf("无法解析服务信息")
	}

	service := &Service{
		Name:        name,
		DisplayName: name,
		Status:      parseSupervisordStatus(fields[1]),
		Type:        "supervisord",
		Description: "Supervisord管理的进程",
	}

	// 解析详细信息
	for i, field := range fields[2:] {
		if strings.HasPrefix(field, "pid") {
			pidStr := strings.TrimPrefix(field, "pid")
			pidStr = strings.Trim(pidStr, ",")
			if pid, err := strconv.Atoi(pidStr); err == nil {
				service.PID = pid
			}
		}
		if strings.Contains(field, ":") && i > 0 {
			timeStr := strings.Join(fields[2+i:], " ")
			if startTime, err := time.Parse("Jan 02 15:04 PM", timeStr); err == nil {
				service.StartTime = &startTime
			}
			break
		}
	}

	return service, nil
}

// StartService 启动supervisord服务
func (s *SupervisordManager) StartService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "supervisorctl", "start", name)
	if err != nil {
		return fmt.Errorf("启动supervisord服务失败: %w", err)
	}
	return nil
}

// StopService 停止supervisord服务
func (s *SupervisordManager) StopService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "supervisorctl", "stop", name)
	if err != nil {
		return fmt.Errorf("停止supervisord服务失败: %w", err)
	}
	return nil
}

// RestartService 重启supervisord服务
func (s *SupervisordManager) RestartService(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "supervisorctl", "restart", name)
	if err != nil {
		return fmt.Errorf("重启supervisord服务失败: %w", err)
	}
	return nil
}

// EnableService supervisord没有启用概念，返回不支持错误
func (s *SupervisordManager) EnableService(ctx context.Context, name string) error {
	return fmt.Errorf("supervisord不支持启用操作")
}

// DisableService supervisord没有禁用概念，返回不支持错误
func (s *SupervisordManager) DisableService(ctx context.Context, name string) error {
	return fmt.Errorf("supervisord不支持禁用操作")
}

// ServiceExists 检查supervisord服务是否存在
func (s *SupervisordManager) ServiceExists(ctx context.Context, name string) bool {
	_, err := executeCommand(ctx, "supervisorctl", "status", name)
	return err == nil
}

// GetServiceLogs 获取supervisord服务日志
func (s *SupervisordManager) GetServiceLogs(ctx context.Context, name string, lines int) ([]string, error) {
	// supervisorctl tail命令获取日志
	output, err := executeCommand(ctx, "supervisorctl", "tail", "-"+strconv.Itoa(lines), name)
	if err != nil {
		return nil, fmt.Errorf("获取supervisord服务日志失败: %w", err)
	}

	logLines := strings.Split(output, "\n")
	
	// 过滤空行
	var result []string
	for _, line := range logLines {
		if strings.TrimSpace(line) != "" {
			result = append(result, line)
		}
	}

	return result, nil
}

// parseSupervisordStatus 解析supervisord服务状态
func parseSupervisordStatus(status string) ServiceStatus {
	status = strings.ToLower(strings.TrimSpace(status))
	
	switch {
	case strings.Contains(status, "running"):
		return StatusRunning
	case strings.Contains(status, "stopped"):
		return StatusStopped
	case strings.Contains(status, "exited"):
		return StatusStopped
	case strings.Contains(status, "fatal"):
		return StatusFailed
	case strings.Contains(status, "backoff"):
		return StatusFailed
	case strings.Contains(status, "starting"):
		return StatusUnknown
	case strings.Contains(status, "stopping"):
		return StatusUnknown
	default:
		return StatusUnknown
	}
}

// ReloadConfig 重新加载supervisord配置
func (s *SupervisordManager) ReloadConfig(ctx context.Context) error {
	_, err := executeCommand(ctx, "supervisorctl", "reread")
	if err != nil {
		return fmt.Errorf("重新读取supervisord配置失败: %w", err)
	}
	
	_, err = executeCommand(ctx, "supervisorctl", "update")
	if err != nil {
		return fmt.Errorf("更新supervisord配置失败: %w", err)
	}
	
	return nil
}

// GetProcessInfo 获取进程详细信息
func (s *SupervisordManager) GetProcessInfo(ctx context.Context, name string) (map[string]interface{}, error) {
	output, err := executeCommand(ctx, "supervisorctl", "status", name)
	if err != nil {
		return nil, fmt.Errorf("获取进程信息失败: %w", err)
	}

	info := make(map[string]interface{})
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		
		fields := strings.Fields(line)
		if len(fields) >= 2 {
			info["name"] = fields[0]
			info["status"] = fields[1]
			
			// 解析其他信息
			for _, field := range fields[2:] {
				if strings.HasPrefix(field, "pid") {
					pidStr := strings.TrimPrefix(field, "pid")
					pidStr = strings.Trim(pidStr, ",")
					if pid, err := strconv.Atoi(pidStr); err == nil {
						info["pid"] = pid
					}
				}
			}
		}
	}

	return info, nil
}

// ClearLogs 清空服务日志
func (s *SupervisordManager) ClearLogs(ctx context.Context, name string) error {
	_, err := executeCommand(ctx, "supervisorctl", "clear", name)
	if err != nil {
		return fmt.Errorf("清空supervisord服务日志失败: %w", err)
	}
	return nil
}
