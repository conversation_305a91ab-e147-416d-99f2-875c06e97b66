package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	LevelDebug LogLevel = iota
	LevelInfo
	LevelWarn
	LevelError
	LevelFatal
)

// String 返回日志级别字符串
func (l LogLevel) String() string {
	switch l {
	case LevelDebug:
		return "DEBUG"
	case LevelInfo:
		return "INFO"
	case LevelWarn:
		return "WARN"
	case LevelError:
		return "ERROR"
	case LevelFatal:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// Config 日志配置
type Config struct {
	Level      LogLevel `yaml:"level"`       // 日志级别
	Format     string   `yaml:"format"`      // 日志格式: text, json
	Output     string   `yaml:"output"`      // 输出方式: stdout, file, both
	File       string   `yaml:"file"`        // 日志文件路径
	MaxSize    int      `yaml:"max_size"`    // 最大文件大小(MB)
	MaxBackups int      `yaml:"max_backups"` // 最大备份数量
	MaxAge     int      `yaml:"max_age"`     // 最大保存天数
	Compress   bool     `yaml:"compress"`    // 是否压缩
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		Level:      LevelInfo,
		Format:     "text",
		Output:     "stdout",
		File:       "./logs/app.log",
		MaxSize:    100,
		MaxBackups: 5,
		MaxAge:     30,
		Compress:   true,
	}
}

// Logger 日志器
type Logger struct {
	config   *Config
	logger   *log.Logger
	file     *os.File
	mu       sync.RWMutex
	level    LogLevel
	writers  []io.Writer
}

// NewLogger 创建日志器
func NewLogger(config *Config) (*Logger, error) {
	if config == nil {
		config = DefaultConfig()
	}

	logger := &Logger{
		config:  config,
		level:   config.Level,
		writers: make([]io.Writer, 0),
	}

	// 设置输出
	if err := logger.setupOutput(); err != nil {
		return nil, err
	}

	// 创建标准库logger
	multiWriter := io.MultiWriter(logger.writers...)
	logger.logger = log.New(multiWriter, "", 0)

	return logger, nil
}

// setupOutput 设置输出
func (l *Logger) setupOutput() error {
	switch l.config.Output {
	case "stdout":
		l.writers = append(l.writers, os.Stdout)
	case "file":
		if err := l.setupFileOutput(); err != nil {
			return err
		}
	case "both":
		l.writers = append(l.writers, os.Stdout)
		if err := l.setupFileOutput(); err != nil {
			return err
		}
	default:
		l.writers = append(l.writers, os.Stdout)
	}

	return nil
}

// setupFileOutput 设置文件输出
func (l *Logger) setupFileOutput() error {
	// 创建日志目录
	dir := filepath.Dir(l.config.File)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 打开日志文件
	file, err := os.OpenFile(l.config.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %w", err)
	}

	l.file = file
	l.writers = append(l.writers, file)

	return nil
}

// log 记录日志
func (l *Logger) log(level LogLevel, format string, args ...interface{}) {
	if level < l.level {
		return
	}

	l.mu.RLock()
	defer l.mu.RUnlock()

	// 获取调用者信息
	_, file, line, ok := runtime.Caller(2)
	if !ok {
		file = "unknown"
		line = 0
	}

	// 格式化消息
	message := fmt.Sprintf(format, args...)
	
	// 构建日志条目
	entry := l.formatEntry(level, file, line, message)
	
	// 输出日志
	l.logger.Print(entry)

	// 如果是FATAL级别，退出程序
	if level == LevelFatal {
		os.Exit(1)
	}
}

// formatEntry 格式化日志条目
func (l *Logger) formatEntry(level LogLevel, file string, line int, message string) string {
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	filename := filepath.Base(file)

	switch l.config.Format {
	case "json":
		return fmt.Sprintf(`{"time":"%s","level":"%s","file":"%s:%d","msg":"%s"}`,
			timestamp, level.String(), filename, line, message)
	default:
		return fmt.Sprintf("[%s] %s %s:%d - %s",
			timestamp, level.String(), filename, line, message)
	}
}

// Debug 记录调试日志
func (l *Logger) Debug(format string, args ...interface{}) {
	l.log(LevelDebug, format, args...)
}

// Info 记录信息日志
func (l *Logger) Info(format string, args ...interface{}) {
	l.log(LevelInfo, format, args...)
}

// Warn 记录警告日志
func (l *Logger) Warn(format string, args ...interface{}) {
	l.log(LevelWarn, format, args...)
}

// Error 记录错误日志
func (l *Logger) Error(format string, args ...interface{}) {
	l.log(LevelError, format, args...)
}

// Fatal 记录致命错误日志并退出
func (l *Logger) Fatal(format string, args ...interface{}) {
	l.log(LevelFatal, format, args...)
}

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level LogLevel) {
	l.mu.Lock()
	l.level = level
	l.mu.Unlock()
}

// GetLevel 获取日志级别
func (l *Logger) GetLevel() LogLevel {
	l.mu.RLock()
	defer l.mu.RUnlock()
	return l.level
}

// Close 关闭日志器
func (l *Logger) Close() error {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.file != nil {
		return l.file.Close()
	}
	return nil
}

// Rotate 轮转日志文件
func (l *Logger) Rotate() error {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.file == nil {
		return nil
	}

	// 关闭当前文件
	if err := l.file.Close(); err != nil {
		return err
	}

	// 重命名当前文件
	timestamp := time.Now().Format("20060102_150405")
	backupFile := strings.TrimSuffix(l.config.File, filepath.Ext(l.config.File)) + 
		"_" + timestamp + filepath.Ext(l.config.File)
	
	if err := os.Rename(l.config.File, backupFile); err != nil {
		return err
	}

	// 创建新文件
	file, err := os.OpenFile(l.config.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return err
	}

	l.file = file

	// 更新writers
	l.writers = l.writers[:len(l.writers)-1] // 移除旧的文件writer
	l.writers = append(l.writers, file)      // 添加新的文件writer

	// 重新创建logger
	multiWriter := io.MultiWriter(l.writers...)
	l.logger = log.New(multiWriter, "", 0)

	// 清理旧的备份文件
	go l.cleanupOldFiles()

	return nil
}

// cleanupOldFiles 清理旧的日志文件
func (l *Logger) cleanupOldFiles() {
	if l.config.MaxBackups <= 0 && l.config.MaxAge <= 0 {
		return
	}

	dir := filepath.Dir(l.config.File)
	baseName := strings.TrimSuffix(filepath.Base(l.config.File), filepath.Ext(l.config.File))
	ext := filepath.Ext(l.config.File)

	files, err := filepath.Glob(filepath.Join(dir, baseName+"_*"+ext))
	if err != nil {
		return
	}

	// 按修改时间排序
	type fileInfo struct {
		path    string
		modTime time.Time
	}

	var fileInfos []fileInfo
	for _, file := range files {
		if stat, err := os.Stat(file); err == nil {
			fileInfos = append(fileInfos, fileInfo{
				path:    file,
				modTime: stat.ModTime(),
			})
		}
	}

	// 按时间排序（最新的在前）
	for i := 0; i < len(fileInfos)-1; i++ {
		for j := i + 1; j < len(fileInfos); j++ {
			if fileInfos[i].modTime.Before(fileInfos[j].modTime) {
				fileInfos[i], fileInfos[j] = fileInfos[j], fileInfos[i]
			}
		}
	}

	// 删除超过数量限制的文件
	if l.config.MaxBackups > 0 && len(fileInfos) > l.config.MaxBackups {
		for i := l.config.MaxBackups; i < len(fileInfos); i++ {
			os.Remove(fileInfos[i].path)
		}
		fileInfos = fileInfos[:l.config.MaxBackups]
	}

	// 删除超过时间限制的文件
	if l.config.MaxAge > 0 {
		cutoff := time.Now().AddDate(0, 0, -l.config.MaxAge)
		for _, info := range fileInfos {
			if info.modTime.Before(cutoff) {
				os.Remove(info.path)
			}
		}
	}
}

// 全局日志器
var defaultLogger *Logger

// InitLogger 初始化全局日志器
func InitLogger(config *Config) error {
	logger, err := NewLogger(config)
	if err != nil {
		return err
	}
	defaultLogger = logger
	return nil
}

// 全局日志函数
func Debug(format string, args ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Debug(format, args...)
	}
}

func Info(format string, args ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Info(format, args...)
	}
}

func Warn(format string, args ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Warn(format, args...)
	}
}

func Error(format string, args ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Error(format, args...)
	}
}

func Fatal(format string, args ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Fatal(format, args...)
	}
}
