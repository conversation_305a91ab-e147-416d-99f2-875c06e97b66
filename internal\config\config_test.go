package config

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

// setupTestConfig 创建测试配置
func setupTestConfig(t *testing.T) (*Manager, string, func()) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "config_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}

	// 创建测试配置文件
	configFile := filepath.Join(tempDir, "config.yaml")
	configContent := `
system:
  name: "Test Monitor"
  version: "1.0.0"
  environment: "testing"
  data_dir: "./data"
  mode: "both"

database:
  path: "./data/test.db"
  max_open_conns: 10
  max_idle_conns: 2

scheduler:
  enabled: true
  mode: "odd"
  timezone: "Asia/Shanghai"
  max_concurrent: 2

servers:
  - name: "test-server-01"
    ip: "*************"
    port: 5201
    location: "Test Location"
    enabled: true
    priority: 1

test:
  duration: "10s"
  parallel: 2
  protocol: "tcp"

web:
  enabled: true
  port: 8080
  host: "127.0.0.1"

api:
  enabled: true
  port: 8443
  host: "127.0.0.1"

log:
  level: "debug"
  format: "text"
  output: "stdout"
`

	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		os.RemoveAll(tempDir)
		t.Fatalf("Failed to write config file: %v", err)
	}

	// 创建配置管理器
	manager := NewManager(configFile)

	// 清理函数
	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return manager, configFile, cleanup
}

func TestConfigLoad(t *testing.T) {
	manager, _, cleanup := setupTestConfig(t)
	defer cleanup()

	// 加载配置
	err := manager.Load()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	config := manager.Get()
	if config == nil {
		t.Fatal("Config is nil")
	}

	// 验证系统配置
	if config.System.Name != "Test Monitor" {
		t.Errorf("Expected system name 'Test Monitor', got '%s'", config.System.Name)
	}

	if config.System.Environment != "testing" {
		t.Errorf("Expected environment 'testing', got '%s'", config.System.Environment)
	}

	// 验证数据库配置
	if config.Database.MaxOpenConns != 10 {
		t.Errorf("Expected max_open_conns 10, got %d", config.Database.MaxOpenConns)
	}

	// 验证服务器配置
	if len(config.Servers) != 1 {
		t.Errorf("Expected 1 server, got %d", len(config.Servers))
	}

	if config.Servers[0].Name != "test-server-01" {
		t.Errorf("Expected server name 'test-server-01', got '%s'", config.Servers[0].Name)
	}
}

func TestConfigDefaults(t *testing.T) {
	// 创建临时目录但不创建配置文件
	tempDir, err := os.MkdirTemp("", "config_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 不指定配置文件，让viper使用默认搜索路径
	manager := NewManager("")

	// 加载配置（应该使用默认值）
	err = manager.Load()
	if err != nil {
		t.Fatalf("Failed to load config with defaults: %v", err)
	}

	config := manager.Get()
	if config == nil {
		t.Fatal("Config is nil")
	}

	// 验证默认值
	if config.System.Name != "Server Monitor" {
		t.Errorf("Expected default system name 'Server Monitor', got '%s'", config.System.Name)
	}

	if config.Database.MaxOpenConns != 25 {
		t.Errorf("Expected default max_open_conns 25, got %d", config.Database.MaxOpenConns)
	}

	if config.Web.Port != 8080 {
		t.Errorf("Expected default web port 8080, got %d", config.Web.Port)
	}
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		expectError bool
		errorField  string
	}{
		{
			name: "valid config",
			config: &Config{
				System: SystemConfig{
					Name:        "Test",
					Version:     "1.0.0",
					Environment: "testing",
					DataDir:     "./data",
					Mode:        "both",
				},
				Database: DatabaseConfig{
					Path:         "./test.db",
					MaxOpenConns: 10,
					MaxIdleConns: 5,
				},
				Scheduler: SchedulerConfig{
					Mode:          "odd",
					Timezone:      "Asia/Shanghai",
					MaxConcurrent: 4,
				},
				Servers: []ServerConfig{
					{
						Name:    "test-server",
						IP:      "***********",
						Port:    5201,
						Enabled: true,
					},
				},
				Test: TestConfig{
					Duration: time.Second * 30,
					Parallel: 4,
					Protocol: "tcp",
				},
				Web: WebConfig{
					Enabled: true,
					Port:    8080,
					Host:    "127.0.0.1",
				},
				API: APIConfig{
					Enabled: true,
					Port:    8443,
					Host:    "127.0.0.1",
				},
				Log: LogConfig{
					Level:      "info",
					Format:     "text",
					Output:     "stdout",
					MaxSize:    100,
					MaxBackups: 3,
					MaxAge:     28,
				},
			},
			expectError: false,
		},
		{
			name: "invalid system name",
			config: &Config{
				System: SystemConfig{
					Name:        "", // 空名称
					Version:     "1.0.0",
					Environment: "testing",
					DataDir:     "./data",
					Mode:        "both",
				},
				Servers: []ServerConfig{
					{
						Name:    "test-server",
						IP:      "***********",
						Port:    5201,
						Enabled: true,
					},
				},
				Log: LogConfig{
					Level:      "info",
					Format:     "text",
					Output:     "stdout",
					MaxSize:    100,
					MaxBackups: 3,
					MaxAge:     28,
				},
			},
			expectError: true,
			errorField:  "system.name",
		},
		{
			name: "invalid server IP",
			config: &Config{
				System: SystemConfig{
					Name:        "Test",
					Version:     "1.0.0",
					Environment: "testing",
					DataDir:     "./data",
					Mode:        "both",
				},
				Servers: []ServerConfig{
					{
						Name:    "test-server",
						IP:      "invalid-ip", // 无效IP
						Port:    5201,
						Enabled: true,
					},
				},
				Log: LogConfig{
					Level:      "info",
					Format:     "text",
					Output:     "stdout",
					MaxSize:    100,
					MaxBackups: 3,
					MaxAge:     28,
				},
			},
			expectError: true,
			errorField:  "servers[0].ip",
		},
		{
			name: "invalid log level",
			config: &Config{
				System: SystemConfig{
					Name:        "Test",
					Version:     "1.0.0",
					Environment: "testing",
					DataDir:     "./data",
					Mode:        "both",
				},
				Servers: []ServerConfig{
					{
						Name:    "test-server",
						IP:      "***********",
						Port:    5201,
						Enabled: true,
					},
				},
				Log: LogConfig{
					Level:      "invalid", // 无效日志级别
					Format:     "text",
					Output:     "stdout",
					MaxSize:    100,
					MaxBackups: 3,
					MaxAge:     28,
				},
			},
			expectError: true,
			errorField:  "log.level",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected validation error, but got none")
					return
				}

				// 检查错误是否包含预期的字段
				if tt.errorField != "" {
					if ve, ok := err.(ValidationErrors); ok {
						found := false
						for _, e := range ve {
							if e.Field == tt.errorField {
								found = true
								break
							}
						}
						if !found {
							t.Errorf("Expected error for field '%s', but not found in: %v", tt.errorField, err)
						}
					} else {
						t.Errorf("Expected ValidationErrors, got %T: %v", err, err)
					}
				}
			} else {
				if err != nil {
					t.Errorf("Expected no validation error, but got: %v", err)
				}
			}
		})
	}
}

func TestConfigSave(t *testing.T) {
	manager, configFile, cleanup := setupTestConfig(t)
	defer cleanup()

	// 加载配置
	err := manager.Load()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// 保存配置到新文件
	newConfigFile := filepath.Join(filepath.Dir(configFile), "saved_config.yaml")
	err = manager.Save(newConfigFile)
	if err != nil {
		t.Fatalf("Failed to save config: %v", err)
	}

	// 验证文件是否存在
	if _, err := os.Stat(newConfigFile); os.IsNotExist(err) {
		t.Errorf("Saved config file does not exist: %s", newConfigFile)
	}

	// 加载保存的配置并验证
	newManager := NewManager(newConfigFile)
	err = newManager.Load()
	if err != nil {
		t.Fatalf("Failed to load saved config: %v", err)
	}

	originalConfig := manager.Get()
	savedConfig := newManager.Get()

	if originalConfig.System.Name != savedConfig.System.Name {
		t.Errorf("Config not saved correctly: expected name '%s', got '%s'",
			originalConfig.System.Name, savedConfig.System.Name)
	}
}

func TestConfigReload(t *testing.T) {
	manager, configFile, cleanup := setupTestConfig(t)
	defer cleanup()

	// 加载初始配置
	err := manager.Load()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	originalName := manager.Get().System.Name

	// 修改配置文件
	newConfigContent := `
system:
  name: "Modified Test Monitor"
  version: "1.0.0"
  environment: "testing"
  data_dir: "./data"
  mode: "both"

servers:
  - name: "test-server-01"
    ip: "*************"
    port: 5201
    enabled: true

log:
  level: "info"
  format: "text"
  output: "stdout"
`

	err = os.WriteFile(configFile, []byte(newConfigContent), 0644)
	if err != nil {
		t.Fatalf("Failed to write modified config: %v", err)
	}

	// 重新加载配置
	err = manager.Reload()
	if err != nil {
		t.Fatalf("Failed to reload config: %v", err)
	}

	newName := manager.Get().System.Name
	if newName == originalName {
		t.Errorf("Config was not reloaded: name still '%s'", newName)
	}

	if newName != "Modified Test Monitor" {
		t.Errorf("Expected reloaded name 'Modified Test Monitor', got '%s'", newName)
	}
}
