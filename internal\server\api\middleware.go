package api

import (
	"fmt"
	"net/http"
	"strings"
	"sync" // Add sync package
	"time"

	"server-monitor/internal/config" // Add config package
	"server-monitor/internal/database"
	"server-monitor/internal/logger" // Import the project's logger

	"github.com/gin-contrib/cors" // Add gin-contrib/cors
	"github.com/gin-contrib/gzip" // Add gin-contrib/gzip
	"github.com/gin-gonic/gin"
)

// AuthMiddleware 认证中间件
func AuthMiddleware(repo database.Repository, authConfig config.AuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			SendErrorResponse(c, http.StatusUnauthorized, http.StatusUnauthorized, "Authorization header required")
			c.Abort()
			return
		}

		// 检查是否是Bearer token
		if !strings.HasPrefix(authHeader, "Bearer ") {
			SendErrorResponse(c, http.StatusUnauthorized, http.StatusUnauthorized, "Invalid authorization format")
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")

		// 模拟JWT验证 (实际应用中会使用JWT库进行验证)
		// 假设一个简单的验证：如果token是"valid-token"，则认为是有效用户
		// 实际中会解析JWT，验证签名，检查过期时间等
		// 模拟JWT验证 (实际应用中会使用JWT库进行验证)
		// 假设一个简单的验证：如果token是"valid-token"，则认为是有效用户
		// 实际中会解析JWT，验证签名，检查过期时间等
		// For demonstration, we'll use a simple check. In a real app, you'd validate against a proper token.
		// You might also check authConfig.TokenTTL for token expiration.
		if tokenString != "valid-token" {
			SendErrorResponse(c, http.StatusUnauthorized, http.StatusUnauthorized, "Invalid or expired token")
			c.Abort()
			return
		}

		// 模拟从token中获取用户信息，并设置到Gin Context中
		// 实际中会解析JWT的claims，获取用户ID、角色等
		userID := uint(1)   // 假设用户ID为1
		userRole := "admin" // 假设用户角色为admin

		c.Set("userID", userID)
		c.Set("userRole", userRole)

		c.Next()
	}
}

// CORSMiddleware 处理跨域请求
func CORSMiddleware(corsConfig config.CORSConfig) gin.HandlerFunc {
	if !corsConfig.Enabled {
		return func(c *gin.Context) {
			c.Next()
		}
	}

	// 使用 gin-contrib/cors 库
	corsConfigGin := cors.Config{
		AllowOrigins: corsConfig.AllowOrigins,
		AllowMethods: corsConfig.AllowMethods,
		AllowHeaders: corsConfig.AllowHeaders,
		// 其他可选配置，例如：
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}

	return cors.New(corsConfigGin)
}

// CompressionMiddleware 压缩中间件
func CompressionMiddleware() gin.HandlerFunc {
	return gzip.Gzip(gzip.DefaultCompression)
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(rateLimit int) gin.HandlerFunc {
	// 简单的内存存储，用于生产环境请使用分布式缓存如Redis
	// key: client IP, value: [timestamp1, timestamp2, ...]
	clientRequests := make(map[string][]time.Time)
	mu := &sync.Mutex{} // 保护 clientRequests 的并发访问

	return func(c *gin.Context) {
		if rateLimit <= 0 { // 如果限流设置为0或负数，则不启用限流
			c.Next()
			return
		}

		clientIP := c.ClientIP()
		now := time.Now()

		mu.Lock()
		defer mu.Unlock()

		// 清理过期请求记录
		var validRequests []time.Time
		for _, t := range clientRequests[clientIP] {
			// 假设限流周期为1分钟，只保留1分钟内的请求记录
			if now.Sub(t) <= time.Minute {
				validRequests = append(validRequests, t)
			}
		}
		clientRequests[clientIP] = validRequests

		if len(clientRequests[clientIP]) >= rateLimit {
			SendErrorResponse(c, http.StatusTooManyRequests, http.StatusTooManyRequests, "Too many requests")
			c.Abort()
			return
		}

		clientRequests[clientIP] = append(clientRequests[clientIP], now)
		c.Next()
	}
}

// RoleMiddleware 角色权限中间件
func RoleMiddleware(authConfig config.AuthConfig, allowedRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("userRole")
		if !exists {
			SendErrorResponse(c, http.StatusForbidden, http.StatusForbidden, "Role information not found")
			c.Abort()
			return
		}

		roleStr, ok := userRole.(string)
		if !ok {
			SendErrorResponse(c, http.StatusForbidden, http.StatusForbidden, "Invalid role format")
			c.Abort()
			return
		}

		for _, role := range allowedRoles {
			if roleStr == role {
				c.Next()
				return
			}
		}

		SendErrorResponse(c, http.StatusForbidden, http.StatusForbidden, fmt.Sprintf("Access denied. Required roles: %v", allowedRoles))
		c.Abort()
	}
}

// LoggerMiddleware 记录请求详细信息。(LoggerMiddleware logs request details.)
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)

		// 使用项目日志器记录请求详细信息
		logger.Info("Request - Path: %s, Method: %s, Status: %d, Duration: %v",
			c.Request.URL.Path, c.Request.Method, c.Writer.Status(), duration)
	}
}
