package crypto

import (
	"strings"
	"sync"
	"testing"
	"time"
)

func TestGenerateSecureMessageID(t *testing.T) {
	// 测试基本功能 (Test basic functionality)
	id, err := GenerateSecureMessageID()
	if err != nil {
		t.Fatalf("Failed to generate secure message ID: %v", err)
	}

	if id == "" {
		t.<PERSON>rror("Generated ID should not be empty")
	}

	// 验证ID格式 (Validate ID format)
	if err := ValidateSecureMessageID(id); err != nil {
		t.Errorf("Generated ID failed validation: %v", err)
	}

	t.Logf("Generated ID: %s", id)
}

func TestGenerateSecureMessageIDMust(t *testing.T) {
	// 测试Must版本 (Test Must version)
	id := GenerateSecureMessageIDMust()
	if id == "" {
		t.Error("Generated ID should not be empty")
	}

	// 验证ID格式 (Validate ID format)
	if err := ValidateSecureMessageID(id); err != nil {
		t.<PERSON><PERSON>("Generated ID failed validation: %v", err)
	}
}

func TestParseSecureMessageID(t *testing.T) {
	// 生成一个ID (Generate an ID)
	id, err := GenerateSecureMessageID()
	if err != nil {
		t.Fatalf("Failed to generate secure message ID: %v", err)
	}

	// 解析ID (Parse ID)
	randomBytes, timestamp, counter, err := ParseSecureMessageID(id)
	if err != nil {
		t.Fatalf("Failed to parse secure message ID: %v", err)
	}

	// 验证随机字节长度 (Validate random bytes length)
	if len(randomBytes) != RandomBytesLen {
		t.Errorf("Expected random bytes length %d, got %d", RandomBytesLen, len(randomBytes))
	}

	// 验证时间戳合理性 (Validate timestamp reasonableness)
	now := time.Now().UnixNano()
	if timestamp > now || timestamp < now-int64(time.Second) {
		t.Errorf("Timestamp seems unreasonable: %d (now: %d)", timestamp, now)
	}

	// 计数器应该是有效值 (Counter should be a valid value)
	// uint32类型本身就是非负的，所以这里只需要记录值 (uint32 is inherently non-negative, so just log the value)
	t.Logf("Counter value: %d", counter)

	t.Logf("Parsed - Random bytes: %d bytes, Timestamp: %d, Counter: %d", len(randomBytes), timestamp, counter)
}

func TestIDUniqueness(t *testing.T) {
	// 测试ID唯一性 (Test ID uniqueness)
	const numIDs = 10000
	ids := make(map[string]bool, numIDs)

	for i := 0; i < numIDs; i++ {
		id, err := GenerateSecureMessageID()
		if err != nil {
			t.Fatalf("Failed to generate ID at iteration %d: %v", i, err)
		}

		if ids[id] {
			t.Fatalf("Duplicate ID found: %s", id)
		}
		ids[id] = true
	}

	t.Logf("Successfully generated %d unique IDs", numIDs)
}

func TestConcurrentIDGeneration(t *testing.T) {
	// 测试并发ID生成 (Test concurrent ID generation)
	const numGoroutines = 100
	const numIDsPerGoroutine = 1000

	var wg sync.WaitGroup
	idChan := make(chan string, numGoroutines*numIDsPerGoroutine)

	// 启动多个goroutine (Start multiple goroutines)
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < numIDsPerGoroutine; j++ {
				id, err := GenerateSecureMessageID()
				if err != nil {
					t.Errorf("Failed to generate ID: %v", err)
					return
				}
				idChan <- id
			}
		}()
	}

	wg.Wait()
	close(idChan)

	// 检查唯一性 (Check uniqueness)
	ids := make(map[string]bool)
	count := 0
	for id := range idChan {
		if ids[id] {
			t.Fatalf("Duplicate ID found in concurrent test: %s", id)
		}
		ids[id] = true
		count++
	}

	expectedCount := numGoroutines * numIDsPerGoroutine
	if count != expectedCount {
		t.Errorf("Expected %d IDs, got %d", expectedCount, count)
	}

	t.Logf("Successfully generated %d unique IDs concurrently", count)
}

func TestGetIDTimestamp(t *testing.T) {
	// 生成ID (Generate ID)
	beforeGeneration := time.Now()
	id, err := GenerateSecureMessageID()
	if err != nil {
		t.Fatalf("Failed to generate secure message ID: %v", err)
	}
	afterGeneration := time.Now()

	// 提取时间戳 (Extract timestamp)
	timestamp, err := GetIDTimestamp(id)
	if err != nil {
		t.Fatalf("Failed to get timestamp from ID: %v", err)
	}

	// 验证时间戳在合理范围内 (Validate timestamp is within reasonable range)
	if timestamp.Before(beforeGeneration) || timestamp.After(afterGeneration) {
		t.Errorf("Timestamp %v is not between %v and %v", timestamp, beforeGeneration, afterGeneration)
	}
}

func TestIsIDExpired(t *testing.T) {
	// 生成一个ID (Generate an ID)
	id, err := GenerateSecureMessageID()
	if err != nil {
		t.Fatalf("Failed to generate secure message ID: %v", err)
	}

	// 测试未过期 (Test not expired)
	expired, err := IsIDExpired(id, time.Hour)
	if err != nil {
		t.Fatalf("Failed to check if ID is expired: %v", err)
	}
	if expired {
		t.Error("Newly generated ID should not be expired")
	}

	// 等待一小段时间然后测试过期 (Wait a bit then test expired)
	time.Sleep(time.Millisecond)
	expired, err = IsIDExpired(id, time.Nanosecond)
	if err != nil {
		t.Fatalf("Failed to check if ID is expired: %v", err)
	}
	if !expired {
		t.Error("ID should be expired with very short max age")
	}
}

func TestValidateSecureMessageID(t *testing.T) {
	// 测试有效ID (Test valid ID)
	validID, err := GenerateSecureMessageID()
	if err != nil {
		t.Fatalf("Failed to generate secure message ID: %v", err)
	}

	if err := ValidateSecureMessageID(validID); err != nil {
		t.Errorf("Valid ID failed validation: %v", err)
	}

	// 测试无效ID (Test invalid IDs)
	invalidIDs := []string{
		"",                       // 空字符串 (Empty string)
		"invalid",                // 无效base64 (Invalid base64)
		"dGVzdA==",               // 太短 (Too short)
		strings.Repeat("A", 100), // 太长 (Too long)
	}

	for _, invalidID := range invalidIDs {
		if err := ValidateSecureMessageID(invalidID); err == nil {
			t.Errorf("Invalid ID '%s' passed validation", invalidID)
		}
	}
}

func BenchmarkGenerateSecureMessageID(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := GenerateSecureMessageID()
		if err != nil {
			b.Fatalf("Failed to generate ID: %v", err)
		}
	}
}

func BenchmarkGenerateSecureMessageIDMust(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_ = GenerateSecureMessageIDMust()
	}
}

func BenchmarkParseSecureMessageID(b *testing.B) {
	// 预生成一个ID用于基准测试 (Pre-generate an ID for benchmarking)
	id, err := GenerateSecureMessageID()
	if err != nil {
		b.Fatalf("Failed to generate ID for benchmark: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, _, err := ParseSecureMessageID(id)
		if err != nil {
			b.Fatalf("Failed to parse ID: %v", err)
		}
	}
}
