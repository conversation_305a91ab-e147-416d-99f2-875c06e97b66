package reporter

import (
	"context"
	"fmt"
	"time"
)

// Reporter defines the interface for a data reporter.
type Reporter interface {
	Report(ctx context.Context, data interface{}) error
}

// reporter implements the Reporter interface.
type reporter struct {
	client      HTTPClient
	url         string
	headers     map[string]string
	retryPolicy RetryPolicy
}

// NewR<PERSON>orter creates a new data reporter.
func NewReporter(client HTTPClient, url string, headers map[string]string, retryPolicy RetryPolicy) Reporter {
	return &reporter{
		client:      client,
		url:         url,
		headers:     headers,
		retryPolicy: retryPolicy,
	}
}

// Report sends the data to the configured URL with retry mechanism.
func (r *reporter) Report(ctx context.Context, data interface{}) error {
	var lastErr error
	for i := 0; i < r.retryPolicy.MaxRetries; i++ {
		resp, err := r.client.Send(ctx, r.url, data, r.headers)
		if err != nil {
			lastErr = fmt.Errorf("failed to send data (attempt %d/%d): %w", i+1, r.retryPolicy.MaxRetries, err)
			time.Sleep(r.retryPolicy.CalculateDelay(i))
			continue
		}

		if resp.StatusCode >= 200 && resp.StatusCode < 300 {
			resp.Body.Close()
			return nil // Success
		}

		body, _ := ResponseBodyToString(resp)
		lastErr = fmt.Errorf("server returned non-success status code %d (attempt %d/%d): %s", resp.StatusCode, i+1, r.retryPolicy.MaxRetries, body)
		resp.Body.Close()
		time.Sleep(r.retryPolicy.CalculateDelay(i))
	}
	return fmt.Errorf("failed to report data after %d attempts: %w", r.retryPolicy.MaxRetries, lastErr)
}

// RetryPolicy defines the retry behavior for the reporter.
type RetryPolicy struct {
	MaxRetries   int
	InitialDelay time.Duration
	Factor       float64
}

// DefaultRetryPolicy returns a default retry policy.
func DefaultRetryPolicy() RetryPolicy {
	return RetryPolicy{
		MaxRetries:   3,
		InitialDelay: 1 * time.Second,
		Factor:       2.0,
	}
}

// CalculateDelay calculates the delay for a given retry attempt.
func (rp RetryPolicy) CalculateDelay(attempt int) time.Duration {
	if attempt < 0 {
		return 0
	}
	delay := float64(rp.InitialDelay) * (float64(1) + float64(attempt)*rp.Factor)
	return time.Duration(delay)
}
