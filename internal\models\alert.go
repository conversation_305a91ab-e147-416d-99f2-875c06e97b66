package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Alert 表示警报表
type Alert struct {
	gorm.Model
	ServerID  uint      `gorm:"not null"`
	Server    Server    `gorm:"foreignKey:ServerID"` // 属于服务器
	Metric    string    `gorm:"not null"`            // 例如："cpu_usage", "memory_usage"
	Threshold float64   `gorm:"not null"`
	Operator  string    `gorm:"size:10;not null"` // 例如：">", "<", "="
	Value     float64   `gorm:"not null"`
	Message   string    `gorm:"type:text"`
	Level     string    `gorm:"size:20;not null"` // 例如："info", "warning", "critical"
	Status    string    `gorm:"size:20;not null"` // 例如："active", "resolved"
	Timestamp time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}

// Validate validates the Alert struct fields
func (a *Alert) Validate() error {
	if a.ServerID == 0 {
		return fmt.Errorf("server ID cannot be empty")
	}
	if a.Metric == "" {
		return fmt.Errorf("metric cannot be empty")
	}
	if a.Operator == "" {
		return fmt.Errorf("operator cannot be empty")
	}
	if a.Level == "" {
		return fmt.Errorf("level cannot be empty")
	}
	if a.Status == "" {
		return fmt.Errorf("status cannot be empty")
	}

	// Validate Level
	validLevels := map[string]bool{"info": true, "warning": true, "critical": true}
	if _, ok := validLevels[a.Level]; !ok {
		return fmt.Errorf("invalid alert level: %s", a.Level)
	}

	// Validate Status
	validStatuses := map[string]bool{"active": true, "resolved": true}
	if _, ok := validStatuses[a.Status]; !ok {
		return fmt.Errorf("invalid alert status: %s", a.Status)
	}

	// Validate Operator
	validOperators := map[string]bool{">": true, "<": true, "=": true, ">=": true, "<=": true, "!=": true}
	if _, ok := validOperators[a.Operator]; !ok {
		return fmt.Errorf("invalid operator: %s", a.Operator)
	}

	return nil
}
