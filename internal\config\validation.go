package config

import (
	"fmt"
	"net"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// ValidationError 验证错误
type ValidationError struct {
	Field   string
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error for field '%s': %s", e.Field, e.Message)
}

// ValidationErrors 多个验证错误
type ValidationErrors []ValidationError

func (e ValidationErrors) Error() string {
	var messages []string
	for _, err := range e {
		messages = append(messages, err.Error())
	}
	return strings.Join(messages, "; ")
}

// Validate 验证配置
func (c *Config) Validate() error {
	var errors ValidationErrors

	// 验证系统配置
	if err := c.validateSystem(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "system", Message: err.Error()})
		}
	}

	// 验证数据库配置
	if err := c.validateDatabase(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "database", Message: err.Error()})
		}
	}

	// 验证调度器配置
	if err := c.validateScheduler(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "scheduler", Message: err.Error()})
		}
	}

	// 验证服务器配置
	if err := c.validateServers(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "servers", Message: err.Error()})
		}
	}

	// 验证对端配置
	if err := c.validatePeer(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "peer", Message: err.Error()})
		}
	}

	// 验证测试配置
	if err := c.validateTest(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "test", Message: err.Error()})
		}
	}

	// 验证Web配置
	if err := c.validateWeb(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "web", Message: err.Error()})
		}
	}

	// 验证API配置
	if err := c.validateAPI(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "api", Message: err.Error()})
		}
	}

	// 验证同步配置
	if err := c.validateSync(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "sync", Message: err.Error()})
		}
	}

	// 验证日志配置
	if err := c.validateLog(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "log", Message: err.Error()})
		}
	}

	// 验证安全配置 (Validate security configuration)
	if err := c.validateSecurity(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "security", Message: err.Error()})
		}
	}

	if len(errors) > 0 {
		return errors
	}

	return nil
}

// validateSystem 验证系统配置
func (c *Config) validateSystem() error {
	var errors ValidationErrors

	if c.System.Name == "" {
		errors = append(errors, ValidationError{Field: "system.name", Message: "name is required"})
	}

	if c.System.Version == "" {
		errors = append(errors, ValidationError{Field: "system.version", Message: "version is required"})
	}

	if c.System.Environment == "" {
		errors = append(errors, ValidationError{Field: "system.environment", Message: "environment is required"})
	} else {
		validEnvs := []string{"development", "testing", "staging", "production"}
		if !contains(validEnvs, c.System.Environment) {
			errors = append(errors, ValidationError{
				Field:   "system.environment",
				Message: fmt.Sprintf("environment must be one of: %s", strings.Join(validEnvs, ", ")),
			})
		}
	}

	if c.System.DataDir == "" {
		errors = append(errors, ValidationError{Field: "system.data_dir", Message: "data_dir is required"})
	}

	if c.System.Mode == "" {
		errors = append(errors, ValidationError{Field: "system.mode", Message: "mode is required"})
	} else {
		validModes := []string{"server", "client", "both"}
		if !contains(validModes, c.System.Mode) {
			errors = append(errors, ValidationError{
				Field:   "system.mode",
				Message: fmt.Sprintf("mode must be one of: %s", strings.Join(validModes, ", ")),
			})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateDatabase 验证数据库配置
func (c *Config) validateDatabase() error {
	var errors ValidationErrors

	if c.Database.Path == "" {
		errors = append(errors, ValidationError{Field: "database.path", Message: "path is required"})
	} else {
		// 检查数据库目录是否可写
		dir := filepath.Dir(c.Database.Path)
		if err := os.MkdirAll(dir, 0755); err != nil {
			errors = append(errors, ValidationError{
				Field:   "database.path",
				Message: fmt.Sprintf("cannot create database directory: %v", err),
			})
		}
	}

	if c.Database.MaxOpenConns <= 0 {
		errors = append(errors, ValidationError{Field: "database.max_open_conns", Message: "max_open_conns must be positive"})
	}

	if c.Database.MaxIdleConns < 0 {
		errors = append(errors, ValidationError{Field: "database.max_idle_conns", Message: "max_idle_conns cannot be negative"})
	}

	if c.Database.MaxIdleConns > c.Database.MaxOpenConns {
		errors = append(errors, ValidationError{
			Field:   "database.max_idle_conns",
			Message: "max_idle_conns cannot be greater than max_open_conns",
		})
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateScheduler 验证调度器配置
func (c *Config) validateScheduler() error {
	var errors ValidationErrors

	if c.Scheduler.Mode == "" {
		errors = append(errors, ValidationError{Field: "scheduler.mode", Message: "mode is required"})
	} else {
		validModes := []string{"odd", "even", "both"}
		if !contains(validModes, c.Scheduler.Mode) {
			errors = append(errors, ValidationError{
				Field:   "scheduler.mode",
				Message: fmt.Sprintf("mode must be one of: %s", strings.Join(validModes, ", ")),
			})
		}
	}

	if c.Scheduler.Timezone == "" {
		errors = append(errors, ValidationError{Field: "scheduler.timezone", Message: "timezone is required"})
	} else {
		if _, err := time.LoadLocation(c.Scheduler.Timezone); err != nil {
			errors = append(errors, ValidationError{
				Field:   "scheduler.timezone",
				Message: fmt.Sprintf("invalid timezone: %v", err),
			})
		}
	}

	if c.Scheduler.StartHour < 0 || c.Scheduler.StartHour > 23 {
		errors = append(errors, ValidationError{Field: "scheduler.start_hour", Message: "start_hour must be between 0 and 23"})
	}

	if c.Scheduler.EndHour < 0 || c.Scheduler.EndHour > 23 {
		errors = append(errors, ValidationError{Field: "scheduler.end_hour", Message: "end_hour must be between 0 and 23"})
	}

	if c.Scheduler.MaxConcurrent <= 0 {
		errors = append(errors, ValidationError{Field: "scheduler.max_concurrent", Message: "max_concurrent must be positive"})
	}

	if c.Scheduler.RetryAttempts < 0 {
		errors = append(errors, ValidationError{Field: "scheduler.retry_attempts", Message: "retry_attempts cannot be negative"})
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateServers 验证服务器配置
func (c *Config) validateServers() error {
	var errors ValidationErrors

	if len(c.Servers) == 0 {
		errors = append(errors, ValidationError{Field: "servers", Message: "at least one server is required"})
		return errors
	}

	nameMap := make(map[string]bool)
	ipMap := make(map[string]bool)

	for i, server := range c.Servers {
		prefix := fmt.Sprintf("servers[%d]", i)

		if server.Name == "" {
			errors = append(errors, ValidationError{Field: prefix + ".name", Message: "name is required"})
		} else {
			if nameMap[server.Name] {
				errors = append(errors, ValidationError{Field: prefix + ".name", Message: "duplicate server name"})
			}
			nameMap[server.Name] = true
		}

		if server.IP == "" {
			errors = append(errors, ValidationError{Field: prefix + ".ip", Message: "ip is required"})
		} else {
			if net.ParseIP(server.IP) == nil {
				errors = append(errors, ValidationError{Field: prefix + ".ip", Message: "invalid IP address"})
			} else {
				ipKey := fmt.Sprintf("%s:%d", server.IP, server.Port)
				if ipMap[ipKey] {
					errors = append(errors, ValidationError{Field: prefix + ".ip", Message: "duplicate server IP:port"})
				}
				ipMap[ipKey] = true
			}
		}

		if server.Port <= 0 || server.Port > 65535 {
			errors = append(errors, ValidationError{Field: prefix + ".port", Message: "port must be between 1 and 65535"})
		}

		if server.Priority < 0 {
			errors = append(errors, ValidationError{Field: prefix + ".priority", Message: "priority cannot be negative"})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validatePeer 验证对端配置
func (c *Config) validatePeer() error {
	var errors ValidationErrors

	if c.Peer.Enabled {
		if c.Peer.IP == "" {
			errors = append(errors, ValidationError{Field: "peer.ip", Message: "ip is required when peer is enabled"})
		} else if net.ParseIP(c.Peer.IP) == nil {
			errors = append(errors, ValidationError{Field: "peer.ip", Message: "invalid IP address"})
		}

		if c.Peer.Port <= 0 || c.Peer.Port > 65535 {
			errors = append(errors, ValidationError{Field: "peer.port", Message: "port must be between 1 and 65535"})
		}

		if c.Peer.Username == "" {
			errors = append(errors, ValidationError{Field: "peer.username", Message: "username is required when peer is enabled"})
		}

		if c.Peer.Password == "" {
			errors = append(errors, ValidationError{Field: "peer.password", Message: "password is required when peer is enabled"})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateTest 验证测试配置
func (c *Config) validateTest() error {
	var errors ValidationErrors

	if c.Test.Duration <= 0 {
		errors = append(errors, ValidationError{Field: "test.duration", Message: "duration must be positive"})
	}

	if c.Test.Parallel <= 0 {
		errors = append(errors, ValidationError{Field: "test.parallel", Message: "parallel must be positive"})
	}

	if c.Test.Protocol == "" {
		errors = append(errors, ValidationError{Field: "test.protocol", Message: "protocol is required"})
	} else {
		validProtocols := []string{"tcp", "udp", "both"}
		if !contains(validProtocols, c.Test.Protocol) {
			errors = append(errors, ValidationError{
				Field:   "test.protocol",
				Message: fmt.Sprintf("protocol must be one of: %s", strings.Join(validProtocols, ", ")),
			})
		}
	}

	if c.Test.MaxRetries < 0 {
		errors = append(errors, ValidationError{Field: "test.max_retries", Message: "max_retries cannot be negative"})
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateWeb 验证Web配置
func (c *Config) validateWeb() error {
	var errors ValidationErrors

	if c.Web.Enabled {
		if c.Web.Port <= 0 || c.Web.Port > 65535 {
			errors = append(errors, ValidationError{Field: "web.port", Message: "port must be between 1 and 65535"})
		}

		if c.Web.Host == "" {
			errors = append(errors, ValidationError{Field: "web.host", Message: "host is required when web is enabled"})
		}

		if c.Web.TLS.Enabled {
			if c.Web.TLS.CertFile == "" {
				errors = append(errors, ValidationError{Field: "web.tls.cert_file", Message: "cert_file is required when TLS is enabled"})
			}
			if c.Web.TLS.KeyFile == "" {
				errors = append(errors, ValidationError{Field: "web.tls.key_file", Message: "key_file is required when TLS is enabled"})
			}
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateAPI 验证API配置
func (c *Config) validateAPI() error {
	var errors ValidationErrors

	if c.API.Enabled {
		if c.API.Port <= 0 || c.API.Port > 65535 {
			errors = append(errors, ValidationError{Field: "api.port", Message: "port must be between 1 and 65535"})
		}

		if c.API.Host == "" {
			errors = append(errors, ValidationError{Field: "api.host", Message: "host is required when API is enabled"})
		}

		if c.API.RateLimit < 0 {
			errors = append(errors, ValidationError{Field: "api.rate_limit", Message: "rate_limit cannot be negative"})
		}

		if c.API.TLS.Enabled {
			if c.API.TLS.CertFile == "" {
				errors = append(errors, ValidationError{Field: "api.tls.cert_file", Message: "cert_file is required when TLS is enabled"})
			}
			if c.API.TLS.KeyFile == "" {
				errors = append(errors, ValidationError{Field: "api.tls.key_file", Message: "key_file is required when TLS is enabled"})
			}
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateSync 验证同步配置
func (c *Config) validateSync() error {
	var errors ValidationErrors

	if c.Sync.Enabled {
		if c.Sync.BatchSize <= 0 {
			errors = append(errors, ValidationError{Field: "sync.batch_size", Message: "batch_size must be positive"})
		}

		if c.Sync.MaxRetries < 0 {
			errors = append(errors, ValidationError{Field: "sync.max_retries", Message: "max_retries cannot be negative"})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateLog 验证日志配置
func (c *Config) validateLog() error {
	var errors ValidationErrors

	if c.Log.Level == "" {
		errors = append(errors, ValidationError{Field: "log.level", Message: "level is required"})
	} else {
		validLevels := []string{"debug", "info", "warn", "error", "fatal", "panic"}
		if !contains(validLevels, c.Log.Level) {
			errors = append(errors, ValidationError{
				Field:   "log.level",
				Message: fmt.Sprintf("level must be one of: %s", strings.Join(validLevels, ", ")),
			})
		}
	}

	if c.Log.Format == "" {
		errors = append(errors, ValidationError{Field: "log.format", Message: "format is required"})
	} else {
		validFormats := []string{"json", "text"}
		if !contains(validFormats, c.Log.Format) {
			errors = append(errors, ValidationError{
				Field:   "log.format",
				Message: fmt.Sprintf("format must be one of: %s", strings.Join(validFormats, ", ")),
			})
		}
	}

	if c.Log.Output == "" {
		errors = append(errors, ValidationError{Field: "log.output", Message: "output is required"})
	} else {
		validOutputs := []string{"stdout", "stderr", "file"}
		if !contains(validOutputs, c.Log.Output) {
			errors = append(errors, ValidationError{
				Field:   "log.output",
				Message: fmt.Sprintf("output must be one of: %s", strings.Join(validOutputs, ", ")),
			})
		}

		if c.Log.Output == "file" && c.Log.File == "" {
			errors = append(errors, ValidationError{Field: "log.file", Message: "file is required when output is 'file'"})
		}
	}

	if c.Log.MaxSize <= 0 {
		errors = append(errors, ValidationError{Field: "log.max_size", Message: "max_size must be positive"})
	}

	if c.Log.MaxBackups < 0 {
		errors = append(errors, ValidationError{Field: "log.max_backups", Message: "max_backups cannot be negative"})
	}

	if c.Log.MaxAge < 0 {
		errors = append(errors, ValidationError{Field: "log.max_age", Message: "max_age cannot be negative"})
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// contains 检查字符串切片是否包含指定值
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// isValidName 检查名称是否有效
func isValidName(name string) bool {
	// 名称只能包含字母、数字、连字符和下划线
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, name)
	return matched
}

// validateSecurity 验证安全配置 (Validate security configuration)
func (c *Config) validateSecurity() error {
	var errors ValidationErrors

	// 验证加密配置 (Validate encryption configuration)
	if err := c.validateEncryption(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "security.encryption", Message: err.Error()})
		}
	}

	// 验证密钥管理配置 (Validate key manager configuration)
	if err := c.validateKeyManager(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "security.key_manager", Message: err.Error()})
		}
	}

	// 验证认证配置 (Validate authentication configuration)
	if err := c.validateAuth(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "security.auth", Message: err.Error()})
		}
	}

	// 验证重放攻击防护配置 (Validate replay protection configuration)
	if err := c.validateReplayProtection(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "security.replay_protection", Message: err.Error()})
		}
	}

	// 验证消息完整性配置 (Validate message integrity configuration)
	if err := c.validateMessageIntegrity(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "security.message_integrity", Message: err.Error()})
		}
	}

	// 验证速率限制配置 (Validate rate limiting configuration)
	if err := c.validateRateLimiting(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "security.rate_limiting", Message: err.Error()})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateEncryption 验证加密配置 (Validate encryption configuration)
func (c *Config) validateEncryption() error {
	var errors ValidationErrors

	if c.Security.Encryption.Enabled {
		if c.Security.Encryption.Algorithm == "" {
			errors = append(errors, ValidationError{
				Field:   "security.encryption.algorithm",
				Message: "algorithm is required when encryption is enabled",
			})
		} else {
			validAlgorithms := []string{"AES-256-GCM", "AES-192-GCM", "AES-128-GCM"}
			if !contains(validAlgorithms, c.Security.Encryption.Algorithm) {
				errors = append(errors, ValidationError{
					Field:   "security.encryption.algorithm",
					Message: fmt.Sprintf("algorithm must be one of: %s", strings.Join(validAlgorithms, ", ")),
				})
			}
		}

		if c.Security.Encryption.KeySize <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.encryption.key_size",
				Message: "key_size must be positive when encryption is enabled",
			})
		} else {
			validKeySizes := []int{128, 192, 256}
			validSize := false
			for _, size := range validKeySizes {
				if c.Security.Encryption.KeySize == size {
					validSize = true
					break
				}
			}
			if !validSize {
				errors = append(errors, ValidationError{
					Field:   "security.encryption.key_size",
					Message: "key_size must be 128, 192, or 256",
				})
			}
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateKeyManager 验证密钥管理配置 (Validate key manager configuration)
func (c *Config) validateKeyManager() error {
	var errors ValidationErrors

	if c.Security.KeyManager.Enabled {
		if c.Security.KeyManager.RotationInterval <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.key_manager.rotation_interval",
				Message: "rotation_interval must be positive when key manager is enabled",
			})
		} else if c.Security.KeyManager.RotationInterval < time.Minute {
			errors = append(errors, ValidationError{
				Field:   "security.key_manager.rotation_interval",
				Message: "rotation_interval should not be less than 1 minute for security reasons",
			})
		}

		if c.Security.KeyManager.KeyTTL <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.key_manager.key_ttl",
				Message: "key_ttl must be positive when key manager is enabled",
			})
		} else if c.Security.KeyManager.KeyTTL <= c.Security.KeyManager.RotationInterval {
			errors = append(errors, ValidationError{
				Field:   "security.key_manager.key_ttl",
				Message: "key_ttl should be greater than rotation_interval",
			})
		}

		if c.Security.KeyManager.MaxKeyHistory <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.key_manager.max_key_history",
				Message: "max_key_history must be positive when key manager is enabled",
			})
		} else if c.Security.KeyManager.MaxKeyHistory > 100 {
			errors = append(errors, ValidationError{
				Field:   "security.key_manager.max_key_history",
				Message: "max_key_history should not exceed 100 for performance reasons",
			})
		}

		if c.Security.KeyManager.MasterPassword == "" {
			errors = append(errors, ValidationError{
				Field:   "security.key_manager.master_password",
				Message: "master_password is required when key manager is enabled",
			})
		} else if len(c.Security.KeyManager.MasterPassword) < 8 {
			errors = append(errors, ValidationError{
				Field:   "security.key_manager.master_password",
				Message: "master_password should be at least 8 characters long",
			})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateAuth 验证认证配置 (Validate authentication configuration)
func (c *Config) validateAuth() error {
	var errors ValidationErrors

	if c.Security.Auth.Enabled {
		if c.Security.Auth.TokenTTL <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.auth.token_ttl",
				Message: "token_ttl must be positive when auth is enabled",
			})
		} else if c.Security.Auth.TokenTTL > 24*time.Hour {
			errors = append(errors, ValidationError{
				Field:   "security.auth.token_ttl",
				Message: "token_ttl should not exceed 24 hours for security reasons",
			})
		}

		if c.Security.Auth.RefreshTokenTTL <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.auth.refresh_token_ttl",
				Message: "refresh_token_ttl must be positive when auth is enabled",
			})
		} else if c.Security.Auth.RefreshTokenTTL <= c.Security.Auth.TokenTTL {
			errors = append(errors, ValidationError{
				Field:   "security.auth.refresh_token_ttl",
				Message: "refresh_token_ttl should be greater than token_ttl",
			})
		}

		if c.Security.Auth.MaxLoginAttempts <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.auth.max_login_attempts",
				Message: "max_login_attempts must be positive when auth is enabled",
			})
		} else if c.Security.Auth.MaxLoginAttempts > 20 {
			errors = append(errors, ValidationError{
				Field:   "security.auth.max_login_attempts",
				Message: "max_login_attempts should not exceed 20 for security reasons",
			})
		}

		if c.Security.Auth.LockoutDuration <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.auth.lockout_duration",
				Message: "lockout_duration must be positive when auth is enabled",
			})
		} else if c.Security.Auth.LockoutDuration < time.Minute {
			errors = append(errors, ValidationError{
				Field:   "security.auth.lockout_duration",
				Message: "lockout_duration should be at least 1 minute",
			})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateReplayProtection 验证重放攻击防护配置 (Validate replay protection configuration)
func (c *Config) validateReplayProtection() error {
	var errors ValidationErrors

	if c.Security.ReplayProtection.Enabled {
		if c.Security.ReplayProtection.TimeWindow <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.replay_protection.time_window",
				Message: "time_window must be positive when replay protection is enabled",
			})
		} else if c.Security.ReplayProtection.TimeWindow < time.Second {
			errors = append(errors, ValidationError{
				Field:   "security.replay_protection.time_window",
				Message: "time_window should be at least 1 second",
			})
		}

		if c.Security.ReplayProtection.NonceCache <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.replay_protection.nonce_cache",
				Message: "nonce_cache must be positive when replay protection is enabled",
			})
		} else if c.Security.ReplayProtection.NonceCache > 100000 {
			errors = append(errors, ValidationError{
				Field:   "security.replay_protection.nonce_cache",
				Message: "nonce_cache should not exceed 100000 for performance reasons",
			})
		}

		if c.Security.ReplayProtection.CacheTTL <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.replay_protection.cache_ttl",
				Message: "cache_ttl must be positive when replay protection is enabled",
			})
		} else if c.Security.ReplayProtection.CacheTTL <= c.Security.ReplayProtection.TimeWindow {
			errors = append(errors, ValidationError{
				Field:   "security.replay_protection.cache_ttl",
				Message: "cache_ttl should be greater than time_window",
			})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateMessageIntegrity 验证消息完整性配置 (Validate message integrity configuration)
func (c *Config) validateMessageIntegrity() error {
	var errors ValidationErrors

	if c.Security.MessageIntegrity.Enabled {
		if c.Security.MessageIntegrity.ChecksumAlgorithm == "" {
			errors = append(errors, ValidationError{
				Field:   "security.message_integrity.checksum_algorithm",
				Message: "checksum_algorithm is required when message integrity is enabled",
			})
		} else {
			validAlgorithms := []string{"SHA256", "SHA512", "SHA3-256", "SHA3-512"}
			if !contains(validAlgorithms, c.Security.MessageIntegrity.ChecksumAlgorithm) {
				errors = append(errors, ValidationError{
					Field:   "security.message_integrity.checksum_algorithm",
					Message: fmt.Sprintf("checksum_algorithm must be one of: %s", strings.Join(validAlgorithms, ", ")),
				})
			}
		}

		if c.Security.MessageIntegrity.EnableSequenceValidation {
			if c.Security.MessageIntegrity.MaxSequenceGap <= 0 {
				errors = append(errors, ValidationError{
					Field:   "security.message_integrity.max_sequence_gap",
					Message: "max_sequence_gap must be positive when sequence validation is enabled",
				})
			} else if c.Security.MessageIntegrity.MaxSequenceGap > 10000 {
				errors = append(errors, ValidationError{
					Field:   "security.message_integrity.max_sequence_gap",
					Message: "max_sequence_gap should not exceed 10000 for performance reasons",
				})
			}
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateRateLimiting 验证速率限制配置 (Validate rate limiting configuration)
func (c *Config) validateRateLimiting() error {
	var errors ValidationErrors

	if c.Security.RateLimiting.Enabled {
		if c.Security.RateLimiting.RequestsPerSec <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.rate_limiting.requests_per_sec",
				Message: "requests_per_sec must be positive when rate limiting is enabled",
			})
		} else if c.Security.RateLimiting.RequestsPerSec > 10000 {
			errors = append(errors, ValidationError{
				Field:   "security.rate_limiting.requests_per_sec",
				Message: "requests_per_sec should not exceed 10000 for performance reasons",
			})
		}

		if c.Security.RateLimiting.BurstSize <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.rate_limiting.burst_size",
				Message: "burst_size must be positive when rate limiting is enabled",
			})
		} else if c.Security.RateLimiting.BurstSize < c.Security.RateLimiting.RequestsPerSec {
			errors = append(errors, ValidationError{
				Field:   "security.rate_limiting.burst_size",
				Message: "burst_size should be at least equal to requests_per_sec",
			})
		}

		if c.Security.RateLimiting.CleanupInterval <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.rate_limiting.cleanup_interval",
				Message: "cleanup_interval must be positive when rate limiting is enabled",
			})
		}

		if c.Security.RateLimiting.BanDuration <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.rate_limiting.ban_duration",
				Message: "ban_duration must be positive when rate limiting is enabled",
			})
		}

		if c.Security.RateLimiting.MaxViolations <= 0 {
			errors = append(errors, ValidationError{
				Field:   "security.rate_limiting.max_violations",
				Message: "max_violations must be positive when rate limiting is enabled",
			})
		} else if c.Security.RateLimiting.MaxViolations > 100 {
			errors = append(errors, ValidationError{
				Field:   "security.rate_limiting.max_violations",
				Message: "max_violations should not exceed 100 for security reasons",
			})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}
