package alert

import (
	"fmt"
	"log"
	"sync"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/monitor"
)

// AlertLevel 告警级别
type AlertLevel int

const (
	LevelInfo AlertLevel = iota
	LevelWarning
	LevelCritical
)

// String 返回告警级别字符串
func (l AlertLevel) String() string {
	switch l {
	case LevelInfo:
		return "信息"
	case LevelWarning:
		return "警告"
	case LevelCritical:
		return "严重"
	default:
		return "未知"
	}
}

// Alert 告警信息
type Alert struct {
	ID          string     `json:"id"`          // 告警ID
	Level       AlertLevel `json:"level"`       // 告警级别
	Type        string     `json:"type"`        // 告警类型
	Title       string     `json:"title"`       // 告警标题
	Message     string     `json:"message"`     // 告警消息
	Hostname    string     `json:"hostname"`    // 主机名
	Timestamp   time.Time  `json:"timestamp"`   // 告警时间
	Resolved    bool       `json:"resolved"`    // 是否已解决
	ResolvedAt  *time.Time `json:"resolved_at"` // 解决时间
}

// Rule 告警规则
type Rule struct {
	Name        string     `json:"name"`        // 规则名称
	Type        string     `json:"type"`        // 监控类型 (cpu, memory, disk, network)
	Threshold   float64    `json:"threshold"`   // 阈值
	Operator    string     `json:"operator"`    // 操作符 (>, <, >=, <=, ==)
	Level       AlertLevel `json:"level"`       // 告警级别
	Duration    int        `json:"duration"`    // 持续时间(秒)
	Enabled     bool       `json:"enabled"`     // 是否启用
	Description string     `json:"description"` // 规则描述
}

// Manager 告警管理器
type Manager struct {
	config        *config.Config
	systemMonitor *monitor.SystemMonitor
	rules         []Rule
	activeAlerts  map[string]*Alert
	alertHistory  []*Alert
	mu            sync.RWMutex
	running       bool
	stopCh        chan struct{}
	
	// 告警状态跟踪
	alertStates   map[string]*AlertState
}

// AlertState 告警状态
type AlertState struct {
	RuleName    string    // 规则名称
	StartTime   time.Time // 开始时间
	LastCheck   time.Time // 最后检查时间
	Triggered   bool      // 是否已触发
	Value       float64   // 当前值
}

// NewManager 创建告警管理器
func NewManager(cfg *config.Config, sysMon *monitor.SystemMonitor) *Manager {
	return &Manager{
		config:        cfg,
		systemMonitor: sysMon,
		rules:         getDefaultRules(),
		activeAlerts:  make(map[string]*Alert),
		alertHistory:  make([]*Alert, 0),
		alertStates:   make(map[string]*AlertState),
		running:       false,
		stopCh:        make(chan struct{}),
	}
}

// getDefaultRules 获取默认告警规则
func getDefaultRules() []Rule {
	return []Rule{
		{
			Name:        "CPU使用率过高",
			Type:        "cpu",
			Threshold:   80.0,
			Operator:    ">",
			Level:       LevelWarning,
			Duration:    300, // 5分钟
			Enabled:     true,
			Description: "CPU使用率超过80%持续5分钟",
		},
		{
			Name:        "CPU使用率严重过高",
			Type:        "cpu",
			Threshold:   95.0,
			Operator:    ">",
			Level:       LevelCritical,
			Duration:    60, // 1分钟
			Enabled:     true,
			Description: "CPU使用率超过95%持续1分钟",
		},
		{
			Name:        "内存使用率过高",
			Type:        "memory",
			Threshold:   85.0,
			Operator:    ">",
			Level:       LevelWarning,
			Duration:    300, // 5分钟
			Enabled:     true,
			Description: "内存使用率超过85%持续5分钟",
		},
		{
			Name:        "内存使用率严重过高",
			Type:        "memory",
			Threshold:   95.0,
			Operator:    ">",
			Level:       LevelCritical,
			Duration:    60, // 1分钟
			Enabled:     true,
			Description: "内存使用率超过95%持续1分钟",
		},
		{
			Name:        "磁盘使用率过高",
			Type:        "disk",
			Threshold:   90.0,
			Operator:    ">",
			Level:       LevelWarning,
			Duration:    600, // 10分钟
			Enabled:     true,
			Description: "磁盘使用率超过90%持续10分钟",
		},
		{
			Name:        "磁盘使用率严重过高",
			Type:        "disk",
			Threshold:   98.0,
			Operator:    ">",
			Level:       LevelCritical,
			Duration:    300, // 5分钟
			Enabled:     true,
			Description: "磁盘使用率超过98%持续5分钟",
		},
	}
}

// Start 启动告警管理器
func (m *Manager) Start() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("告警管理器已经在运行")
	}

	m.running = true
	m.stopCh = make(chan struct{})

	// 启动监控循环
	go m.monitorLoop()

	log.Println("告警管理器已启动")
	return nil
}

// Stop 停止告警管理器
func (m *Manager) Stop() {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return
	}

	m.running = false
	close(m.stopCh)

	log.Println("告警管理器已停止")
}

// monitorLoop 监控循环
func (m *Manager) monitorLoop() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-m.stopCh:
			return
		case <-ticker.C:
			m.checkRules()
		}
	}
}

// checkRules 检查所有规则
func (m *Manager) checkRules() {
	// 获取系统信息
	systemInfo, err := m.systemMonitor.GetSystemInfo()
	if err != nil {
		log.Printf("获取系统信息失败: %v", err)
		return
	}

	now := time.Now()

	for _, rule := range m.rules {
		if !rule.Enabled {
			continue
		}

		// 获取当前值
		value := m.getValueByType(rule.Type, systemInfo)
		
		// 检查是否满足条件
		triggered := m.evaluateCondition(value, rule.Threshold, rule.Operator)

		// 获取或创建告警状态
		state, exists := m.alertStates[rule.Name]
		if !exists {
			state = &AlertState{
				RuleName:  rule.Name,
				StartTime: now,
				Triggered: false,
			}
			m.alertStates[rule.Name] = state
		}

		state.LastCheck = now
		state.Value = value

		if triggered {
			if !state.Triggered {
				// 开始触发
				state.Triggered = true
				state.StartTime = now
			} else {
				// 检查是否达到持续时间
				duration := now.Sub(state.StartTime).Seconds()
				if duration >= float64(rule.Duration) {
					// 触发告警
					m.triggerAlert(rule, value, systemInfo.Hostname)
					// 重置状态，避免重复告警
					state.StartTime = now
				}
			}
		} else {
			if state.Triggered {
				// 条件不再满足，解决告警
				m.resolveAlert(rule.Name)
				state.Triggered = false
			}
		}
	}
}

// getValueByType 根据类型获取值
func (m *Manager) getValueByType(alertType string, systemInfo *monitor.SystemInfo) float64 {
	switch alertType {
	case "cpu":
		return systemInfo.CPUUsage
	case "memory":
		return systemInfo.MemoryUsage
	case "disk":
		return systemInfo.DiskUsage
	case "network_rx":
		return float64(systemInfo.NetworkRx) / 1024 / 1024 // MB
	case "network_tx":
		return float64(systemInfo.NetworkTx) / 1024 / 1024 // MB
	default:
		return 0
	}
}

// evaluateCondition 评估条件
func (m *Manager) evaluateCondition(value, threshold float64, operator string) bool {
	switch operator {
	case ">":
		return value > threshold
	case "<":
		return value < threshold
	case ">=":
		return value >= threshold
	case "<=":
		return value <= threshold
	case "==":
		return value == threshold
	default:
		return false
	}
}

// triggerAlert 触发告警
func (m *Manager) triggerAlert(rule Rule, value float64, hostname string) {
	alertID := fmt.Sprintf("%s_%d", rule.Name, time.Now().Unix())
	
	alert := &Alert{
		ID:        alertID,
		Level:     rule.Level,
		Type:      rule.Type,
		Title:     rule.Name,
		Message:   fmt.Sprintf("%s: 当前值 %.2f, 阈值 %.2f", rule.Description, value, rule.Threshold),
		Hostname:  hostname,
		Timestamp: time.Now(),
		Resolved:  false,
	}

	m.mu.Lock()
	m.activeAlerts[rule.Name] = alert
	m.alertHistory = append(m.alertHistory, alert)
	m.mu.Unlock()

	log.Printf("🚨 告警触发: %s - %s", alert.Title, alert.Message)

	// 这里可以添加通知逻辑（邮件、短信、Webhook等）
	m.sendNotification(alert)
}

// resolveAlert 解决告警
func (m *Manager) resolveAlert(ruleName string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if alert, exists := m.activeAlerts[ruleName]; exists {
		now := time.Now()
		alert.Resolved = true
		alert.ResolvedAt = &now
		delete(m.activeAlerts, ruleName)

		log.Printf("✅ 告警解决: %s", alert.Title)
	}
}

// sendNotification 发送通知
func (m *Manager) sendNotification(alert *Alert) {
	// 这里实现具体的通知逻辑
	// 例如：邮件、短信、Webhook、企业微信等
	
	log.Printf("📧 发送通知: [%s] %s - %s", alert.Level.String(), alert.Title, alert.Message)
	
	// 示例：简单的日志通知
	// 实际应用中可以集成邮件服务、短信服务等
}

// GetActiveAlerts 获取活跃告警
func (m *Manager) GetActiveAlerts() []*Alert {
	m.mu.RLock()
	defer m.mu.RUnlock()

	alerts := make([]*Alert, 0, len(m.activeAlerts))
	for _, alert := range m.activeAlerts {
		alerts = append(alerts, alert)
	}

	return alerts
}

// GetAlertHistory 获取告警历史
func (m *Manager) GetAlertHistory(limit int) []*Alert {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if limit <= 0 || limit > len(m.alertHistory) {
		limit = len(m.alertHistory)
	}

	// 返回最近的告警
	start := len(m.alertHistory) - limit
	if start < 0 {
		start = 0
	}

	return m.alertHistory[start:]
}

// GetRules 获取告警规则
func (m *Manager) GetRules() []Rule {
	return m.rules
}

// UpdateRule 更新告警规则
func (m *Manager) UpdateRule(name string, rule Rule) error {
	for i, r := range m.rules {
		if r.Name == name {
			m.rules[i] = rule
			return nil
		}
	}
	return fmt.Errorf("规则不存在: %s", name)
}

// AddRule 添加告警规则
func (m *Manager) AddRule(rule Rule) {
	m.rules = append(m.rules, rule)
}

// IsRunning 检查是否运行中
func (m *Manager) IsRunning() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.running
}
