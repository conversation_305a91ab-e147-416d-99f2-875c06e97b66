package cli

import (
	"strings"
	"testing"
	"time"
)

func TestParseBasicArgs(t *testing.T) {
	parser := NewParser("monitor")

	args, err := parser.Parse([]string{
		"--version",
		"--config", "/path/to/config.yaml",
		"--log-level", "debug",
		"--data-dir", "/custom/data",
	})

	if err != nil {
		t.Fatalf("Failed to parse args: %v", err)
	}

	if !args.Version {
		t.Error("Expected version flag to be true")
	}

	if args.Config != "/path/to/config.yaml" {
		t.<PERSON>("Expected config '/path/to/config.yaml', got '%s'", args.Config)
	}

	if args.LogLevel != "debug" {
		t.Errorf("Expected log level 'debug', got '%s'", args.LogLevel)
	}

	if args.DataDir != "/custom/data" {
		t.<PERSON>("Expected data dir '/custom/data', got '%s'", args.DataDir)
	}
}

func TestParseServerMode(t *testing.T) {
	parser := NewParser("monitor")

	args, err := parser.Parse([]string{
		"--server",
		"--port", "9443",
		"--web-port", "9080",
		"--password", "test123",
		"--tls",
		"--tls-cert", "/path/to/cert.pem",
		"--tls-key", "/path/to/key.pem",
	})

	if err != nil {
		t.Fatalf("Failed to parse server args: %v", err)
	}

	if !args.ServerMode {
		t.Error("Expected server mode to be true")
	}

	if args.Mode != ModeServer {
		t.Errorf("Expected mode 'server', got '%s'", args.Mode)
	}

	if args.ServerPort != 9443 {
		t.Errorf("Expected server port 9443, got %d", args.ServerPort)
	}

	if args.WebPort != 9080 {
		t.Errorf("Expected web port 9080, got %d", args.WebPort)
	}

	if args.Password != "test123" {
		t.Errorf("Expected password 'test123', got '%s'", args.Password)
	}

	if !args.EnableTLS {
		t.Error("Expected TLS to be enabled")
	}

	if args.TLSCert != "/path/to/cert.pem" {
		t.Errorf("Expected TLS cert '/path/to/cert.pem', got '%s'", args.TLSCert)
	}
}

func TestParseClientMode(t *testing.T) {
	parser := NewParser("monitor")

	args, err := parser.Parse([]string{
		"--client",
		"--name", "test-client",
		"--server-ip", "*************",
		"--server-password", "server123",
		"--test-duration", "60s",
		"--test-parallel", "8",
		"--test-protocol", "udp",
	})

	if err != nil {
		t.Fatalf("Failed to parse client args: %v", err)
	}

	if !args.ClientMode {
		t.Error("Expected client mode to be true")
	}

	if args.Mode != ModeClient {
		t.Errorf("Expected mode 'client', got '%s'", args.Mode)
	}

	if args.ClientName != "test-client" {
		t.Errorf("Expected client name 'test-client', got '%s'", args.ClientName)
	}

	if args.ServerIP != "*************" {
		t.Errorf("Expected server IP '*************', got '%s'", args.ServerIP)
	}

	if args.ServerPassword != "server123" {
		t.Errorf("Expected server password 'server123', got '%s'", args.ServerPassword)
	}

	if args.TestDuration != 60*time.Second {
		t.Errorf("Expected test duration 60s, got %v", args.TestDuration)
	}

	if args.TestParallel != 8 {
		t.Errorf("Expected test parallel 8, got %d", args.TestParallel)
	}

	if args.TestProtocol != "udp" {
		t.Errorf("Expected test protocol 'udp', got '%s'", args.TestProtocol)
	}
}

func TestParseBothMode(t *testing.T) {
	parser := NewParser("monitor")

	args, err := parser.Parse([]string{
		"--server",
		"--client",
		"--name", "dual-client",
		"--port", "8443",
	})

	if err != nil {
		t.Fatalf("Failed to parse both mode args: %v", err)
	}

	if args.Mode != ModeBoth {
		t.Errorf("Expected mode 'both', got '%s'", args.Mode)
	}

	if !args.ServerMode {
		t.Error("Expected server mode to be true")
	}

	if !args.ClientMode {
		t.Error("Expected client mode to be true")
	}
}

func TestParseShortFlags(t *testing.T) {
	parser := NewParser("monitor")

	args, err := parser.Parse([]string{
		"-v",
		"-h",
		"-s",
		"-p", "9000",
		"-n", "short-client",
		"-c", "short-config.yaml",
		"-d",
	})

	if err != nil {
		t.Fatalf("Failed to parse short flags: %v", err)
	}

	if !args.Version {
		t.Error("Expected version flag to be true")
	}

	if !args.Help {
		t.Error("Expected help flag to be true")
	}

	if !args.ServerMode {
		t.Error("Expected server mode to be true")
	}

	if args.ServerPort != 9000 {
		t.Errorf("Expected server port 9000, got %d", args.ServerPort)
	}

	if args.ClientName != "short-client" {
		t.Errorf("Expected client name 'short-client', got '%s'", args.ClientName)
	}

	if args.Config != "short-config.yaml" {
		t.Errorf("Expected config 'short-config.yaml', got '%s'", args.Config)
	}

	if !args.Daemon {
		t.Error("Expected daemon flag to be true")
	}
}

func TestValidationErrors(t *testing.T) {
	tests := []struct {
		name        string
		args        []string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "invalid mode",
			args:        []string{"--mode", "invalid"},
			expectError: true,
			errorMsg:    "invalid mode",
		},
		{
			name:        "invalid server port",
			args:        []string{"--server", "--port", "70000"},
			expectError: true,
			errorMsg:    "invalid server port",
		},
		{
			name:        "invalid web port",
			args:        []string{"--server", "--web-port", "-1"},
			expectError: true,
			errorMsg:    "invalid web port",
		},
		{
			name:        "TLS without cert",
			args:        []string{"--server", "--tls", "--tls-key", "/path/key.pem"},
			expectError: true,
			errorMsg:    "TLS certificate file is required",
		},
		{
			name:        "TLS without key",
			args:        []string{"--server", "--tls", "--tls-cert", "/path/cert.pem"},
			expectError: true,
			errorMsg:    "TLS key file is required",
		},
		{
			name:        "client mode without server IP",
			args:        []string{"--client"},
			expectError: true,
			errorMsg:    "server IP is required",
		},
		{
			name:        "invalid test parallel",
			args:        []string{"--client", "--server-ip", "*******", "--test-parallel", "0"},
			expectError: true,
			errorMsg:    "test parallel must be positive",
		},
		{
			name:        "invalid test protocol",
			args:        []string{"--client", "--server-ip", "*******", "--test-protocol", "invalid"},
			expectError: true,
			errorMsg:    "invalid test protocol",
		},
		{
			name:        "invalid log level",
			args:        []string{"--log-level", "invalid"},
			expectError: true,
			errorMsg:    "invalid log level",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parser := NewParser("monitor")
			_, err := parser.Parse(tt.args)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error containing '%s', but got none", tt.errorMsg)
					return
				}
				if !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("Expected error containing '%s', got '%s'", tt.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
			}
		})
	}
}

func TestDefaultValues(t *testing.T) {
	parser := NewParser("monitor")

	args, err := parser.Parse([]string{})
	if err != nil {
		t.Fatalf("Failed to parse empty args: %v", err)
	}

	// 检查默认值
	if args.Config != "./configs/config.yaml" {
		t.Errorf("Expected default config './configs/config.yaml', got '%s'", args.Config)
	}

	if args.DataDir != "./data" {
		t.Errorf("Expected default data dir './data', got '%s'", args.DataDir)
	}

	if args.LogLevel != "info" {
		t.Errorf("Expected default log level 'info', got '%s'", args.LogLevel)
	}

	if args.Mode != ModeBoth {
		t.Errorf("Expected default mode 'both', got '%s'", args.Mode)
	}

	if args.ServerPort != 8443 {
		t.Errorf("Expected default server port 8443, got %d", args.ServerPort)
	}

	if args.WebPort != 8080 {
		t.Errorf("Expected default web port 8080, got %d", args.WebPort)
	}

	if args.TestDuration != 30*time.Second {
		t.Errorf("Expected default test duration 30s, got %v", args.TestDuration)
	}

	if args.TestParallel != 4 {
		t.Errorf("Expected default test parallel 4, got %d", args.TestParallel)
	}

	if args.TestProtocol != "tcp" {
		t.Errorf("Expected default test protocol 'tcp', got '%s'", args.TestProtocol)
	}
}

func TestClientNameDefault(t *testing.T) {
	parser := NewParser("monitor")

	args, err := parser.Parse([]string{"--client", "--server-ip", "*******"})
	if err != nil {
		t.Fatalf("Failed to parse client args: %v", err)
	}

	// 客户端名称应该有默认值（主机名或unknown-client）
	if args.ClientName == "" {
		t.Error("Expected client name to have a default value")
	}
}
