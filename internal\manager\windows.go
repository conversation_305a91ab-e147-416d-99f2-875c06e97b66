//go:build windows
// +build windows

package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
)

// WindowsManager Windows服务管理器
type WindowsManager struct{}

// NewWindowsManager 创建Windows服务管理器
func NewWindowsManager() *WindowsManager {
	return &WindowsManager{}
}

// ListServices 列出所有Windows服务
func (w *WindowsManager) ListServices(ctx context.Context) ([]*Service, error) {
	// 使用PowerShell获取服务列表
	cmd := `Get-Service | Select-Object Name,DisplayName,Status | ConvertTo-Json`
	output, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return nil, fmt.Errorf("获取Windows服务列表失败: %w", err)
	}

	// 如果只有一个服务，PowerShell返回对象而不是数组
	if !strings.HasPrefix(strings.TrimSpace(output), "[") {
		output = "[" + output + "]"
	}

	var windowsServices []struct {
		Name        string `json:"Name"`
		DisplayName string `json:"DisplayName"`
		Status      string `json:"Status"`
	}

	if err := json.Unmarshal([]byte(output), &windowsServices); err != nil {
		return nil, fmt.Errorf("解析Windows服务列表失败: %w", err)
	}

	var services []*Service
	for _, ws := range windowsServices {
		service := &Service{
			Name:        ws.Name,
			DisplayName: ws.DisplayName,
			Status:      parseWindowsStatus(ws.Status),
			Type:        "windows",
		}

		// 获取详细信息
		w.enrichServiceInfo(ctx, service)

		services = append(services, service)
	}

	return services, nil
}

// GetService 获取指定Windows服务的详细信息
func (w *WindowsManager) GetService(ctx context.Context, name string) (*Service, error) {
	if !w.ServiceExists(ctx, name) {
		return nil, fmt.Errorf("服务不存在: %s", name)
	}

	service := &Service{
		Name: name,
		Type: "windows",
	}

	if err := w.enrichServiceInfo(ctx, service); err != nil {
		return nil, err
	}

	return service, nil
}

// enrichServiceInfo 丰富Windows服务信息
func (w *WindowsManager) enrichServiceInfo(ctx context.Context, service *Service) error {
	// 获取服务详细信息
	cmd := fmt.Sprintf(`Get-Service -Name "%s" | Select-Object Name,DisplayName,Status,StartType | ConvertTo-Json`, service.Name)
	output, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return err
	}

	var serviceInfo struct {
		Name        string `json:"Name"`
		DisplayName string `json:"DisplayName"`
		Status      string `json:"Status"`
		StartType   string `json:"StartType"`
	}

	if err := json.Unmarshal([]byte(output), &serviceInfo); err != nil {
		return err
	}

	service.DisplayName = serviceInfo.DisplayName
	service.Status = parseWindowsStatus(serviceInfo.Status)

	// 获取进程信息
	if service.Status == StatusRunning {
		w.getProcessInfo(ctx, service)
	}

	return nil
}

// getProcessInfo 获取服务进程信息
func (w *WindowsManager) getProcessInfo(ctx context.Context, service *Service) {
	cmd := fmt.Sprintf(`Get-WmiObject -Class Win32_Service -Filter "Name='%s'" | Select-Object ProcessId | ConvertTo-Json`, service.Name)
	output, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return
	}

	var processInfo struct {
		ProcessId int `json:"ProcessId"`
	}

	if err := json.Unmarshal([]byte(output), &processInfo); err == nil {
		service.PID = processInfo.ProcessId
	}
}

// StartService 启动Windows服务
func (w *WindowsManager) StartService(ctx context.Context, name string) error {
	cmd := fmt.Sprintf(`Start-Service -Name "%s"`, name)
	_, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return fmt.Errorf("启动Windows服务失败: %w", err)
	}
	return nil
}

// StopService 停止Windows服务
func (w *WindowsManager) StopService(ctx context.Context, name string) error {
	cmd := fmt.Sprintf(`Stop-Service -Name "%s" -Force`, name)
	_, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return fmt.Errorf("停止Windows服务失败: %w", err)
	}
	return nil
}

// RestartService 重启Windows服务
func (w *WindowsManager) RestartService(ctx context.Context, name string) error {
	cmd := fmt.Sprintf(`Restart-Service -Name "%s" -Force`, name)
	_, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return fmt.Errorf("重启Windows服务失败: %w", err)
	}
	return nil
}

// EnableService 启用Windows服务
func (w *WindowsManager) EnableService(ctx context.Context, name string) error {
	cmd := fmt.Sprintf(`Set-Service -Name "%s" -StartupType Automatic`, name)
	_, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return fmt.Errorf("启用Windows服务失败: %w", err)
	}
	return nil
}

// DisableService 禁用Windows服务
func (w *WindowsManager) DisableService(ctx context.Context, name string) error {
	cmd := fmt.Sprintf(`Set-Service -Name "%s" -StartupType Disabled`, name)
	_, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return fmt.Errorf("禁用Windows服务失败: %w", err)
	}
	return nil
}

// ServiceExists 检查Windows服务是否存在
func (w *WindowsManager) ServiceExists(ctx context.Context, name string) bool {
	cmd := fmt.Sprintf(`Get-Service -Name "%s" -ErrorAction SilentlyContinue`, name)
	output, err := executeCommand(ctx, "powershell", "-Command", cmd)
	return err == nil && strings.TrimSpace(output) != ""
}

// GetServiceLogs 获取Windows服务日志
func (w *WindowsManager) GetServiceLogs(ctx context.Context, name string, lines int) ([]string, error) {
	// Windows服务日志通常在事件日志中
	cmd := fmt.Sprintf(`Get-EventLog -LogName System -Source "%s" -Newest %d | Select-Object TimeGenerated,EntryType,Message | ConvertTo-Json`, name, lines)
	output, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return nil, fmt.Errorf("获取Windows服务日志失败: %w", err)
	}

	// 如果只有一条日志，PowerShell返回对象而不是数组
	if !strings.HasPrefix(strings.TrimSpace(output), "[") {
		output = "[" + output + "]"
	}

	var events []struct {
		TimeGenerated string `json:"TimeGenerated"`
		EntryType     string `json:"EntryType"`
		Message       string `json:"Message"`
	}

	if err := json.Unmarshal([]byte(output), &events); err != nil {
		return nil, fmt.Errorf("解析Windows服务日志失败: %w", err)
	}

	var logs []string
	for _, event := range events {
		logEntry := fmt.Sprintf("[%s] %s: %s", event.TimeGenerated, event.EntryType, event.Message)
		logs = append(logs, logEntry)
	}

	return logs, nil
}

// parseWindowsStatus 解析Windows服务状态
func parseWindowsStatus(status string) ServiceStatus {
	status = strings.ToLower(strings.TrimSpace(status))

	switch status {
	case "running":
		return StatusRunning
	case "stopped":
		return StatusStopped
	case "paused":
		return StatusStopped
	case "start pending", "stop pending", "continue pending", "pause pending":
		return StatusUnknown
	default:
		return StatusUnknown
	}
}

// GetServiceConfig 获取Windows服务配置
func (w *WindowsManager) GetServiceConfig(ctx context.Context, name string) (map[string]interface{}, error) {
	cmd := fmt.Sprintf(`Get-WmiObject -Class Win32_Service -Filter "Name='%s'" | Select-Object * | ConvertTo-Json`, name)
	output, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return nil, fmt.Errorf("获取Windows服务配置失败: %w", err)
	}

	var config map[string]interface{}
	if err := json.Unmarshal([]byte(output), &config); err != nil {
		return nil, fmt.Errorf("解析Windows服务配置失败: %w", err)
	}

	return config, nil
}

// SetServiceStartupType 设置Windows服务启动类型
func (w *WindowsManager) SetServiceStartupType(ctx context.Context, name, startupType string) error {
	// startupType: Automatic, Manual, Disabled
	cmd := fmt.Sprintf(`Set-Service -Name "%s" -StartupType %s`, name, startupType)
	_, err := executeCommand(ctx, "powershell", "-Command", cmd)
	if err != nil {
		return fmt.Errorf("设置Windows服务启动类型失败: %w", err)
	}
	return nil
}
