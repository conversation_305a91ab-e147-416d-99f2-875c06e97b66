package protocol

import (
	"encoding/json"
	"server-monitor/internal/crypto"
	"testing"
	"time"
)

func TestNewApplicationMessage(t *testing.T) {
	data := &AuthData{
		Username:  "test_user",
		Password:  "test_pass",
		ClientID:  "client_001",
		Version:   "1.0.0",
		Timestamp: time.Now().Unix(),
	}

	msg := NewApplicationMessage(MessageTypeAuth, "client", "server", data)

	if msg.ID == "" {
		t.Error("Message ID should not be empty")
	}

	if msg.Type != MessageTypeAuth {
		t.<PERSON>rf("Expected message type %s, got %s", MessageTypeAuth, msg.Type)
	}

	if msg.From != "client" {
		t.<PERSON>rf("Expected from 'client', got '%s'", msg.From)
	}

	if msg.To != "server" {
		t.Errorf("Expected to 'server', got '%s'", msg.To)
	}

	if msg.Timestamp <= 0 {
		t.<PERSON>rror("Message timestamp should be positive")
	}

	if msg.Data == nil {
		t.Error("Message data should not be nil")
	}
}

func TestApplicationMessageToJSON(t *testing.T) {
	data := &HeartbeatData{
		ClientID:  "client_001",
		Timestamp: time.Now().Unix(),
		Status:    "active",
	}

	msg := NewApplicationMessage(MessageTypeHeartbeat, "client", "server", data)

	jsonData, err := msg.ToJSON()
	if err != nil {
		t.Fatalf("Failed to convert message to JSON: %v", err)
	}

	if len(jsonData) == 0 {
		t.Error("JSON data should not be empty")
	}

	// 验证是否是有效的 JSON (Verify if it's valid JSON)
	var temp map[string]interface{}
	if err := json.Unmarshal(jsonData, &temp); err != nil {
		t.Errorf("Generated JSON is invalid: %v", err)
	}
}

func TestApplicationMessageFromJSON(t *testing.T) {
	originalData := &TestRequestData{
		TestID:       "test_001",
		ServerName:   "server-01",
		ServerIP:     "*************",
		ServerPort:   5201,
		Duration:     30,
		Parallel:     4,
		Protocol:     "tcp",
		Reverse:      false,
		Bidir:        false,
		WindowSize:   "64K",
		BufferLength: "128K",
		Bandwidth:    "0",
	}

	originalMsg := NewApplicationMessage(MessageTypeTestRequest, "client", "server", originalData)

	// Convert to JSON
	jsonData, err := originalMsg.ToJSON()
	if err != nil {
		t.Fatalf("Failed to convert message to JSON: %v", err)
	}

	// Convert back from JSON
	parsedMsg, err := FromJSON(jsonData)
	if err != nil {
		t.Fatalf("Failed to parse message from JSON: %v", err)
	}

	// 验证基本字段 (Verify basic fields)
	if parsedMsg.ID != originalMsg.ID {
		t.Errorf("Expected ID %s, got %s", originalMsg.ID, parsedMsg.ID)
	}

	if parsedMsg.Type != originalMsg.Type {
		t.Errorf("Expected type %s, got %s", originalMsg.Type, parsedMsg.Type)
	}

	if parsedMsg.From != originalMsg.From {
		t.Errorf("Expected from %s, got %s", originalMsg.From, parsedMsg.From)
	}

	if parsedMsg.To != originalMsg.To {
		t.Errorf("Expected to %s, got %s", originalMsg.To, parsedMsg.To)
	}
}

func TestApplicationMessageValidate(t *testing.T) {
	tests := []struct {
		name    string
		msg     *ApplicationMessage
		wantErr bool
	}{
		{
			name: "valid message",
			msg: &ApplicationMessage{
				ID:        "test_id",
				Type:      MessageTypeAuth,
				Timestamp: time.Now().Unix(),
				From:      "client",
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: false,
		},
		{
			name: "missing ID",
			msg: &ApplicationMessage{
				Type:      MessageTypeAuth,
				Timestamp: time.Now().Unix(),
				From:      "client",
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: true,
		},
		{
			name: "missing type",
			msg: &ApplicationMessage{
				ID:        "test_id",
				Timestamp: time.Now().Unix(),
				From:      "client",
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: true,
		},
		{
			name: "missing from",
			msg: &ApplicationMessage{
				ID:        "test_id",
				Type:      MessageTypeAuth,
				Timestamp: time.Now().Unix(),
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: true,
		},
		{
			name: "invalid timestamp",
			msg: &ApplicationMessage{
				ID:        "test_id",
				Type:      MessageTypeAuth,
				Timestamp: 0,
				From:      "client",
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.msg.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestApplicationMessageIsExpired(t *testing.T) {
	// 创建一个带有当前时间戳的消息 (Create a message with the current timestamp)
	msg := &ApplicationMessage{
		Timestamp: time.Now().Unix(),
	}

	// 在 1 小时 TTL 内不应过期 (Should not expire within 1 hour TTL)
	if msg.IsExpired(time.Hour) {
		t.Error("Message should not be expired")
	}

	// 创建一个旧消息 (Create an old message)
	oldMsg := &ApplicationMessage{
		Timestamp: time.Now().Add(-2 * time.Hour).Unix(),
	}

	// 在 1 小时 TTL 后应过期 (Should expire after 1 hour TTL)
	if !oldMsg.IsExpired(time.Hour) {
		t.Error("Old message should be expired")
	}
}

func TestApplicationMessageGetDataAs(t *testing.T) {
	originalData := &AuthData{
		Username:  "test_user",
		Password:  "test_pass",
		ClientID:  "client_001",
		Version:   "1.0.0",
		Timestamp: time.Now().Unix(),
	}

	msg := NewApplicationMessage(MessageTypeAuth, "client", "server", originalData)

	// 将数据提取为 AuthData (Extract data as AuthData)
	var extractedData AuthData
	if err := msg.GetDataAs(&extractedData); err != nil {
		t.Fatalf("Failed to extract data: %v", err)
	}

	// 验证提取的数据 (Verify extracted data)
	if extractedData.Username != originalData.Username {
		t.Errorf("Expected username %s, got %s", originalData.Username, extractedData.Username)
	}

	if extractedData.ClientID != originalData.ClientID {
		t.Errorf("Expected client ID %s, got %s", originalData.ClientID, extractedData.ClientID)
	}
}

func TestCreateAuthMessage(t *testing.T) {
	msg := CreateAuthMessage("client", "server", "user", "pass", "client_001", "1.0.0")

	if msg.Type != MessageTypeAuth {
		t.Errorf("Expected message type %s, got %s", MessageTypeAuth, msg.Type)
	}

	var authData AuthData
	if err := msg.GetDataAs(&authData); err != nil {
		t.Fatalf("Failed to extract auth data: %v", err)
	}

	if authData.Username != "user" {
		t.Errorf("Expected username 'user', got '%s'", authData.Username)
	}

	if authData.Password != "pass" {
		t.Errorf("Expected password 'pass', got '%s'", authData.Password)
	}

	if authData.ClientID != "client_001" {
		t.Errorf("Expected client ID 'client_001', got '%s'", authData.ClientID)
	}
}

func TestCreateAuthResponse(t *testing.T) {
	expiresAt := time.Now().Add(time.Hour).Unix()
	msg := CreateAuthResponse("server", "client", true, "success", "token123", expiresAt)

	if msg.Type != MessageTypeAuthResponse {
		t.Errorf("Expected message type %s, got %s", MessageTypeAuthResponse, msg.Type)
	}

	var authResp AuthResponse
	if err := msg.GetDataAs(&authResp); err != nil {
		t.Fatalf("Failed to extract auth response: %v", err)
	}

	if !authResp.Success {
		t.Error("Expected success to be true")
	}

	if authResp.Message != "success" {
		t.Errorf("Expected message 'success', got '%s'", authResp.Message)
	}

	if authResp.Token != "token123" {
		t.Errorf("Expected token 'token123', got '%s'", authResp.Token)
	}

	if authResp.ExpiresAt != expiresAt {
		t.Errorf("Expected expires at %d, got %d", expiresAt, authResp.ExpiresAt)
	}
}

func TestCreateHeartbeat(t *testing.T) {
	msg := CreateHeartbeat("client", "server", "client_001", "active")

	if msg.Type != MessageTypeHeartbeat {
		t.Errorf("Expected message type %s, got %s", MessageTypeHeartbeat, msg.Type)
	}

	var heartbeat HeartbeatData
	if err := msg.GetDataAs(&heartbeat); err != nil {
		t.Fatalf("Failed to extract heartbeat data: %v", err)
	}

	if heartbeat.ClientID != "client_001" {
		t.Errorf("Expected client ID 'client_001', got '%s'", heartbeat.ClientID)
	}

	if heartbeat.Status != "active" {
		t.Errorf("Expected status 'active', got '%s'", heartbeat.Status)
	}
}

func TestCreateErrorMessage(t *testing.T) {
	msg := CreateErrorMessage("server", "client", 500, "Internal Error", "Database connection failed")

	if msg.Type != MessageTypeError {
		t.Errorf("Expected message type %s, got %s", MessageTypeError, msg.Type)
	}

	var errorData ErrorData
	if err := msg.GetDataAs(&errorData); err != nil {
		t.Fatalf("Failed to extract error data: %v", err)
	}

	if errorData.Code != 500 {
		t.Errorf("Expected error code 500, got %d", errorData.Code)
	}

	if errorData.Message != "Internal Error" {
		t.Errorf("Expected error message 'Internal Error', got '%s'", errorData.Message)
	}

	if errorData.Details != "Database connection failed" {
		t.Errorf("Expected error details 'Database connection failed', got '%s'", errorData.Details)
	}
}

func TestApplicationMessageValidateWithReplayProtection(t *testing.T) {
	// 初始化全局重放攻击防护器，使用更宽松的时间窗口 (Initialize global replay protector with more lenient time window)
	config := &crypto.ReplayProtectorConfig{
		TimeWindow:         10 * time.Minute, // 更长的时间窗口 (Longer time window)
		CacheSize:          1000,
		ClockSkewTolerance: time.Minute,
		EnableCleanup:      false,
	}
	err := crypto.InitGlobalReplayProtector(config)
	if err != nil {
		t.Fatalf("Failed to initialize global replay protector: %v", err)
	}

	// 测试有效消息 (Test valid message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	// 更新消息时间戳为当前时间 (Update message timestamp to current time)
	msg.Timestamp = time.Now().UnixNano()

	// 第一次验证应该成功 (First validation should succeed)
	err = msg.ValidateWithReplayProtection()
	if err != nil {
		t.Errorf("First validation should succeed: %v", err)
	}

	// 第二次验证应该检测到重放攻击 (Second validation should detect replay attack)
	err = msg.ValidateWithReplayProtection()
	if err == nil {
		t.Error("Second validation should detect replay attack")
	}

	// 测试无效消息 (Test invalid message)
	invalidMsg := &ApplicationMessage{
		ID:        "",
		Type:      MessageTypeAuth,
		From:      "client1",
		To:        "server1",
		Timestamp: 0,
	}

	err = invalidMsg.ValidateWithReplayProtection()
	if err == nil {
		t.Error("Invalid message should fail validation")
	}
}

func TestApplicationMessageSequenceNumber(t *testing.T) {
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	// 初始状态应该没有序列号 (Initially should have no sequence number)
	_, hasSeqNum := msg.GetSequenceNumber()
	if hasSeqNum {
		t.Error("New message should not have sequence number initially")
	}

	// 设置序列号 (Set sequence number)
	msg.SetSequenceNumber(123)
	seqNum, hasSeqNum := msg.GetSequenceNumber()
	if !hasSeqNum {
		t.Error("Message should have sequence number after setting")
	}
	if seqNum != 123 {
		t.Errorf("Expected sequence number 123, got %d", seqNum)
	}

	// 生成并设置序列号 (Generate and set sequence number)
	msg.GenerateAndSetSequenceNumber()
	newSeqNum, _ := msg.GetSequenceNumber()
	if newSeqNum == 0 {
		t.Error("Generated sequence number should not be zero")
	}

	// 再次生成序列号应该递增 (Generating sequence number again should increment)
	msg.GenerateAndSetSequenceNumber()
	newerSeqNum, _ := msg.GetSequenceNumber()
	if newerSeqNum <= newSeqNum {
		t.Errorf("Newer sequence number %d should be greater than previous %d", newerSeqNum, newSeqNum)
	}
}

func TestApplicationMessageChecksum(t *testing.T) {
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	// 设置序列号以便计算校验和 (Set sequence number for checksum calculation)
	msg.SetSequenceNumber(123)

	// 初始状态应该没有校验和 (Initially should have no checksum)
	_, hasChecksum := msg.GetChecksum()
	if hasChecksum {
		t.Error("New message should not have checksum initially")
	}

	// 计算校验和 (Calculate checksum)
	checksum1 := msg.CalculateChecksum()
	if checksum1 == "" {
		t.Error("Calculated checksum should not be empty")
	}

	// 相同消息应该产生相同的校验和 (Same message should produce same checksum)
	checksum2 := msg.CalculateChecksum()
	if checksum1 != checksum2 {
		t.Errorf("Checksums should be identical: %s vs %s", checksum1, checksum2)
	}

	// 设置校验和 (Set checksum)
	msg.SetChecksum(checksum1)
	storedChecksum, hasChecksum := msg.GetChecksum()
	if !hasChecksum {
		t.Error("Message should have checksum after setting")
	}
	if storedChecksum != checksum1 {
		t.Errorf("Expected checksum %s, got %s", checksum1, storedChecksum)
	}

	// 验证校验和 (Validate checksum)
	err := msg.ValidateChecksum()
	if err != nil {
		t.Errorf("Checksum validation should pass: %v", err)
	}

	// 修改消息后校验和应该失效 (Checksum should fail after modifying message)
	msg.From = "different-sender"
	err = msg.ValidateChecksum()
	if err == nil {
		t.Error("Checksum validation should fail after modifying message")
	}
}

func TestApplicationMessageGenerateAndSetChecksum(t *testing.T) {
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	// 设置序列号 (Set sequence number)
	msg.SetSequenceNumber(456)

	// 生成并设置校验和 (Generate and set checksum)
	msg.GenerateAndSetChecksum()

	// 验证校验和已设置 (Verify checksum is set)
	checksum, hasChecksum := msg.GetChecksum()
	if !hasChecksum {
		t.Error("Message should have checksum after generating")
	}
	if checksum == "" {
		t.Error("Generated checksum should not be empty")
	}

	// 验证校验和正确性 (Verify checksum correctness)
	err := msg.ValidateChecksum()
	if err != nil {
		t.Errorf("Generated checksum should be valid: %v", err)
	}
}

func TestApplicationMessageValidateWithIntegrity(t *testing.T) {
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	// 设置序列号和校验和 (Set sequence number and checksum)
	msg.SetSequenceNumber(789)
	msg.GenerateAndSetChecksum()

	// 完整性验证应该通过 (Integrity validation should pass)
	err := msg.ValidateWithIntegrity()
	if err != nil {
		t.Errorf("Integrity validation should pass: %v", err)
	}

	// 修改消息后完整性验证应该失败 (Integrity validation should fail after modifying message)
	originalTo := msg.To
	msg.To = "different-receiver"
	err = msg.ValidateWithIntegrity()
	if err == nil {
		t.Error("Integrity validation should fail after modifying message")
	}

	// 恢复消息并重新生成校验和 (Restore message and regenerate checksum)
	msg.To = originalTo
	msg.GenerateAndSetChecksum()
	err = msg.ValidateWithIntegrity()
	if err != nil {
		t.Errorf("Integrity validation should pass after fixing checksum: %v", err)
	}
}

func BenchmarkCalculateChecksum(b *testing.B) {
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg.SetSequenceNumber(123)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = msg.CalculateChecksum()
	}
}

func BenchmarkValidateChecksum(b *testing.B) {
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	msg.SetSequenceNumber(123)
	msg.GenerateAndSetChecksum()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = msg.ValidateChecksum()
	}
}

func BenchmarkGenerateSequenceNumber(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_ = generateSequenceNumber()
	}
}

func TestCreateAckMessage(t *testing.T) {
	messageID := "test-message-123"
	status := "success"

	ackMsg := CreateAckMessage("server1", "client1", messageID, status)

	if ackMsg.Type != MessageTypeAck {
		t.Errorf("Expected message type %s, got %s", MessageTypeAck, ackMsg.Type)
	}

	if ackMsg.From != "server1" {
		t.Errorf("Expected from 'server1', got '%s'", ackMsg.From)
	}

	if ackMsg.To != "client1" {
		t.Errorf("Expected to 'client1', got '%s'", ackMsg.To)
	}

	ackData, ok := ackMsg.Data.(*AckData)
	if !ok {
		t.Error("Expected AckData type")
	}

	if ackData.MessageID != messageID {
		t.Errorf("Expected message ID '%s', got '%s'", messageID, ackData.MessageID)
	}

	if ackData.Status != status {
		t.Errorf("Expected status '%s', got '%s'", status, ackData.Status)
	}

	if ackData.ReceivedAt == 0 {
		t.Error("ReceivedAt should not be zero")
	}

	if ackData.ProcessedAt == 0 {
		t.Error("ProcessedAt should not be zero")
	}
}

func TestCreateNackMessage(t *testing.T) {
	messageID := "test-message-456"
	errorMessage := "processing failed"

	nackMsg := CreateNackMessage("server1", "client1", messageID, errorMessage)

	if nackMsg.Type != MessageTypeNack {
		t.Errorf("Expected message type %s, got %s", MessageTypeNack, nackMsg.Type)
	}

	if nackMsg.From != "server1" {
		t.Errorf("Expected from 'server1', got '%s'", nackMsg.From)
	}

	if nackMsg.To != "client1" {
		t.Errorf("Expected to 'client1', got '%s'", nackMsg.To)
	}

	ackData, ok := nackMsg.Data.(*AckData)
	if !ok {
		t.Error("Expected AckData type")
	}

	if ackData.MessageID != messageID {
		t.Errorf("Expected message ID '%s', got '%s'", messageID, ackData.MessageID)
	}

	if ackData.Status != "failed" {
		t.Errorf("Expected status 'failed', got '%s'", ackData.Status)
	}

	if ackData.ErrorMessage != errorMessage {
		t.Errorf("Expected error message '%s', got '%s'", errorMessage, ackData.ErrorMessage)
	}
}
