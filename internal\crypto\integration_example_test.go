package crypto

import (
	"fmt"
	"testing"
	"time"
)

// TestKeyRotationIntegrationExample 密钥轮换集成示例 (Key rotation integration example)
func TestKeyRotationIntegrationExample(t *testing.T) {
	// 创建密钥管理器配置 (Create key manager configuration)
	config := &KeyManagerConfig{
		RotationInterval: 100 * time.Millisecond, // 短间隔用于演示 (Short interval for demonstration)
		KeyTTL:           time.Hour,
		MaxKeyHistory:    10, // 增加历史密钥数量以容纳所有消息 (Increase history to accommodate all messages)
		AutoRotate:       true,
		MasterPassword:   []byte("demo-master-password-123"),
	}

	// 创建密钥管理器 (Create key manager)
	km, err := NewKeyManager(config)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	defer km.Stop()

	// 创建加密服务 (Create encryption service)
	es := NewEncryptionService(km)

	// 模拟应用程序数据 (Simulate application data)
	messages := []string{
		"User login: <EMAIL>",
		"Transaction: $100.00 transfer",
		"System alert: High CPU usage",
		"User logout: <EMAIL>",
	}

	// 存储加密的消息 (Store encrypted messages)
	encryptedMessages := make([]*EncryptedData, len(messages))

	fmt.Printf("=== 密钥轮换演示 (Key Rotation Demonstration) ===\n")

	// 加密消息并观察密钥轮换 (Encrypt messages and observe key rotation)
	for i, msg := range messages {
		// 获取当前密钥版本 (Get current key version)
		currentVersion := km.GetKeyVersion()

		// 加密消息 (Encrypt message)
		encData, err := es.EncryptData([]byte(msg))
		if err != nil {
			t.Fatalf("Failed to encrypt message %d: %v", i, err)
		}

		encryptedMessages[i] = encData

		fmt.Printf("Message %d: Encrypted with key version %d (current: %d)\n",
			i+1, encData.KeyVersion, currentVersion)

		// 等待可能的密钥轮换 (Wait for possible key rotation)
		time.Sleep(120 * time.Millisecond)

		// 显示密钥统计 (Show key statistics)
		stats := km.GetKeyStats()
		fmt.Printf("  Stats: Current key v%v, Historical keys: %v\n",
			stats["current_key_version"], stats["historical_keys_count"])
	}

	fmt.Printf("\n=== 解密验证 (Decryption Verification) ===\n")

	// 验证所有消息都能正确解密 (Verify all messages can be decrypted correctly)
	for i, encData := range encryptedMessages {
		decrypted, err := es.DecryptData(encData)
		if err != nil {
			t.Fatalf("Failed to decrypt message %d: %v", i, err)
		}

		if string(decrypted) != messages[i] {
			t.Errorf("Message %d decryption mismatch. Expected: %s, Got: %s",
				i, messages[i], string(decrypted))
		}

		fmt.Printf("Message %d: Successfully decrypted with key version %d\n",
			i+1, encData.KeyVersion)
	}

	// 最终统计 (Final statistics)
	finalStats := km.GetKeyStats()
	fmt.Printf("\n=== 最终统计 (Final Statistics) ===\n")
	fmt.Printf("Current key version: %v\n", finalStats["current_key_version"])
	fmt.Printf("Historical keys count: %v\n", finalStats["historical_keys_count"])
	fmt.Printf("Valid historical keys: %v\n", finalStats["valid_historical_keys"])
	fmt.Printf("Auto rotation enabled: %v\n", finalStats["auto_rotate_enabled"])

	// 验证前向安全性 (Verify forward secrecy)
	fmt.Printf("\n=== 前向安全性验证 (Forward Secrecy Verification) ===\n")

	// 手动轮换密钥 (Manually rotate key)
	oldVersion := km.GetKeyVersion()
	err = km.RotateKey()
	if err != nil {
		t.Fatalf("Failed to manually rotate key: %v", err)
	}
	newVersion := km.GetKeyVersion()

	fmt.Printf("Manual key rotation: v%d -> v%d\n", oldVersion, newVersion)

	// 新密钥加密的数据，旧密钥无法解密（这是期望的行为）
	// (Data encrypted with new key cannot be decrypted with old key - this is expected)
	newMessage := "New message after rotation"
	newEncData, err := es.EncryptData([]byte(newMessage))
	if err != nil {
		t.Fatalf("Failed to encrypt new message: %v", err)
	}

	fmt.Printf("New message encrypted with key version %d\n", newEncData.KeyVersion)

	// 验证新消息可以解密 (Verify new message can be decrypted)
	decryptedNew, err := es.DecryptData(newEncData)
	if err != nil {
		t.Fatalf("Failed to decrypt new message: %v", err)
	}

	if string(decryptedNew) != newMessage {
		t.Errorf("New message decryption failed. Expected: %s, Got: %s",
			newMessage, string(decryptedNew))
	}

	fmt.Printf("New message successfully decrypted\n")

	// 验证历史消息仍然可以解密 (Verify historical messages can still be decrypted)
	fmt.Printf("\n=== 历史消息解密验证 (Historical Message Decryption Verification) ===\n")
	for i, encData := range encryptedMessages {
		decrypted, err := es.DecryptData(encData)
		if err != nil {
			t.Fatalf("Failed to decrypt historical message %d: %v", i, err)
		}

		if string(decrypted) != messages[i] {
			t.Errorf("Historical message %d decryption mismatch", i)
		}

		fmt.Printf("Historical message %d: Still accessible with key version %d\n",
			i+1, encData.KeyVersion)
	}

	fmt.Printf("\n✅ 密钥轮换集成测试成功完成 (Key rotation integration test completed successfully)\n")
}

// TestKeyManagerConfigIntegration 测试配置集成 (Test configuration integration)
func TestKeyManagerConfigIntegration(t *testing.T) {
	// 测试不同的配置组合 (Test different configuration combinations)
	testCases := []struct {
		name   string
		config *KeyManagerConfig
	}{
		{
			name: "High Security Config",
			config: &KeyManagerConfig{
				RotationInterval: 1 * time.Hour,
				KeyTTL:           6 * time.Hour,
				MaxKeyHistory:    20,
				AutoRotate:       true,
				MasterPassword:   []byte("high-security-password-with-entropy"),
			},
		},
		{
			name: "Balanced Config",
			config: &KeyManagerConfig{
				RotationInterval: 24 * time.Hour,
				KeyTTL:           72 * time.Hour,
				MaxKeyHistory:    10,
				AutoRotate:       true,
				MasterPassword:   []byte("balanced-security-password"),
			},
		},
		{
			name: "Manual Rotation Config",
			config: &KeyManagerConfig{
				RotationInterval: 7 * 24 * time.Hour,  // 7 days
				KeyTTL:           30 * 24 * time.Hour, // 30 days
				MaxKeyHistory:    5,
				AutoRotate:       false, // Manual rotation only
				MasterPassword:   []byte("manual-rotation-password"),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			km, err := NewKeyManager(tc.config)
			if err != nil {
				t.Fatalf("Failed to create key manager with %s: %v", tc.name, err)
			}
			defer km.Stop()

			// 验证配置正确应用 (Verify configuration is correctly applied)
			stats := km.GetKeyStats()

			if stats["auto_rotate_enabled"] != tc.config.AutoRotate {
				t.Errorf("Auto rotate setting mismatch for %s", tc.name)
			}

			// 测试基本功能 (Test basic functionality)
			testData := []byte("Test data for " + tc.name)
			es := NewEncryptionService(km)

			encData, err := es.EncryptData(testData)
			if err != nil {
				t.Fatalf("Failed to encrypt with %s: %v", tc.name, err)
			}

			decrypted, err := es.DecryptData(encData)
			if err != nil {
				t.Fatalf("Failed to decrypt with %s: %v", tc.name, err)
			}

			if string(decrypted) != string(testData) {
				t.Errorf("Data mismatch for %s", tc.name)
			}

			fmt.Printf("✅ %s: Configuration and functionality verified\n", tc.name)
		})
	}
}
