package reporter

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"sync"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/logger"
	"server-monitor/internal/pool"
)

// HTTPClient HTTP客户端
type HTTPClient struct {
	config     *config.Config
	logger     *logger.Logger
	httpClient *http.Client
	connPool   *pool.Pool
	mu         sync.RWMutex

	// 统计信息
	stats *ClientStats
}

// ClientStats 客户端统计信息
type ClientStats struct {
	TotalRequests     int64         `json:"total_requests"`
	SuccessRequests   int64         `json:"success_requests"`
	FailedRequests    int64         `json:"failed_requests"`
	TotalBytes        int64         `json:"total_bytes"`
	AverageLatency    time.Duration `json:"average_latency"`
	LastRequestTime   time.Time     `json:"last_request_time"`
	ConnectionsUsed   int64         `json:"connections_used"`
	ConnectionsReused int64         `json:"connections_reused"`
}

// RequestOptions 请求选项
type RequestOptions struct {
	Method     string            `json:"method"`
	URL        string            `json:"url"`
	Headers    map[string]string `json:"headers"`
	Body       interface{}       `json:"body"`
	Timeout    time.Duration     `json:"timeout"`
	Retries    int               `json:"retries"`
	RetryDelay time.Duration     `json:"retry_delay"`
}

// Response HTTP响应
type Response struct {
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers"`
	Body       []byte            `json:"body"`
	Latency    time.Duration     `json:"latency"`
	Size       int64             `json:"size"`
}

// NewHTTPClient 创建HTTP客户端
func NewHTTPClient(cfg *config.Config, logger *logger.Logger) *HTTPClient {
	// 创建连接池
	connPool := pool.NewPool(
		func() (pool.Connection, error) {
			// 使用API配置作为服务器配置
			host := "127.0.0.1"
			port := 8443
			if len(cfg.Servers) > 0 {
				host = cfg.Servers[0].IP
				port = cfg.Servers[0].Port
			}
			conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), 10*time.Second)
			if err != nil {
				return nil, err
			}
			return pool.NewTCPConnection(conn), nil
		},
		&pool.Config{
			MaxIdle:     10,
			MaxActive:   50,
			IdleTimeout: 5 * time.Minute,
			MaxLifetime: 30 * time.Minute,
			TestOnGet:   true,
		},
	)

	// 创建HTTP传输
	transport := &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			// 使用连接池
			poolConn, err := connPool.Get(ctx)
			if err != nil {
				// 如果连接池失败，回退到直接连接
				return net.DialTimeout(network, addr, 10*time.Second)
			}

			if tcpConn, ok := poolConn.(*pool.TCPConnection); ok {
				return tcpConn.GetConn(), nil
			}

			return net.DialTimeout(network, addr, 10*time.Second)
		},
		MaxIdleConns:          100,
		MaxIdleConnsPerHost:   10,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		DisableKeepAlives:     false,
		DisableCompression:    false,
	}

	// TLS配置（使用默认配置）
	transport.TLSClientConfig = &tls.Config{
		InsecureSkipVerify: true, // 测试环境跳过验证
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Transport: transport,
		Timeout:   30 * time.Second,
	}

	return &HTTPClient{
		config:     cfg,
		logger:     logger,
		httpClient: httpClient,
		connPool:   connPool,
		stats:      &ClientStats{},
	}
}

// Get 发送GET请求
func (c *HTTPClient) Get(ctx context.Context, url string, headers map[string]string) (*Response, error) {
	options := &RequestOptions{
		Method:     "GET",
		URL:        url,
		Headers:    headers,
		Timeout:    30 * time.Second,
		Retries:    3,
		RetryDelay: 1 * time.Second,
	}

	return c.Request(ctx, options)
}

// Post 发送POST请求
func (c *HTTPClient) Post(ctx context.Context, url string, body interface{}, headers map[string]string) (*Response, error) {
	options := &RequestOptions{
		Method:     "POST",
		URL:        url,
		Headers:    headers,
		Body:       body,
		Timeout:    30 * time.Second,
		Retries:    3,
		RetryDelay: 1 * time.Second,
	}

	return c.Request(ctx, options)
}

// Put 发送PUT请求
func (c *HTTPClient) Put(ctx context.Context, url string, body interface{}, headers map[string]string) (*Response, error) {
	options := &RequestOptions{
		Method:     "PUT",
		URL:        url,
		Headers:    headers,
		Body:       body,
		Timeout:    30 * time.Second,
		Retries:    3,
		RetryDelay: 1 * time.Second,
	}

	return c.Request(ctx, options)
}

// Delete 发送DELETE请求
func (c *HTTPClient) Delete(ctx context.Context, url string, headers map[string]string) (*Response, error) {
	options := &RequestOptions{
		Method:     "DELETE",
		URL:        url,
		Headers:    headers,
		Timeout:    30 * time.Second,
		Retries:    3,
		RetryDelay: 1 * time.Second,
	}

	return c.Request(ctx, options)
}

// Request 发送HTTP请求
func (c *HTTPClient) Request(ctx context.Context, options *RequestOptions) (*Response, error) {
	var lastErr error

	for attempt := 0; attempt <= options.Retries; attempt++ {
		if attempt > 0 {
			// 等待重试延迟
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(options.RetryDelay):
			}

			c.logger.Debug("HTTP请求重试 %d/%d: %s %s", attempt, options.Retries, options.Method, options.URL)
		}

		response, err := c.doRequest(ctx, options)
		if err == nil {
			return response, nil
		}

		lastErr = err

		// 检查是否应该重试
		if !c.shouldRetry(err, response) {
			break
		}
	}

	return nil, fmt.Errorf("HTTP请求失败，已重试 %d 次: %w", options.Retries, lastErr)
}

// doRequest 执行单次HTTP请求
func (c *HTTPClient) doRequest(ctx context.Context, options *RequestOptions) (*Response, error) {
	startTime := time.Now()

	// 更新统计
	c.mu.Lock()
	c.stats.TotalRequests++
	c.stats.LastRequestTime = startTime
	c.mu.Unlock()

	// 准备请求体
	var bodyReader io.Reader
	var contentLength int64

	if options.Body != nil {
		bodyData, err := c.marshalBody(options.Body)
		if err != nil {
			c.updateFailedStats()
			return nil, fmt.Errorf("序列化请求体失败: %w", err)
		}
		bodyReader = bytes.NewReader(bodyData)
		contentLength = int64(len(bodyData))
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, options.Method, options.URL, bodyReader)
	if err != nil {
		c.updateFailedStats()
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	c.setHeaders(req, options.Headers)

	// 设置超时
	if options.Timeout > 0 {
		timeoutCtx, cancel := context.WithTimeout(ctx, options.Timeout)
		defer cancel()
		req = req.WithContext(timeoutCtx)
	}

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.updateFailedStats()
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.updateFailedStats()
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	latency := time.Since(startTime)
	responseSize := int64(len(respBody))

	// 构建响应
	response := &Response{
		StatusCode: resp.StatusCode,
		Headers:    make(map[string]string),
		Body:       respBody,
		Latency:    latency,
		Size:       responseSize,
	}

	// 复制响应头
	for key, values := range resp.Header {
		if len(values) > 0 {
			response.Headers[key] = values[0]
		}
	}

	// 更新统计
	c.updateSuccessStats(latency, contentLength+responseSize)

	// 检查HTTP状态码
	if resp.StatusCode >= 400 {
		c.updateFailedStats()
		return response, fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
	}

	return response, nil
}

// marshalBody 序列化请求体
func (c *HTTPClient) marshalBody(body interface{}) ([]byte, error) {
	switch v := body.(type) {
	case []byte:
		return v, nil
	case string:
		return []byte(v), nil
	default:
		return json.Marshal(v)
	}
}

// setHeaders 设置请求头
func (c *HTTPClient) setHeaders(req *http.Request, headers map[string]string) {
	// 设置默认头
	req.Header.Set("User-Agent", "server-monitor-client/1.0")
	req.Header.Set("Accept", "application/json")

	if req.Body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// 设置API密钥（如果有的话）
	// req.Header.Set("X-API-Key", "test-api-key")

	// 设置自定义头
	for key, value := range headers {
		req.Header.Set(key, value)
	}
}

// shouldRetry 判断是否应该重试
func (c *HTTPClient) shouldRetry(err error, response *Response) bool {
	// 网络错误通常可以重试
	if err != nil {
		return true
	}

	// 某些HTTP状态码可以重试
	if response != nil {
		switch response.StatusCode {
		case 429, 500, 502, 503, 504:
			return true
		}
	}

	return false
}

// updateSuccessStats 更新成功统计
func (c *HTTPClient) updateSuccessStats(latency time.Duration, bytes int64) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.stats.SuccessRequests++
	c.stats.TotalBytes += bytes

	// 更新平均延迟
	if c.stats.SuccessRequests == 1 {
		c.stats.AverageLatency = latency
	} else {
		c.stats.AverageLatency = time.Duration(
			(int64(c.stats.AverageLatency)*(c.stats.SuccessRequests-1) + int64(latency)) / c.stats.SuccessRequests,
		)
	}
}

// updateFailedStats 更新失败统计
func (c *HTTPClient) updateFailedStats() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.stats.FailedRequests++
}

// GetStats 获取统计信息
func (c *HTTPClient) GetStats() *ClientStats {
	c.mu.RLock()
	defer c.mu.RUnlock()

	stats := *c.stats
	return &stats
}

// Close 关闭客户端
func (c *HTTPClient) Close() error {
	if c.connPool != nil {
		return c.connPool.Close()
	}
	return nil
}

// HealthCheck 健康检查
func (c *HTTPClient) HealthCheck() map[string]interface{} {
	stats := c.GetStats()
	poolStats := c.connPool.Stats()

	successRate := float64(0)
	if stats.TotalRequests > 0 {
		successRate = float64(stats.SuccessRequests) / float64(stats.TotalRequests) * 100
	}

	return map[string]interface{}{
		"total_requests":    stats.TotalRequests,
		"success_requests":  stats.SuccessRequests,
		"failed_requests":   stats.FailedRequests,
		"success_rate":      fmt.Sprintf("%.2f%%", successRate),
		"total_bytes":       stats.TotalBytes,
		"average_latency":   stats.AverageLatency.String(),
		"last_request_time": stats.LastRequestTime,
		"connection_pool":   poolStats,
	}
}
