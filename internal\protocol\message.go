package protocol

import (
	"encoding/binary"
	"fmt"
)

const (
	NonceSize  = 12 // GCM recommended Nonce size
	TagSize    = 16 // GCM authentication tag size
	LengthSize = 4  // Size of the length prefix
)

// Message 表示加密数据消息的格式 (Message represents the format of an encrypted data message)
// [4字节长度][12字节随机数][加密数据][16字节认证标签] ([4-byte length][12-byte nonce][encrypted data][16-byte authentication tag])
type Message struct {
	Length     uint32 // Length of the encrypted data (ciphertext + tag)
	Nonce      []byte // Nonce used for AES-GCM
	Ciphertext []byte // Encrypted data (including GCM tag)
}

// Marshal 将 Message 序列化为字节切片 (<PERSON> serializes a Message into a byte slice)
func (m *Message) Marshal() ([]byte, error) {
	if len(m.Nonce) != NonceSize {
		return nil, fmt.Errorf("nonce size must be %d bytes, got %d", NonceSize, len(m.Nonce))
	}

	// 计算密文和标签的总长度 (Calculate the total length of ciphertext and tag)
	// GCM Seal 方法已将标签附加到密文 (The GCM Seal method has appended the tag to the ciphertext)
	m.Length = uint32(len(m.Ciphertext))

	buf := make([]byte, LengthSize+NonceSize+len(m.Ciphertext))

	// 写入长度 (Write length)
	binary.BigEndian.PutUint32(buf[0:LengthSize], m.Length)
	// 写入随机数 (Write nonce)
	copy(buf[LengthSize:LengthSize+NonceSize], m.Nonce)
	// 写入密文（包含标签） (Write ciphertext (including tag))
	copy(buf[LengthSize+NonceSize:], m.Ciphertext)

	return buf, nil
}

// Unmarshal 将字节切片反序列化为 Message (Unmarshal deserializes a byte slice into a Message)
func (m *Message) Unmarshal(data []byte) error {
	if len(data) < LengthSize+NonceSize {
		return fmt.Errorf("message data too short to unmarshal")
	}

	// 读取长度 (Read length)
	m.Length = binary.BigEndian.Uint32(data[0:LengthSize])

	// 读取随机数 (Read nonce)
	m.Nonce = data[LengthSize : LengthSize+NonceSize]

	// 读取密文（包含标签） (Read ciphertext (including tag))
	m.Ciphertext = data[LengthSize+NonceSize:]

	if uint32(len(m.Ciphertext)) != m.Length {
		return fmt.Errorf("ciphertext length mismatch: expected %d, got %d", m.Length, len(m.Ciphertext))
	}

	return nil
}
