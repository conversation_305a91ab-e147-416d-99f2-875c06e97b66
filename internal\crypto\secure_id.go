package crypto

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/binary"
	"fmt"
	"io"
	"sync"
	"time"
)

const (
	// RandomBytesLen 随机字节长度 (Random bytes length)
	RandomBytesLen = 16 // 128位随机数 (128-bit random number)
	
	// TimestampBytesLen 时间戳字节长度 (Timestamp bytes length)
	TimestampBytesLen = 8 // 64位时间戳 (64-bit timestamp)
	
	// TotalIDLen 总ID长度 (Total ID length)
	TotalIDLen = RandomBytesLen + TimestampBytesLen // 24字节 (24 bytes)
)

var (
	// idCounter 用于确保同一纳秒内生成的ID唯一性 (Counter to ensure uniqueness within the same nanosecond)
	idCounter uint32
	
	// counterMutex 保护计数器的互斥锁 (Mutex to protect the counter)
	counterMutex sync.Mutex
	
	// lastTimestamp 上次生成ID的时间戳 (Last timestamp when ID was generated)
	lastTimestamp int64
)

// SecureIDGenerator 安全ID生成器 (Secure ID generator)
type SecureIDGenerator struct {
	// 可以添加配置选项 (Can add configuration options)
}

// NewSecureIDGenerator 创建新的安全ID生成器 (Creates a new secure ID generator)
func NewSecureIDGenerator() *SecureIDGenerator {
	return &SecureIDGenerator{}
}

// GenerateSecureMessageID 生成安全的消息ID (Generates a secure message ID)
// 返回格式：base64编码的(16字节随机数 + 8字节时间戳 + 4字节计数器)
// Return format: base64 encoded (16-byte random + 8-byte timestamp + 4-byte counter)
func GenerateSecureMessageID() (string, error) {
	// 生成随机字节 (Generate random bytes)
	randomBytes := make([]byte, RandomBytesLen)
	if _, err := io.ReadFull(rand.Reader, randomBytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	
	// 获取当前时间戳 (Get current timestamp)
	now := time.Now().UnixNano()
	
	// 处理计数器以确保唯一性 (Handle counter to ensure uniqueness)
	counterMutex.Lock()
	if now == lastTimestamp {
		idCounter++
	} else {
		idCounter = 0
		lastTimestamp = now
	}
	currentCounter := idCounter
	counterMutex.Unlock()
	
	// 创建完整的ID字节数组 (Create complete ID byte array)
	idBytes := make([]byte, TotalIDLen+4) // +4 for counter
	
	// 复制随机字节 (Copy random bytes)
	copy(idBytes[0:RandomBytesLen], randomBytes)
	
	// 添加时间戳 (Add timestamp)
	binary.BigEndian.PutUint64(idBytes[RandomBytesLen:RandomBytesLen+TimestampBytesLen], uint64(now))
	
	// 添加计数器 (Add counter)
	binary.BigEndian.PutUint32(idBytes[RandomBytesLen+TimestampBytesLen:], currentCounter)
	
	// 使用URL安全的base64编码 (Use URL-safe base64 encoding)
	return base64.URLEncoding.EncodeToString(idBytes), nil
}

// GenerateSecureMessageIDMust 生成安全的消息ID，如果失败则panic (Generates secure message ID, panics on failure)
// 这个函数用于与现有代码兼容，现有代码不处理错误 (This function is for compatibility with existing code that doesn't handle errors)
func GenerateSecureMessageIDMust() string {
	id, err := GenerateSecureMessageID()
	if err != nil {
		// 在生产环境中，这种情况极少发生，但如果发生了，我们需要有一个fallback
		// In production, this rarely happens, but if it does, we need a fallback
		panic(fmt.Sprintf("failed to generate secure message ID: %v", err))
	}
	return id
}

// ParseSecureMessageID 解析安全消息ID (Parses a secure message ID)
// 返回随机字节、时间戳和计数器 (Returns random bytes, timestamp, and counter)
func ParseSecureMessageID(id string) (randomBytes []byte, timestamp int64, counter uint32, err error) {
	// 解码base64 (Decode base64)
	idBytes, err := base64.URLEncoding.DecodeString(id)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("failed to decode message ID: %w", err)
	}
	
	// 检查长度 (Check length)
	expectedLen := TotalIDLen + 4 // +4 for counter
	if len(idBytes) != expectedLen {
		return nil, 0, 0, fmt.Errorf("invalid message ID length: expected %d, got %d", expectedLen, len(idBytes))
	}
	
	// 提取随机字节 (Extract random bytes)
	randomBytes = make([]byte, RandomBytesLen)
	copy(randomBytes, idBytes[0:RandomBytesLen])
	
	// 提取时间戳 (Extract timestamp)
	timestamp = int64(binary.BigEndian.Uint64(idBytes[RandomBytesLen : RandomBytesLen+TimestampBytesLen]))
	
	// 提取计数器 (Extract counter)
	counter = binary.BigEndian.Uint32(idBytes[RandomBytesLen+TimestampBytesLen:])
	
	return randomBytes, timestamp, counter, nil
}

// ValidateSecureMessageID 验证消息ID的格式 (Validates the format of a message ID)
func ValidateSecureMessageID(id string) error {
	_, _, _, err := ParseSecureMessageID(id)
	return err
}

// GetIDTimestamp 从消息ID中提取时间戳 (Extracts timestamp from message ID)
func GetIDTimestamp(id string) (time.Time, error) {
	_, timestamp, _, err := ParseSecureMessageID(id)
	if err != nil {
		return time.Time{}, err
	}
	return time.Unix(0, timestamp), nil
}

// IsIDExpired 检查消息ID是否过期 (Checks if message ID is expired)
func IsIDExpired(id string, maxAge time.Duration) (bool, error) {
	timestamp, err := GetIDTimestamp(id)
	if err != nil {
		return false, err
	}
	return time.Since(timestamp) > maxAge, nil
}
