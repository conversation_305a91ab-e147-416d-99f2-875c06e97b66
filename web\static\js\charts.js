// charts.js - 统一的图表工具库
console.log('Charts script loaded.');

// 图表实例缓存
const chartInstances = new Map();

// 数据缓存和配置
const chartDataCache = new Map();
const MAX_DATA_POINTS = 20; // 最大数据点数量

// 默认图表配置
const defaultChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
        duration: 300 // 减少动画时间提高性能
    },
    scales: {
        x: {
            display: true,
            title: {
                display: true,
                text: '时间'
            }
        },
        y: {
            beginAtZero: true,
            title: {
                display: true,
                text: '使用率 (%)'
            }
        }
    },
    plugins: {
        legend: {
            display: true,
            position: 'top'
        },
        tooltip: {
            mode: 'index',
            intersect: false
        }
    },
    interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
    }
};

// 图表颜色配置
const chartColors = {
    cpu: 'rgb(75, 192, 192)',
    memory: 'rgb(255, 99, 132)',
    disk: 'rgb(54, 162, 235)',
    network: 'rgb(255, 205, 86)',
    networkRx: 'rgb(153, 102, 255)',
    networkTx: 'rgb(255, 159, 64)'
};

/**
 * 创建实时图表
 * @param {string} canvasId - Canvas元素ID
 * @param {string} type - 图表类型 (cpu, memory, disk, network)
 * @param {Object} options - 自定义选项
 * @returns {Chart} Chart.js实例
 */
function createRealtimeChart(canvasId, type, options = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
        console.error(`Canvas element with id '${canvasId}' not found`);
        return null;
    }

    // 如果图表已存在，先销毁
    if (chartInstances.has(canvasId)) {
        chartInstances.get(canvasId).destroy();
    }

    // 合并配置
    const chartOptions = {
        ...defaultChartOptions,
        ...options,
        scales: {
            ...defaultChartOptions.scales,
            ...(options.scales || {})
        }
    };

    // 根据类型设置特定配置
    if (type === 'network') {
        chartOptions.scales.y.title.text = '流量 (KB/s)';
    }

    // 初始化数据结构
    const initialData = {
        labels: [],
        datasets: [{
            label: getChartLabel(type),
            data: [],
            borderColor: chartColors[type] || chartColors.cpu,
            backgroundColor: (chartColors[type] || chartColors.cpu).replace('rgb', 'rgba').replace(')', ', 0.1)'),
            tension: 0.1,
            fill: false
        }]
    };

    // 网络图表需要两个数据集
    if (type === 'network') {
        initialData.datasets = [
            {
                label: '接收 (RX)',
                data: [],
                borderColor: chartColors.networkRx,
                backgroundColor: chartColors.networkRx.replace('rgb', 'rgba').replace(')', ', 0.1)'),
                tension: 0.1,
                fill: false
            },
            {
                label: '发送 (TX)',
                data: [],
                borderColor: chartColors.networkTx,
                backgroundColor: chartColors.networkTx.replace('rgb', 'rgba').replace(')', ', 0.1)'),
                tension: 0.1,
                fill: false
            }
        ];
    }

    // 创建图表
    const chart = new Chart(canvas, {
        type: 'line',
        data: initialData,
        options: chartOptions
    });

    // 缓存图表实例和数据
    chartInstances.set(canvasId, chart);
    chartDataCache.set(canvasId, {
        type: type,
        lastUpdate: Date.now(),
        dataPoints: []
    });

    return chart;
}

/**
 * 更新图表数据
 * @param {string} canvasId - Canvas元素ID
 * @param {Object} data - 新数据
 * @param {number} timestamp - 时间戳
 */
function updateChartData(canvasId, data, timestamp = Date.now()) {
    const chart = chartInstances.get(canvasId);
    const cache = chartDataCache.get(canvasId);

    if (!chart || !cache) {
        console.warn(`Chart '${canvasId}' not found`);
        return;
    }

    // 防抖处理 - 限制更新频率
    const now = Date.now();
    if (now - cache.lastUpdate < 1000) { // 最多每秒更新一次
        return;
    }
    cache.lastUpdate = now;

    // 格式化时间标签
    const timeLabel = new Date(timestamp).toLocaleTimeString();

    // 添加新数据点
    chart.data.labels.push(timeLabel);

    if (cache.type === 'network') {
        // 网络图表有两个数据集
        const rxValue = data.network_rx ? (data.network_rx / 1024).toFixed(2) : 0; // 转换为KB
        const txValue = data.network_tx ? (data.network_tx / 1024).toFixed(2) : 0;

        chart.data.datasets[0].data.push(parseFloat(rxValue));
        chart.data.datasets[1].data.push(parseFloat(txValue));
    } else {
        // 单一数据集
        const value = getDataValue(data, cache.type);
        chart.data.datasets[0].data.push(value);
    }

    // 限制数据点数量
    if (chart.data.labels.length > MAX_DATA_POINTS) {
        chart.data.labels.shift();
        chart.data.datasets.forEach(dataset => {
            dataset.data.shift();
        });
    }

    // 更新图表
    chart.update('none'); // 使用'none'模式提高性能
}

/**
 * 获取图表标签
 * @param {string} type - 图表类型
 * @returns {string} 标签文本
 */
function getChartLabel(type) {
    const labels = {
        cpu: 'CPU使用率 (%)',
        memory: '内存使用率 (%)',
        disk: '磁盘使用率 (%)',
        network: '网络流量 (KB/s)'
    };
    return labels[type] || '未知指标';
}

/**
 * 从数据对象中提取对应类型的值
 * @param {Object} data - 数据对象
 * @param {string} type - 数据类型
 * @returns {number} 数值
 */
function getDataValue(data, type) {
    const mapping = {
        cpu: 'cpu_usage',
        memory: 'memory_usage',
        disk: 'disk_usage'
    };

    const key = mapping[type];
    return key && data[key] ? parseFloat(data[key].toFixed(2)) : 0;
}

/**
 * 批量创建监控图表
 * @param {Object} chartConfigs - 图表配置对象 {canvasId: type}
 */
function createMonitoringCharts(chartConfigs) {
    Object.entries(chartConfigs).forEach(([canvasId, type]) => {
        createRealtimeChart(canvasId, type);
    });
}

/**
 * 处理WebSocket系统信息消息
 * @param {Object} systemInfo - 系统信息数据
 */
function handleSystemInfoUpdate(systemInfo) {
    const timestamp = systemInfo.timestamp ? new Date(systemInfo.timestamp).getTime() : Date.now();

    // 更新所有图表
    chartInstances.forEach((chart, canvasId) => {
        updateChartData(canvasId, systemInfo, timestamp);
    });
}

/**
 * 销毁图表
 * @param {string} canvasId - Canvas元素ID
 */
function destroyChart(canvasId) {
    const chart = chartInstances.get(canvasId);
    if (chart) {
        chart.destroy();
        chartInstances.delete(canvasId);
        chartDataCache.delete(canvasId);
    }
}

/**
 * 销毁所有图表
 */
function destroyAllCharts() {
    chartInstances.forEach((chart, canvasId) => {
        chart.destroy();
    });
    chartInstances.clear();
    chartDataCache.clear();
}

/**
 * 设置图表错误状态
 * @param {string} canvasId - Canvas元素ID
 * @param {string} errorMessage - 错误消息
 */
function setChartError(canvasId, errorMessage) {
    const canvas = document.getElementById(canvasId);
    if (canvas) {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#ff6b6b';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(errorMessage || '数据加载失败', canvas.width / 2, canvas.height / 2);
    }
}

/**
 * 重置图表数据
 * @param {string} canvasId - Canvas元素ID
 */
function resetChartData(canvasId) {
    const chart = chartInstances.get(canvasId);
    if (chart) {
        chart.data.labels = [];
        chart.data.datasets.forEach(dataset => {
            dataset.data = [];
        });
        chart.update();
    }
}

// 导出函数供外部使用
window.ChartManager = {
    createRealtimeChart,
    updateChartData,
    createMonitoringCharts,
    handleSystemInfoUpdate,
    destroyChart,
    destroyAllCharts,
    setChartError,
    resetChartData
};