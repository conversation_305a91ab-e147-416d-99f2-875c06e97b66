package database

import (
	"fmt"
	"sort"
	"time"
)

// Migration 迁移定义
type Migration struct {
	Version     int    `json:"version"`     // 版本号
	Name        string `json:"name"`        // 迁移名称
	Description string `json:"description"` // 迁移描述
	UpSQL       string `json:"up_sql"`      // 升级SQL
	DownSQL     string `json:"down_sql"`    // 降级SQL
}

// MigrationRecord 迁移记录
type MigrationRecord struct {
	ID        int       `json:"id" db:"id"`
	Version   int       `json:"version" db:"version"`
	Name      string    `json:"name" db:"name"`
	AppliedAt time.Time `json:"applied_at" db:"applied_at"`
}

// Migrator 数据库迁移器
type Migrator struct {
	db         *Database
	migrations []Migration
}

// NewMigrator 创建新的迁移器
func NewMigrator(db *Database) *Migrator {
	return &Migrator{
		db:         db,
		migrations: getAllMigrations(),
	}
}

// getAllMigrations 获取所有迁移定义
func getAllMigrations() []Migration {
	migrations := []Migration{
		{
			Version:     1,
			Name:        "create_servers_table",
			Description: "创建服务器信息表",
			UpSQL: `
				CREATE TABLE IF NOT EXISTS servers (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					name TEXT NOT NULL UNIQUE,
					ip TEXT NOT NULL,
					port INTEGER NOT NULL DEFAULT 5201,
					location TEXT DEFAULT '',
					provider TEXT DEFAULT '',
					is_active BOOLEAN NOT NULL DEFAULT 1,
					last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
					updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
				);
				
				CREATE INDEX IF NOT EXISTS idx_servers_name ON servers(name);
				CREATE INDEX IF NOT EXISTS idx_servers_ip ON servers(ip);
				CREATE INDEX IF NOT EXISTS idx_servers_active ON servers(is_active);
			`,
			DownSQL: `DROP TABLE IF EXISTS servers;`,
		},
		{
			Version:     2,
			Name:        "create_hourly_results_table",
			Description: "创建每小时测速结果表",
			UpSQL: `
				CREATE TABLE IF NOT EXISTS hourly_results (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					server_id INTEGER NOT NULL,
					test_hour DATETIME NOT NULL,
					upload_speed REAL DEFAULT 0,
					download_speed REAL DEFAULT 0,
					latency REAL DEFAULT 0,
					jitter REAL DEFAULT 0,
					packet_loss REAL DEFAULT 0,
					test_duration INTEGER DEFAULT 0,
					test_type TEXT NOT NULL DEFAULT 'both',
					status TEXT NOT NULL DEFAULT 'running',
					error_message TEXT DEFAULT '',
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
					FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE
				);
				
				CREATE UNIQUE INDEX IF NOT EXISTS idx_hourly_results_unique ON hourly_results(server_id, test_hour);
				CREATE INDEX IF NOT EXISTS idx_hourly_results_server ON hourly_results(server_id);
				CREATE INDEX IF NOT EXISTS idx_hourly_results_hour ON hourly_results(test_hour);
				CREATE INDEX IF NOT EXISTS idx_hourly_results_status ON hourly_results(status);
			`,
			DownSQL: `DROP TABLE IF EXISTS hourly_results;`,
		},
		{
			Version:     3,
			Name:        "create_sync_status_table",
			Description: "创建双机同步状态表",
			UpSQL: `
				CREATE TABLE IF NOT EXISTS sync_status (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					peer_ip TEXT NOT NULL UNIQUE,
					last_sync_time DATETIME DEFAULT CURRENT_TIMESTAMP,
					sync_status TEXT NOT NULL DEFAULT 'syncing',
					records_synced INTEGER DEFAULT 0,
					last_sync_error TEXT DEFAULT '',
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
					updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
				);
				
				CREATE INDEX IF NOT EXISTS idx_sync_status_peer ON sync_status(peer_ip);
				CREATE INDEX IF NOT EXISTS idx_sync_status_status ON sync_status(sync_status);
			`,
			DownSQL: `DROP TABLE IF EXISTS sync_status;`,
		},
		{
			Version:     4,
			Name:        "create_system_config_table",
			Description: "创建系统配置表",
			UpSQL: `
				CREATE TABLE IF NOT EXISTS system_config (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					config_key TEXT NOT NULL UNIQUE,
					config_value TEXT NOT NULL,
					description TEXT DEFAULT '',
					config_type TEXT NOT NULL DEFAULT 'string',
					is_system BOOLEAN NOT NULL DEFAULT 0,
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
					updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
				);
				
				CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(config_key);
				CREATE INDEX IF NOT EXISTS idx_system_config_type ON system_config(config_type);
				CREATE INDEX IF NOT EXISTS idx_system_config_system ON system_config(is_system);
			`,
			DownSQL: `DROP TABLE IF EXISTS system_config;`,
		},
		{
			Version:     5,
			Name:        "create_migration_table",
			Description: "创建迁移记录表",
			UpSQL: `
				CREATE TABLE IF NOT EXISTS migrations (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					version INTEGER NOT NULL UNIQUE,
					name TEXT NOT NULL,
					applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
				);
				
				CREATE INDEX IF NOT EXISTS idx_migrations_version ON migrations(version);
			`,
			DownSQL: `DROP TABLE IF EXISTS migrations;`,
		},
	}

	// 按版本号排序
	sort.Slice(migrations, func(i, j int) bool {
		return migrations[i].Version < migrations[j].Version
	})

	return migrations
}

// Migrate 执行数据库迁移
func (m *Migrator) Migrate() error {
	// 首先创建迁移表
	if err := m.createMigrationTable(); err != nil {
		return fmt.Errorf("failed to create migration table: %w", err)
	}

	// 获取当前数据库版本
	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("failed to get current version: %w", err)
	}

	// 执行需要的迁移
	for _, migration := range m.migrations {
		if migration.Version <= currentVersion {
			continue
		}

		if err := m.applyMigration(migration); err != nil {
			return fmt.Errorf("failed to apply migration %d: %w", migration.Version, err)
		}
	}

	return nil
}

// createMigrationTable 创建迁移表
func (m *Migrator) createMigrationTable() error {
	sql := `
		CREATE TABLE IF NOT EXISTS migrations (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			version INTEGER NOT NULL UNIQUE,
			name TEXT NOT NULL,
			applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE INDEX IF NOT EXISTS idx_migrations_version ON migrations(version);
	`

	_, err := m.db.GetDB().Exec(sql)
	return err
}

// getCurrentVersion 获取当前数据库版本
func (m *Migrator) getCurrentVersion() (int, error) {
	var version int
	query := "SELECT COALESCE(MAX(version), 0) FROM migrations"

	err := m.db.GetDB().QueryRow(query).Scan(&version)
	if err != nil {
		return 0, err
	}

	return version, nil
}

// applyMigration 应用单个迁移
func (m *Migrator) applyMigration(migration Migration) error {
	tx, err := m.db.BeginTx()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 执行迁移SQL
	if _, err := tx.Exec(migration.UpSQL); err != nil {
		return fmt.Errorf("failed to execute migration SQL: %w", err)
	}

	// 记录迁移
	insertSQL := "INSERT INTO migrations (version, name) VALUES (?, ?)"
	if _, err := tx.Exec(insertSQL, migration.Version, migration.Name); err != nil {
		return fmt.Errorf("failed to record migration: %w", err)
	}

	return tx.Commit()
}

// GetAppliedMigrations 获取已应用的迁移
func (m *Migrator) GetAppliedMigrations() ([]MigrationRecord, error) {
	query := "SELECT id, version, name, applied_at FROM migrations ORDER BY version"

	rows, err := m.db.GetDB().Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var records []MigrationRecord
	for rows.Next() {
		var record MigrationRecord
		if err := rows.Scan(&record.ID, &record.Version, &record.Name, &record.AppliedAt); err != nil {
			return nil, err
		}
		records = append(records, record)
	}

	return records, rows.Err()
}

// GetPendingMigrations 获取待应用的迁移
func (m *Migrator) GetPendingMigrations() ([]Migration, error) {
	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		return nil, err
	}

	var pending []Migration
	for _, migration := range m.migrations {
		if migration.Version > currentVersion {
			pending = append(pending, migration)
		}
	}

	return pending, nil
}
