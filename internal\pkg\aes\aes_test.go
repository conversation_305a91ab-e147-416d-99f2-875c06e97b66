package aes

import (
	"bytes"
	"testing"
)

func TestNew<PERSON>ey(t *testing.T) {
	password := "testpassword"
	salt := make([]byte, saltSize) // Use a fixed salt for deterministic key generation in test

	key1 := NewKey(password, salt)
	key2 := NewKey(password, salt)

	if !bytes.Equal(key1, key2) {
		t.<PERSON>("New<PERSON>ey should produce deterministic keys for same password and salt")
	}

	if len(key1) != keySize {
		t.<PERSON><PERSON>("NewKey should produce a key of size %d, got %d", keySize, len(key1))
	}

	// Test with different password
	password2 := "anotherpassword"
	key3 := NewKey(password2, salt)
	if bytes.Equal(key1, key3) {
		t.<PERSON>rf("New<PERSON>ey should produce different keys for different passwords")
	}

	// Test with different salt
	salt2 := []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16}
	key4 := NewKey(password, salt2)
	if bytes.Equal(key1, key4) {
		t.<PERSON>("New<PERSON>ey should produce different keys for different salts")
	}
}

func TestEncryptDecrypt(t *testing.T) {
	plaintext := []byte("This is a secret message.")
	password := "supersecretpassword"

	encrypted, err := Encrypt(plaintext, password)
	if err != nil {
		t.Fatalf("Encrypt failed: %v", err)
	}

	decrypted, err := Decrypt(encrypted, password)
	if err != nil {
		t.Fatalf("Decrypt failed: %v", err)
	}

	if !bytes.Equal(plaintext, decrypted) {
		t.Errorf("Decrypted plaintext does not match original. Expected: %s, Got: %s", plaintext, decrypted)
	}

	// Test with incorrect password
	incorrectPassword := "wrongpassword"
	_, err = Decrypt(encrypted, incorrectPassword)
	if err == nil {
		t.Errorf("Decrypt with incorrect password should fail, but succeeded")
	}
}

func TestEncryptDecryptEmptyPlaintext(t *testing.T) {
	plaintext := []byte("")
	password := "testpassword"

	encrypted, err := Encrypt(plaintext, password)
	if err != nil {
		t.Fatalf("Encrypt failed for empty plaintext: %v", err)
	}

	decrypted, err := Decrypt(encrypted, password)
	if err != nil {
		t.Fatalf("Decrypt failed for empty plaintext: %v", err)
	}

	if !bytes.Equal(plaintext, decrypted) {
		t.Errorf("Decrypted empty plaintext does not match original.")
	}
}

func TestEncryptDecryptLargePlaintext(t *testing.T) {
	plaintext := bytes.Repeat([]byte("a"), 1024*1024) // 1MB plaintext
	password := "longandstrongpassword"

	encrypted, err := Encrypt(plaintext, password)
	if err != nil {
		t.Fatalf("Encrypt failed for large plaintext: %v", err)
	}

	decrypted, err := Decrypt(encrypted, password)
	if err != nil {
		t.Fatalf("Decrypt failed for large plaintext: %v", err)
	}

	if !bytes.Equal(plaintext, decrypted) {
		t.Errorf("Decrypted large plaintext does not match original.")
	}
}

func BenchmarkEncrypt(b *testing.B) {
	plaintext := bytes.Repeat([]byte("a"), 1024) // 1KB plaintext
	password := "benchmarkpassword"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := Encrypt(plaintext, password)
		if err != nil {
			b.Fatalf("Encrypt failed: %v", err)
		}
	}
}

func BenchmarkDecrypt(b *testing.B) {
	plaintext := bytes.Repeat([]byte("a"), 1024) // 1KB plaintext
	password := "benchmarkpassword"

	encrypted, err := Encrypt(plaintext, password)
	if err != nil {
		b.Fatalf("Encrypt failed during setup: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := Decrypt(encrypted, password)
		if err != nil {
			b.Fatalf("Decrypt failed: %v", err)
		}
	}
}