// services.js
console.log('Services script loaded.');

document.addEventListener('DOMContentLoaded', function() {
    // 示例：模拟服务操作
    document.querySelectorAll('.service-action-btn').forEach(button => {
        button.addEventListener('click', function() {
            const serviceName = this.dataset.serviceName;
            const action = this.dataset.action;
            console.log(`Performing ${action} on ${serviceName}`);
            // 实际应用中，这里会发起API请求
            alert(`执行 ${action} 服务: ${serviceName}`);
        });
    });

    // 示例：模拟添加新服务
    const addServiceForm = document.querySelector('#addServiceForm');
    if (addServiceForm) {
        addServiceForm.addEventListener('submit', function(event) {
            event.preventDefault();
            const serviceName = document.getElementById('serviceName').value;
            const serviceCommand = document.getElementById('serviceCommand').value;
            console.log(`Adding new service: ${serviceName} with command: ${serviceCommand}`);
            // 实际应用中，这里会发起API请求
            alert(`添加新服务: ${serviceName}`);
            addServiceForm.reset();
        });
    }
});