package cli

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// Mode 运行模式
type Mode string

const (
	ModeServer Mode = "server"
	ModeClient Mode = "client"
	ModeBoth   Mode = "both"
)

// Args 命令行参数结构
type Args struct {
	// 通用参数
	Version  bool   `json:"version"`   // 显示版本信息
	Help     bool   `json:"help"`      // 显示帮助信息
	Config   string `json:"config"`    // 配置文件路径
	Mode     Mode   `json:"mode"`      // 运行模式
	DataDir  string `json:"data_dir"`  // 数据目录
	LogLevel string `json:"log_level"` // 日志级别
	LogFile  string `json:"log_file"`  // 日志文件
	Daemon   bool   `json:"daemon"`    // 后台运行
	PidFile  string `json:"pid_file"`  // PID文件路径

	// 服务端参数
	ServerMode bool   `json:"server_mode"` // 服务端模式
	ServerPort int    `json:"server_port"` // 服务端端口
	ServerHost string `json:"server_host"` // 服务端监听地址
	WebPort    int    `json:"web_port"`    // Web界面端口
	WebHost    string `json:"web_host"`    // Web界面监听地址
	APIPort    int    `json:"api_port"`    // API端口
	APIHost    string `json:"api_host"`    // API监听地址
	Password   string `json:"password"`    // 服务端密码
	TLSCert    string `json:"tls_cert"`    // TLS证书文件
	TLSKey     string `json:"tls_key"`     // TLS私钥文件
	EnableTLS  bool   `json:"enable_tls"`  // 启用TLS
	InitData   bool   `json:"init_data"`   // 初始化测试数据

	// 客户端参数
	ClientMode     bool          `json:"client_mode"`     // 客户端模式
	ClientName     string        `json:"client_name"`     // 客户端名称
	ServerIP       string        `json:"server_ip"`       // 服务端IP
	ServerPassword string        `json:"server_password"` // 服务端密码
	ConnectTimeout time.Duration `json:"connect_timeout"` // 连接超时
	ReportInterval time.Duration `json:"report_interval"` // 上报间隔
	TestServers    string        `json:"test_servers"`    // 测试服务器列表
	TestDuration   time.Duration `json:"test_duration"`   // 测试持续时间
	TestParallel   int           `json:"test_parallel"`   // 并行测试数
	TestProtocol   string        `json:"test_protocol"`   // 测试协议

	// 调试和开发参数
	Debug      bool   `json:"debug"`       // 调试模式
	Verbose    bool   `json:"verbose"`     // 详细输出
	DryRun     bool   `json:"dry_run"`     // 干运行模式
	Profile    string `json:"profile"`     // 性能分析
	MemProfile string `json:"mem_profile"` // 内存分析
	CPUProfile string `json:"cpu_profile"` // CPU分析
}

// Parser 命令行参数解析器
type Parser struct {
	flagSet *flag.FlagSet
	args    *Args
}

// NewParser 创建新的参数解析器
func NewParser(name string) *Parser {
	return &Parser{
		flagSet: flag.NewFlagSet(name, flag.ExitOnError),
		args:    &Args{},
	}
}

// Parse 解析命令行参数
func (p *Parser) Parse(arguments []string) (*Args, error) {
	var modeStr string
	// 定义通用参数
	p.flagSet.BoolVar(&p.args.Version, "version", false, "显示版本信息")
	p.flagSet.BoolVar(&p.args.Version, "v", false, "显示版本信息 (简写)")
	p.flagSet.BoolVar(&p.args.Help, "help", false, "显示帮助信息")
	p.flagSet.BoolVar(&p.args.Help, "h", false, "显示帮助信息 (简写)")
	p.flagSet.StringVar(&p.args.Config, "config", "", "配置文件路径")
	p.flagSet.StringVar(&p.args.Config, "c", "", "配置文件路径 (简写)")
	p.flagSet.StringVar(&modeStr, "mode", "both", "运行模式 (server/client/both)")
	p.flagSet.StringVar(&p.args.DataDir, "data-dir", "./data", "数据目录")
	p.flagSet.StringVar(&p.args.LogLevel, "log-level", "info", "日志级别 (debug/info/warn/error)")
	p.flagSet.StringVar(&p.args.LogFile, "log-file", "", "日志文件路径")
	p.flagSet.BoolVar(&p.args.Daemon, "daemon", false, "后台运行")
	p.flagSet.BoolVar(&p.args.Daemon, "d", false, "后台运行 (简写)")
	p.flagSet.StringVar(&p.args.PidFile, "pid-file", "", "PID文件路径")

	// 定义服务端参数
	p.flagSet.BoolVar(&p.args.ServerMode, "server", false, "服务端模式")
	p.flagSet.BoolVar(&p.args.ServerMode, "s", false, "服务端模式 (简写)")
	p.flagSet.IntVar(&p.args.ServerPort, "port", 8443, "服务端端口")
	p.flagSet.IntVar(&p.args.ServerPort, "p", 8443, "服务端端口 (简写)")
	p.flagSet.StringVar(&p.args.ServerHost, "host", "0.0.0.0", "服务端监听地址")
	p.flagSet.IntVar(&p.args.WebPort, "web-port", 8080, "Web界面端口")
	p.flagSet.StringVar(&p.args.WebHost, "web-host", "0.0.0.0", "Web界面监听地址")
	p.flagSet.IntVar(&p.args.APIPort, "api-port", 8443, "API端口")
	p.flagSet.StringVar(&p.args.APIHost, "api-host", "0.0.0.0", "API监听地址")
	p.flagSet.StringVar(&p.args.Password, "password", "", "服务端密码")
	p.flagSet.StringVar(&p.args.Password, "pw", "", "服务端密码 (简写)")
	p.flagSet.StringVar(&p.args.TLSCert, "tls-cert", "", "TLS证书文件")
	p.flagSet.StringVar(&p.args.TLSKey, "tls-key", "", "TLS私钥文件")
	p.flagSet.BoolVar(&p.args.EnableTLS, "tls", false, "启用TLS")
	p.flagSet.BoolVar(&p.args.InitData, "init-data", false, "初始化测试数据")

	// 定义客户端参数
	p.flagSet.BoolVar(&p.args.ClientMode, "client", false, "客户端模式")
	p.flagSet.StringVar(&p.args.ClientName, "name", "", "客户端名称")
	p.flagSet.StringVar(&p.args.ClientName, "n", "", "客户端名称 (简写)")
	p.flagSet.StringVar(&p.args.ServerIP, "server-ip", "", "服务端IP地址")
	p.flagSet.StringVar(&p.args.ServerIP, "ip", "", "服务端IP地址 (简写)")
	p.flagSet.StringVar(&p.args.ServerPassword, "server-password", "", "服务端密码")
	p.flagSet.DurationVar(&p.args.ConnectTimeout, "connect-timeout", 30*time.Second, "连接超时时间")
	p.flagSet.DurationVar(&p.args.ReportInterval, "report-interval", 60*time.Second, "上报间隔时间")
	p.flagSet.StringVar(&p.args.TestServers, "test-servers", "", "测试服务器列表 (逗号分隔)")
	p.flagSet.DurationVar(&p.args.TestDuration, "test-duration", 30*time.Second, "测试持续时间")
	p.flagSet.IntVar(&p.args.TestParallel, "test-parallel", 4, "并行测试数")
	p.flagSet.StringVar(&p.args.TestProtocol, "test-protocol", "tcp", "测试协议 (tcp/udp/both)")

	// 定义调试参数
	p.flagSet.BoolVar(&p.args.Debug, "debug", false, "调试模式")
	p.flagSet.BoolVar(&p.args.Verbose, "verbose", false, "详细输出")
	p.flagSet.BoolVar(&p.args.DryRun, "dry-run", false, "干运行模式")
	p.flagSet.StringVar(&p.args.Profile, "profile", "", "性能分析端口")
	p.flagSet.StringVar(&p.args.MemProfile, "memprofile", "", "内存分析文件")
	p.flagSet.StringVar(&p.args.CPUProfile, "cpuprofile", "", "CPU分析文件")

	// 解析参数
	if err := p.flagSet.Parse(arguments); err != nil {
		return nil, fmt.Errorf("failed to parse arguments: %w", err)
	}

	// 后处理和验证
	if err := p.postProcess(modeStr); err != nil {
		return nil, fmt.Errorf("argument validation failed: %w", err)
	}

	return p.args, nil
}

// postProcess 后处理参数
func (p *Parser) postProcess(modeStr string) error {
	// 处理运行模式
	if p.args.ServerMode && p.args.ClientMode {
		p.args.Mode = ModeBoth
	} else if p.args.ServerMode {
		p.args.Mode = ModeServer
	} else if p.args.ClientMode {
		p.args.Mode = ModeClient
	} else {
		p.args.Mode = Mode(modeStr)
	}

	// 验证运行模式
	switch p.args.Mode {
	case ModeServer, ModeClient, ModeBoth:
		// 有效模式
	default:
		return fmt.Errorf("invalid mode: %s (must be server/client/both)", p.args.Mode)
	}

	// 设置默认配置文件路径
	if p.args.Config == "" {
		p.args.Config = "./configs/config.yaml"
	}

	// 设置默认PID文件路径
	if p.args.PidFile == "" {
		p.args.PidFile = filepath.Join(p.args.DataDir, "monitor.pid")
	}

	// 验证服务端参数
	if p.args.Mode == ModeServer || p.args.Mode == ModeBoth {
		if p.args.ServerPort <= 0 || p.args.ServerPort > 65535 {
			return fmt.Errorf("invalid server port: %d", p.args.ServerPort)
		}
		if p.args.WebPort <= 0 || p.args.WebPort > 65535 {
			return fmt.Errorf("invalid web port: %d", p.args.WebPort)
		}
		if p.args.APIPort <= 0 || p.args.APIPort > 65535 {
			return fmt.Errorf("invalid API port: %d", p.args.APIPort)
		}
		if p.args.EnableTLS {
			if p.args.TLSCert == "" {
				return fmt.Errorf("TLS certificate file is required when TLS is enabled")
			}
			if p.args.TLSKey == "" {
				return fmt.Errorf("TLS key file is required when TLS is enabled")
			}
		}
	}

	// 验证客户端参数
	if p.args.Mode == ModeClient || p.args.Mode == ModeBoth {
		if p.args.ClientName == "" {
			// 使用主机名作为默认客户端名称
			if hostname, err := os.Hostname(); err == nil {
				p.args.ClientName = hostname
			} else {
				p.args.ClientName = "unknown-client"
			}
		}
		if p.args.ServerIP == "" && p.args.Mode == ModeClient {
			return fmt.Errorf("server IP is required in client mode")
		}
		if p.args.TestParallel <= 0 {
			return fmt.Errorf("test parallel must be positive")
		}
		if p.args.TestProtocol != "tcp" && p.args.TestProtocol != "udp" && p.args.TestProtocol != "both" {
			return fmt.Errorf("invalid test protocol: %s", p.args.TestProtocol)
		}
	}

	// 验证日志级别
	validLogLevels := []string{"debug", "info", "warn", "error", "fatal", "panic"}
	if !contains(validLogLevels, p.args.LogLevel) {
		return fmt.Errorf("invalid log level: %s", p.args.LogLevel)
	}

	return nil
}

// PrintUsage 打印使用说明
func (p *Parser) PrintUsage() {
	fmt.Printf("Server Monitor - 分布式服务器监控系统\n\n")
	fmt.Printf("用法:\n")
	fmt.Printf("  %s [选项]\n\n", os.Args[0])
	fmt.Printf("运行模式:\n")
	fmt.Printf("  -s, --server              服务端模式\n")
	fmt.Printf("  --client                  客户端模式\n")
	fmt.Printf("  --mode MODE               运行模式 (server/client/both)\n\n")
	fmt.Printf("通用选项:\n")
	fmt.Printf("  -h, --help                显示帮助信息\n")
	fmt.Printf("  -v, --version             显示版本信息\n")
	fmt.Printf("  -c, --config FILE         配置文件路径\n")
	fmt.Printf("  --data-dir DIR            数据目录\n")
	fmt.Printf("  --log-level LEVEL         日志级别 (debug/info/warn/error)\n")
	fmt.Printf("  --log-file FILE           日志文件路径\n")
	fmt.Printf("  -d, --daemon              后台运行\n")
	fmt.Printf("  --pid-file FILE           PID文件路径\n\n")
	fmt.Printf("服务端选项:\n")
	fmt.Printf("  -p, --port PORT           服务端端口 (默认: 8443)\n")
	fmt.Printf("  --host HOST               服务端监听地址 (默认: 0.0.0.0)\n")
	fmt.Printf("  --web-port PORT           Web界面端口 (默认: 8080)\n")
	fmt.Printf("  --web-host HOST           Web界面监听地址\n")
	fmt.Printf("  --api-port PORT           API端口\n")
	fmt.Printf("  --api-host HOST           API监听地址\n")
	fmt.Printf("  --password, --pw PASS     服务端密码\n")
	fmt.Printf("  --tls                     启用TLS\n")
	fmt.Printf("  --tls-cert FILE           TLS证书文件\n")
	fmt.Printf("  --tls-key FILE            TLS私钥文件\n")
	fmt.Printf("  --init-data               初始化测试数据\n\n")
	fmt.Printf("客户端选项:\n")
	fmt.Printf("  -n, --name NAME           客户端名称\n")
	fmt.Printf("  --server, --ip IP         服务端IP地址\n")
	fmt.Printf("  --server-password PASS    服务端密码\n")
	fmt.Printf("  --connect-timeout TIME    连接超时时间\n")
	fmt.Printf("  --report-interval TIME    上报间隔时间\n")
	fmt.Printf("  --test-servers LIST       测试服务器列表 (逗号分隔)\n")
	fmt.Printf("  --test-duration TIME      测试持续时间\n")
	fmt.Printf("  --test-parallel NUM       并行测试数\n")
	fmt.Printf("  --test-protocol PROTO     测试协议 (tcp/udp/both)\n\n")
	fmt.Printf("调试选项:\n")
	fmt.Printf("  --debug                   调试模式\n")
	fmt.Printf("  --verbose                 详细输出\n")
	fmt.Printf("  --dry-run                 干运行模式\n")
	fmt.Printf("  --profile PORT            性能分析端口\n")
	fmt.Printf("  --memprofile FILE         内存分析文件\n")
	fmt.Printf("  --cpuprofile FILE         CPU分析文件\n\n")
	fmt.Printf("示例:\n")
	fmt.Printf("  # 服务端模式\n")
	fmt.Printf("  %s --server --port 8443 --web-port 8080\n\n", os.Args[0])
	fmt.Printf("  # 客户端模式\n")
	fmt.Printf("  %s --client --name client-01 --server 192.168.1.100\n\n", os.Args[0])
	fmt.Printf("  # 双模式\n")
	fmt.Printf("  %s --mode both --config ./config.yaml\n\n", os.Args[0])
}

// GetArgs 获取解析后的参数
func (p *Parser) GetArgs() *Args {
	return p.args
}

// contains 检查字符串切片是否包含指定值
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
