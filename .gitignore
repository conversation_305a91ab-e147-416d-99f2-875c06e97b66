# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node_modules (if using Node.js tools)
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Project specific ignores
data/
*.db
*.sqlite
*.sqlite3
monitor
monitor.exe
server-monitor
server-monitor.exe

# Temporary files
tmp/
temp/
.tmp/

# Build artifacts
build/
dist/
bin/

# Configuration files with sensitive data
config.yaml
config.json
*.key
*.pem
*.crt

# Tailwind CSS build files
web/static/css/output.css

# Development tools
.air.toml

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
