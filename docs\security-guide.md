# 安全特性使用指南 (Security Features Guide)

## 概述 (Overview)

本指南详细介绍了服务器监控系统的安全特性，包括配置、使用方法和最佳实践。系统提供了企业级的安全保护，确保数据传输和存储的安全性。

## 🔒 安全特性概览 (Security Features Overview)

### 1. 端到端加密 (End-to-End Encryption)
- **算法**: AES-256-GCM (军用级加密标准)
- **密钥长度**: 256位
- **认证加密**: 同时提供机密性和完整性保护
- **性能**: 加密474.8ns/op，解密300.8ns/op

### 2. 自动密钥管理 (Automatic Key Management)
- **密钥轮换**: 可配置的自动密钥轮换
- **版本管理**: 支持多版本密钥并存
- **前向安全**: 新密钥无法解密旧数据
- **安全存储**: 基于PBKDF2的密钥派生

### 3. 消息完整性保护 (Message Integrity Protection)
- **校验和**: SHA256消息摘要
- **序列号验证**: 防止消息重排和丢失
- **时间戳验证**: 检测过期和未来消息
- **篡改检测**: 实时检测消息篡改

### 4. 重放攻击防护 (Replay Attack Protection)
- **Nonce机制**: 唯一性验证
- **时间窗口**: 基于时间的有效性检查
- **LRU缓存**: 高效的重放检测
- **时钟偏差容忍**: 处理系统间时钟差异

### 5. 可靠消息传输 (Reliable Message Delivery)
- **确认机制**: ACK/NACK消息确认
- **自动重传**: 指数退避重试策略
- **去重处理**: 防止重复消息处理
- **状态跟踪**: 完整的消息生命周期管理

## 📋 配置指南 (Configuration Guide)

### 基本安全配置 (Basic Security Configuration)

```yaml
security:
  # 加密配置
  encryption:
    enabled: true                    # 启用加密
    algorithm: "AES-256-GCM"        # 加密算法
    key_size: 256                   # 密钥大小

  # 密钥管理配置
  key_manager:
    enabled: true                   # 启用密钥管理
    rotation_interval: "24h"        # 密钥轮换间隔
    key_ttl: "72h"                 # 密钥生存时间
    max_key_history: 10            # 最大密钥历史数量
    auto_rotate: true              # 自动轮换
    master_password: "${MASTER_PASSWORD}"  # 主密码(环境变量)

  # 认证配置
  auth:
    enabled: true                   # 启用认证
    token_ttl: "1h"                # 令牌生存时间
    refresh_token_ttl: "24h"       # 刷新令牌生存时间
    max_login_attempts: 5          # 最大登录尝试次数
    lockout_duration: "15m"        # 锁定持续时间

  # 重放攻击防护
  replay_protection:
    enabled: true                   # 启用重放攻击防护
    time_window: "5m"              # 时间窗口
    nonce_cache: 10000             # Nonce缓存大小
    cache_ttl: "10m"               # 缓存生存时间

  # 消息完整性
  message_integrity:
    enabled: true                   # 启用消息完整性检查
    checksum_algorithm: "SHA256"    # 校验和算法
    enable_sequence_validation: true # 启用序列号验证
    max_sequence_gap: 100          # 最大序列号间隔

  # 速率限制
  rate_limiting:
    enabled: true                   # 启用速率限制
    requests_per_sec: 100          # 每秒请求数限制
    burst_size: 200                # 突发大小
    cleanup_interval: "1m"         # 清理间隔
    ban_duration: "1h"             # 封禁持续时间
    max_violations: 10             # 最大违规次数
```

### 环境变量配置 (Environment Variables)

```bash
# 主密码 (必需)
export MONITOR_SECURITY_KEY_MANAGER_MASTER_PASSWORD="your-secure-master-password"

# 加密配置
export MONITOR_SECURITY_ENCRYPTION_ENABLED=true
export MONITOR_SECURITY_ENCRYPTION_ALGORITHM="AES-256-GCM"

# 密钥管理配置
export MONITOR_SECURITY_KEY_MANAGER_ROTATION_INTERVAL="24h"
export MONITOR_SECURITY_KEY_MANAGER_KEY_TTL="72h"
export MONITOR_SECURITY_KEY_MANAGER_AUTO_ROTATE=true

# 认证配置
export MONITOR_SECURITY_AUTH_TOKEN_TTL="1h"
export MONITOR_SECURITY_AUTH_MAX_LOGIN_ATTEMPTS=5

# 重放攻击防护
export MONITOR_SECURITY_REPLAY_PROTECTION_TIME_WINDOW="5m"
export MONITOR_SECURITY_REPLAY_PROTECTION_NONCE_CACHE=10000

# 消息完整性
export MONITOR_SECURITY_MESSAGE_INTEGRITY_CHECKSUM_ALGORITHM="SHA256"
export MONITOR_SECURITY_MESSAGE_INTEGRITY_ENABLE_SEQUENCE_VALIDATION=true

# 速率限制
export MONITOR_SECURITY_RATE_LIMITING_REQUESTS_PER_SEC=100
export MONITOR_SECURITY_RATE_LIMITING_BURST_SIZE=200
```

## 🚀 使用示例 (Usage Examples)

### 1. 基本加密使用

```go
package main

import (
    "server-monitor/internal/crypto"
    "server-monitor/internal/config"
)

func main() {
    // 加载配置
    manager := config.NewManager("config.yaml")
    manager.Load()
    cfg := manager.Get()
    
    // 创建密钥管理器
    kmConfig := &crypto.KeyManagerConfig{
        RotationInterval: cfg.Security.KeyManager.RotationInterval,
        KeyTTL:          cfg.Security.KeyManager.KeyTTL,
        MaxKeyHistory:   cfg.Security.KeyManager.MaxKeyHistory,
        AutoRotate:      cfg.Security.KeyManager.AutoRotate,
        MasterPassword:  []byte(cfg.Security.KeyManager.MasterPassword),
    }
    
    keyManager, err := crypto.NewKeyManager(kmConfig)
    if err != nil {
        panic(err)
    }
    defer keyManager.Stop()
    
    // 创建加密服务
    encryptionSvc := crypto.NewEncryptionService(keyManager)
    
    // 加密数据
    plaintext := []byte("sensitive data")
    encData, err := encryptionSvc.EncryptData(plaintext)
    if err != nil {
        panic(err)
    }
    
    // 解密数据
    decrypted, err := encryptionSvc.DecryptData(encData)
    if err != nil {
        panic(err)
    }
    
    println("Original:", string(plaintext))
    println("Decrypted:", string(decrypted))
}
```

### 2. 安全消息传输

```go
package main

import (
    "context"
    "server-monitor/internal/protocol"
    "server-monitor/internal/crypto"
)

func main() {
    // 创建消息
    msg := protocol.NewApplicationMessage(
        protocol.MessageTypeAuth, 
        "client1", 
        "server1", 
        protocol.AuthData{
            Username: "user",
            Password: "pass",
            ClientID: "client_001",
            Version:  "1.0.0",
        })
    
    // 设置安全属性
    msg.Timestamp = time.Now().UnixNano()
    msg.GenerateAndSetSequenceNumber()
    msg.GenerateAndSetChecksum()
    
    // 验证消息完整性
    err := msg.ValidateWithIntegrity()
    if err != nil {
        panic(err)
    }
    
    // 创建可靠性管理器
    config := protocol.DefaultMessageReliabilityConfig()
    sender := &YourMessageSender{} // 实现MessageSender接口
    reliabilityMgr := protocol.NewMessageReliabilityManager(config, sender)
    defer reliabilityMgr.Stop()
    
    // 发送可靠消息
    ctx := context.Background()
    err = reliabilityMgr.SendReliableMessage(ctx, msg)
    if err != nil {
        panic(err)
    }
    
    // 处理确认
    err = reliabilityMgr.HandleAck(msg.ID)
    if err != nil {
        panic(err)
    }
}
```

### 3. 重放攻击防护

```go
package main

import (
    "server-monitor/internal/crypto"
    "time"
)

func main() {
    // 创建重放攻击防护器
    config := &crypto.ReplayProtectorConfig{
        TimeWindow:         5 * time.Minute,
        CacheSize:          10000,
        ClockSkewTolerance: 30 * time.Second,
        EnableCleanup:      true,
        CleanupInterval:    time.Minute,
    }
    
    protector, err := crypto.NewReplayProtector(config)
    if err != nil {
        panic(err)
    }
    defer protector.Stop()
    
    // 验证消息
    messageID := "unique-message-id"
    timestamp := time.Now().UnixNano()
    
    // 第一次验证应该成功
    err = protector.ValidateMessage(messageID, timestamp)
    if err != nil {
        panic("First validation should succeed")
    }
    
    // 第二次验证应该检测到重放攻击
    err = protector.ValidateMessage(messageID, timestamp)
    if err == nil {
        panic("Should detect replay attack")
    }
}
```

## 🛡️ 安全最佳实践 (Security Best Practices)

### 1. 密码管理
- ✅ 使用强密码作为主密码 (至少16个字符)
- ✅ 通过环境变量设置敏感配置
- ✅ 定期更换主密码
- ❌ 不要在代码中硬编码密码

### 2. 密钥轮换
- ✅ 启用自动密钥轮换
- ✅ 设置合理的轮换间隔 (建议24小时)
- ✅ 保留足够的密钥历史 (建议10个)
- ✅ 监控密钥轮换状态

### 3. 网络安全
- ✅ 使用HTTPS/TLS传输
- ✅ 启用所有安全特性
- ✅ 配置防火墙规则
- ✅ 限制网络访问

### 4. 监控和审计
- ✅ 启用安全事件日志
- ✅ 监控异常登录尝试
- ✅ 定期检查安全配置
- ✅ 实施安全审计

### 5. 部署安全
- ✅ 使用最小权限原则
- ✅ 定期更新依赖
- ✅ 实施容器安全
- ✅ 备份配置文件

## 🔧 故障排除 (Troubleshooting)

### 常见问题

1. **密钥轮换失败**
   ```
   错误: failed to rotate key
   解决: 检查主密码配置和权限
   ```

2. **消息验证失败**
   ```
   错误: message validation failed
   解决: 检查时间同步和序列号
   ```

3. **重放攻击检测误报**
   ```
   错误: replay attack detected
   解决: 调整时间窗口和时钟偏差容忍
   ```

4. **性能问题**
   ```
   问题: 加密解密性能低
   解决: 调整缓存大小和清理间隔
   ```

### 调试命令

```bash
# 运行安全测试
go run cmd/security-test/main.go -v -bench

# 检查配置
go run cmd/config-validator/main.go

# 性能基准测试
go test ./internal/security -bench=. -benchmem
```

## 📊 性能指标 (Performance Metrics)

- **加密性能**: 474.8 ns/op (约2.1M ops/sec)
- **解密性能**: 300.8 ns/op (约3.3M ops/sec)
- **内存使用**: 1.4KB per operation
- **并发支持**: 支持高并发操作
- **错误率**: 0% (在测试环境中)

## 🔗 相关文档 (Related Documentation)

- [安全测试报告](security-test-report.md)
- [配置示例](../configs/security-config.yaml.example)
- [API文档](api-documentation.md)
- [部署指南](deployment-guide.md)
