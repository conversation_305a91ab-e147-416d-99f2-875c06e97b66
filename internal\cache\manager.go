package cache

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// CacheItem 缓存项
type CacheItem struct {
	Value      interface{} `json:"value"`      // 缓存值
	Expiration int64       `json:"expiration"` // 过期时间戳
	CreatedAt  int64       `json:"created_at"` // 创建时间戳
}

// IsExpired 检查是否过期
func (item *CacheItem) IsExpired() bool {
	if item.Expiration == 0 {
		return false // 永不过期
	}
	return time.Now().Unix() > item.Expiration
}

// Manager 缓存管理器
type Manager struct {
	items   map[string]*CacheItem // 缓存项
	mu      sync.RWMutex          // 读写锁
	janitor *janitor              // 清理器
}

// janitor 清理器
type janitor struct {
	interval time.Duration // 清理间隔
	stop     chan bool     // 停止信号
}

// NewManager 创建缓存管理器
func NewManager(cleanupInterval time.Duration) *Manager {
	m := &Manager{
		items: make(map[string]*CacheItem),
	}

	// 启动清理器
	if cleanupInterval > 0 {
		m.janitor = &janitor{
			interval: cleanupInterval,
			stop:     make(chan bool),
		}
		go m.janitor.run(m)
	}

	return m
}

// Set 设置缓存项
func (m *Manager) Set(key string, value interface{}, duration time.Duration) {
	var expiration int64
	if duration > 0 {
		expiration = time.Now().Add(duration).Unix()
	}

	item := &CacheItem{
		Value:      value,
		Expiration: expiration,
		CreatedAt:  time.Now().Unix(),
	}

	m.mu.Lock()
	m.items[key] = item
	m.mu.Unlock()
}

// Get 获取缓存项
func (m *Manager) Get(key string) (interface{}, bool) {
	m.mu.RLock()
	item, found := m.items[key]
	m.mu.RUnlock()

	if !found {
		return nil, false
	}

	if item.IsExpired() {
		m.Delete(key)
		return nil, false
	}

	return item.Value, true
}

// GetString 获取字符串缓存项
func (m *Manager) GetString(key string) (string, bool) {
	value, found := m.Get(key)
	if !found {
		return "", false
	}

	if str, ok := value.(string); ok {
		return str, true
	}
	return "", false
}

// GetInt 获取整数缓存项
func (m *Manager) GetInt(key string) (int, bool) {
	value, found := m.Get(key)
	if !found {
		return 0, false
	}

	if i, ok := value.(int); ok {
		return i, true
	}
	return 0, false
}

// GetFloat64 获取浮点数缓存项
func (m *Manager) GetFloat64(key string) (float64, bool) {
	value, found := m.Get(key)
	if !found {
		return 0, false
	}

	if f, ok := value.(float64); ok {
		return f, true
	}
	return 0, false
}

// GetJSON 获取JSON缓存项并反序列化
func (m *Manager) GetJSON(key string, target interface{}) bool {
	value, found := m.Get(key)
	if !found {
		return false
	}

	if jsonStr, ok := value.(string); ok {
		return json.Unmarshal([]byte(jsonStr), target) == nil
	}
	return false
}

// SetJSON 序列化为JSON并设置缓存项
func (m *Manager) SetJSON(key string, value interface{}, duration time.Duration) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}

	m.Set(key, string(jsonData), duration)
	return nil
}

// Delete 删除缓存项
func (m *Manager) Delete(key string) {
	m.mu.Lock()
	delete(m.items, key)
	m.mu.Unlock()
}

// Exists 检查缓存项是否存在
func (m *Manager) Exists(key string) bool {
	_, found := m.Get(key)
	return found
}

// Clear 清空所有缓存项
func (m *Manager) Clear() {
	m.mu.Lock()
	m.items = make(map[string]*CacheItem)
	m.mu.Unlock()
}

// Count 获取缓存项数量
func (m *Manager) Count() int {
	m.mu.RLock()
	count := len(m.items)
	m.mu.RUnlock()
	return count
}

// Keys 获取所有键
func (m *Manager) Keys() []string {
	m.mu.RLock()
	keys := make([]string, 0, len(m.items))
	for key := range m.items {
		keys = append(keys, key)
	}
	m.mu.RUnlock()
	return keys
}

// GetStats 获取缓存统计信息
func (m *Manager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var expiredCount int
	for _, item := range m.items {
		if item.IsExpired() {
			expiredCount++
		}
	}

	return map[string]interface{}{
		"total_items":   len(m.items),
		"expired_items": expiredCount,
		"active_items":  len(m.items) - expiredCount,
	}
}

// Increment 递增数值缓存项
func (m *Manager) Increment(key string, delta int) (int, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	item, found := m.items[key]
	if !found {
		// 不存在则创建
		newItem := &CacheItem{
			Value:     delta,
			CreatedAt: time.Now().Unix(),
		}
		m.items[key] = newItem
		return delta, nil
	}

	if item.IsExpired() {
		// 过期则重新创建
		newItem := &CacheItem{
			Value:     delta,
			CreatedAt: time.Now().Unix(),
		}
		m.items[key] = newItem
		return delta, nil
	}

	if val, ok := item.Value.(int); ok {
		newVal := val + delta
		item.Value = newVal
		return newVal, nil
	}

	return 0, fmt.Errorf("缓存项不是整数类型")
}

// Decrement 递减数值缓存项
func (m *Manager) Decrement(key string, delta int) (int, error) {
	return m.Increment(key, -delta)
}

// Touch 更新缓存项的过期时间
func (m *Manager) Touch(key string, duration time.Duration) bool {
	m.mu.Lock()
	defer m.mu.Unlock()

	item, found := m.items[key]
	if !found || item.IsExpired() {
		return false
	}

	if duration > 0 {
		item.Expiration = time.Now().Add(duration).Unix()
	} else {
		item.Expiration = 0 // 永不过期
	}

	return true
}

// Stop 停止缓存管理器
func (m *Manager) Stop() {
	if m.janitor != nil {
		m.janitor.stop <- true
	}
}

// run 清理器运行
func (j *janitor) run(m *Manager) {
	ticker := time.NewTicker(j.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.deleteExpired()
		case <-j.stop:
			return
		}
	}
}

// deleteExpired 删除过期项
func (m *Manager) deleteExpired() {
	m.mu.Lock()
	defer m.mu.Unlock()

	for key, item := range m.items {
		if item.IsExpired() {
			delete(m.items, key)
		}
	}
}
