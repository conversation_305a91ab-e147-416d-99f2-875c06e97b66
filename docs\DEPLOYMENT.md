# 部署指南

本文档提供服务器监控系统的详细部署指南，包括Docker部署、手动部署和生产环境配置。

## 📋 系统要求

### 最低要求
- **操作系统**: Windows 10+, Ubuntu 18.04+, CentOS 7+, macOS 10.15+
- **内存**: 512MB RAM
- **磁盘**: 1GB 可用空间
- **网络**: 支持HTTP/HTTPS访问

### 推荐配置
- **操作系统**: Ubuntu 20.04 LTS 或 CentOS 8
- **内存**: 2GB RAM
- **磁盘**: 10GB 可用空间
- **CPU**: 2核心以上

### 依赖软件
- **Go**: 1.21+ (仅开发/编译时需要)
- **iPerf3**: 网络性能测试 (可选)
- **SQLite3**: 数据库支持 (通常已内置)

## 🐳 Docker 部署 (推荐)

### 1. 创建 Dockerfile

```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY . .

# 安装依赖
RUN apk add --no-cache git sqlite

# 编译应用
RUN go mod download
RUN CGO_ENABLED=1 GOOS=linux go build -o monitor ./cmd/monitor-test
RUN CGO_ENABLED=1 GOOS=linux go build -o api ./cmd/api-test
RUN CGO_ENABLED=1 GOOS=linux go build -o web ./cmd/web-test

FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache ca-certificates sqlite iperf3

WORKDIR /app

# 复制编译好的二进制文件
COPY --from=builder /app/monitor .
COPY --from=builder /app/api .
COPY --from=builder /app/web .

# 复制配置文件和模板
COPY configs/ ./configs/
COPY web/templates/ ./web/templates/

# 创建数据目录
RUN mkdir -p /app/data

# 暴露端口
EXPOSE 8080 8443

# 启动脚本
COPY docker-entrypoint.sh .
RUN chmod +x docker-entrypoint.sh

ENTRYPOINT ["./docker-entrypoint.sh"]
```

### 2. 创建启动脚本

```bash
#!/bin/sh
# docker-entrypoint.sh

set -e

# 初始化数据库
echo "初始化数据库..."
./monitor --once

# 根据环境变量启动不同服务
case "${SERVICE_TYPE:-web}" in
  "api")
    echo "启动API服务器..."
    exec ./api --port ${API_PORT:-8443}
    ;;
  "web")
    echo "启动Web服务器..."
    exec ./web --port ${WEB_PORT:-8080}
    ;;
  "monitor")
    echo "启动监控服务..."
    exec ./monitor --interval ${MONITOR_INTERVAL:-30s}
    ;;
  *)
    echo "启动Web服务器 (默认)..."
    exec ./web --port ${WEB_PORT:-8080}
    ;;
esac
```

### 3. 创建 docker-compose.yml

```yaml
version: '3.8'

services:
  # Web界面服务
  web:
    build: .
    environment:
      - SERVICE_TYPE=web
      - WEB_PORT=8080
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API服务
  api:
    build: .
    environment:
      - SERVICE_TYPE=api
      - API_PORT=8443
    ports:
      - "8443:8443"
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8443/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务
  monitor:
    build: .
    environment:
      - SERVICE_TYPE=monitor
      - MONITOR_INTERVAL=60s
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
    restart: unless-stopped

networks:
  default:
    name: server-monitor
```

### 4. 部署命令

```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 更新服务
docker-compose pull
docker-compose up -d
```

## 🔧 手动部署

### 1. 环境准备

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y golang-go sqlite3 iperf3

# CentOS/RHEL
sudo yum install -y golang sqlite iperf3

# macOS
brew install go sqlite iperf3
```

### 2. 下载和编译

```bash
# 克隆代码
git clone <repository-url>
cd server-monitor

# 安装依赖
go mod download

# 编译所有程序
make build

# 或者单独编译
go build -o bin/monitor ./cmd/monitor-test
go build -o bin/api ./cmd/api-test
go build -o bin/web ./cmd/web-test
```

### 3. 配置文件

```bash
# 复制配置模板
cp configs/config.yaml.example configs/config.yaml

# 编辑配置
vim configs/config.yaml
```

### 4. 创建系统服务

#### systemd 服务文件 (Linux)

```ini
# /etc/systemd/system/server-monitor-web.service
[Unit]
Description=Server Monitor Web Service
After=network.target

[Service]
Type=simple
User=monitor
Group=monitor
WorkingDirectory=/opt/server-monitor
ExecStart=/opt/server-monitor/bin/web --config /opt/server-monitor/configs/config.yaml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```ini
# /etc/systemd/system/server-monitor-api.service
[Unit]
Description=Server Monitor API Service
After=network.target

[Service]
Type=simple
User=monitor
Group=monitor
WorkingDirectory=/opt/server-monitor
ExecStart=/opt/server-monitor/bin/api --config /opt/server-monitor/configs/config.yaml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

#### 启动服务

```bash
# 创建用户
sudo useradd -r -s /bin/false monitor

# 设置权限
sudo chown -R monitor:monitor /opt/server-monitor

# 启用和启动服务
sudo systemctl enable server-monitor-web
sudo systemctl enable server-monitor-api
sudo systemctl start server-monitor-web
sudo systemctl start server-monitor-api

# 查看状态
sudo systemctl status server-monitor-web
sudo systemctl status server-monitor-api
```

## 🌐 Nginx 反向代理

### 配置文件

```nginx
# /etc/nginx/sites-available/server-monitor
server {
    listen 80;
    server_name monitor.example.com;

    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name monitor.example.com;

    # SSL配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;

    # Web界面
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API接口
    location /api/ {
        proxy_pass http://127.0.0.1:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket
    location /ws {
        proxy_pass http://127.0.0.1:8090;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 启用配置

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/server-monitor /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# firewalld (CentOS)
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL/TLS 配置

```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d monitor.example.com
```

### 3. 访问控制

在配置文件中设置：

```yaml
api:
  auth:
    enabled: true
    type: "basic"  # basic, jwt, api_key
    users:
      - username: "admin"
        password: "secure_password"
```

## 📊 监控和日志

### 1. 日志配置

```yaml
log:
  level: "info"
  format: "json"
  output: "file"
  file: "/var/log/server-monitor/app.log"
  max_size: 100
  max_backups: 5
  max_age: 30
```

### 2. 日志轮转

```bash
# /etc/logrotate.d/server-monitor
/var/log/server-monitor/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 monitor monitor
    postrotate
        systemctl reload server-monitor-web
        systemctl reload server-monitor-api
    endscript
}
```

### 3. 健康检查

```bash
# 检查服务状态
curl -f http://localhost:8080/ || exit 1
curl -f http://localhost:8443/api/v1/health || exit 1
```

## 🚀 性能优化

### 1. 数据库优化

```yaml
database:
  max_open_conns: 25
  max_idle_conns: 10
  conn_max_lifetime: "1h"
  enable_wal: true
```

### 2. 缓存配置

```yaml
cache:
  enabled: true
  ttl: "5m"
  cleanup_interval: "10m"
```

### 3. 系统调优

```bash
# 增加文件描述符限制
echo "monitor soft nofile 65536" >> /etc/security/limits.conf
echo "monitor hard nofile 65536" >> /etc/security/limits.conf

# 内核参数优化
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p
```

## 🔄 备份和恢复

### 1. 数据备份

```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/server-monitor"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
cp /opt/server-monitor/data/monitor.db $BACKUP_DIR/monitor_$DATE.db

# 备份配置
cp -r /opt/server-monitor/configs $BACKUP_DIR/configs_$DATE

# 清理旧备份 (保留30天)
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "configs_*" -mtime +30 -exec rm -rf {} \;
```

### 2. 自动备份

```bash
# 添加到 crontab
0 2 * * * /opt/server-monitor/backup.sh
```

## 📞 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo netstat -tlnp | grep :8080
   sudo lsof -i :8080
   ```

2. **权限问题**
   ```bash
   sudo chown -R monitor:monitor /opt/server-monitor
   sudo chmod +x /opt/server-monitor/bin/*
   ```

3. **数据库锁定**
   ```bash
   sudo systemctl stop server-monitor-*
   rm /opt/server-monitor/data/monitor.db-wal
   sudo systemctl start server-monitor-*
   ```

4. **内存不足**
   ```bash
   # 添加交换空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### 日志查看

```bash
# 系统日志
sudo journalctl -u server-monitor-web -f
sudo journalctl -u server-monitor-api -f

# 应用日志
tail -f /var/log/server-monitor/app.log
```

## 📈 扩展部署

### 高可用部署

1. **负载均衡**: 使用 Nginx 或 HAProxy
2. **数据库集群**: SQLite 集群或迁移到 PostgreSQL
3. **容器编排**: 使用 Kubernetes 或 Docker Swarm
4. **监控告警**: 集成 Prometheus + Grafana

### 多节点部署

```yaml
# docker-compose.ha.yml
version: '3.8'

services:
  web1:
    build: .
    environment:
      - SERVICE_TYPE=web
    deploy:
      replicas: 2

  api1:
    build: .
    environment:
      - SERVICE_TYPE=api
    deploy:
      replicas: 2

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - web1
      - api1
```

这个部署指南涵盖了从开发环境到生产环境的完整部署流程，确保系统能够稳定、安全、高效地运行。
